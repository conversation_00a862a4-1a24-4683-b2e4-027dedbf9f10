<?php
// login.php - Updated to handle social login for existing users
// Replace your existing login.php with this content

// Start output buffering to prevent any unexpected output
ob_start();

// Configure error reporting to prevent HTML output in JSON responses
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
ini_set('log_errors', 1);

// Set timezone at the beginning
date_default_timezone_set('Asia/Manila');

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config/db_connect.php';
require_once 'log_login_history.php'; // Include login history functions

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST method is allowed');
    }

    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!$data) {
        throw new Exception('Invalid JSON input');
    }

    $email = $data['email'] ?? '';
    $password = $data['password'] ?? '';
    $device_info = $data['device_info'] ?? null;
    $latitude = $data['latitude'] ?? null;
    $longitude = $data['longitude'] ?? null;
    
    // Social login fields
    $social_login = $data['social_login'] ?? false;
    $firebase_uid = $data['firebase_uid'] ?? null;

    if (empty($email) || empty($password)) {
        ob_clean();
        echo json_encode(["success" => false, "message" => "Email and password are required"]);
        exit;
    }

    // For social login, try to find user by email first, then update with social info if needed
    if ($social_login && $firebase_uid) {
        // First, check if user exists by email - ADD two_factor_enabled to SELECT
        $stmt = $conn->prepare("SELECT id, password, full_name, role, is_verified, profile_picture_url, total_rating_sum, total_rating_count, contact_number, address_details, latitude, longitude, is_available, firebase_uid, google_id, facebook_id, login_method, two_factor_enabled FROM users WHERE email = ?");
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $stmt->store_result();

        if ($stmt->num_rows > 0) {
            $stmt->bind_result($user_id, $hashed_password, $full_name, $role, $is_verified, $profile_picture_url, $total_rating_sum, $total_rating_count, $contact_number, $address_details, $latitude, $longitude, $is_available, $user_firebase_uid, $user_google_id, $user_facebook_id, $user_login_method, $two_factor_enabled);
            $stmt->fetch();
            $stmt->close();

            // Check if this is a social user or needs to be updated with social info
            $auth_success = false;
            
            if ($user_firebase_uid === $firebase_uid || $user_google_id === $firebase_uid || $user_facebook_id === $firebase_uid) {
                // User already has social login set up
                $auth_success = true;
            } else if (password_verify($password, $hashed_password) || strpos($password, 'SOCIAL_LOGIN_') === 0) {
                // User exists but doesn't have social login configured yet
                // Update user with social login information
                $provider = strpos($password, 'SOCIAL_LOGIN_') === 0 ? 'facebook' : 'google'; // Determine provider

                $update_stmt = $conn->prepare("UPDATE users SET firebase_uid = ?, facebook_id = ?, login_method = ?, auth_provider = ?, last_login = NOW() WHERE id = ?");
                $facebook_id = ($provider === 'facebook') ? $firebase_uid : $user_facebook_id;
                $update_stmt->bind_param("ssssi", $firebase_uid, $facebook_id, $provider, $provider, $user_id);

                if ($update_stmt->execute()) {
                    $auth_success = true;
                } else {
                    error_log('Failed to update user with social info: ' . $update_stmt->error);
                }
                $update_stmt->close();
            }

            if ($auth_success) {
                // Check if 2FA is enabled and not being skipped
                if ($two_factor_enabled && !isset($data['skip_2fa_check'])) {
                    // Just return requires_2fa response without sending OTP
                    // OTP will be sent by the frontend when the dialog opens
                    echo json_encode([
                        "success" => false,
                        "requires_2fa" => true,
                        "message" => "2FA verification required",
                        "email" => $email,
                        "password" => $password
                    ]);
                    exit;
                }
                
                // Set user as logged in
                $update_login_status = $conn->prepare("UPDATE users SET is_logged_in = 1, last_login = NOW() WHERE id = ?");
                $update_login_status->bind_param("i", $user_id);
                $update_login_status->execute();
                $update_login_status->close();

                // Log device info if provided
                if ($device_info) {
                    // LOGIN HISTORY LOGGING
                    try {
                        // Get user's IP address
                        $ip_address = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
                        if (strpos($ip_address, ',') !== false) {
                            $ip_address = trim(explode(',', $ip_address)[0]);
                        }
                        
                        // Use GPS-based login history logging (same as dashboard)
                        logLoginHistory($conn, $user_id, null, $device_info, $ip_address, $latitude, $longitude);
                    } catch (Exception $e) {
                        error_log("Login history logging error: " . $e->getMessage());
                    }
                }

                // Clean output buffer before sending JSON response
                ob_clean();
                echo json_encode([
                    "success" => true,
                    "message" => "Social login successful",
                    "user" => [
                        "id" => (int)$user_id,
                        "full_name" => $full_name,
                        "email" => $email,
                        "role" => $role,
                        "profile_picture_url" => $profile_picture_url,
                        "is_verified" => (bool)$is_verified,
                        "is_available" => (bool)$is_available,
                        "total_rating_sum" => (float)$total_rating_sum,
                        "total_rating_count" => (int)$total_rating_count,
                        "contact_number" => $contact_number,
                        "address_details" => $address_details,
                        "latitude" => (float)$latitude,
                        "longitude" => (float)$longitude
                    ]
                ]);
                exit;
            } else {
                echo json_encode(["success" => false, "message" => "Invalid social login credentials."]);
                exit;
            }
        } else {
            echo json_encode(["success" => false, "message" => "User not found for social login."]);
            exit;
        }
    } else {
        // Regular email/password login - ADD two_factor_enabled to SELECT
        $stmt = $conn->prepare("SELECT id, password, full_name, role, is_verified, profile_picture_url, total_rating_sum, total_rating_count, contact_number, address_details, latitude, longitude, is_available, two_factor_enabled FROM users WHERE email = ?");
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $stmt->store_result();

        if ($stmt->num_rows > 0) {
            $stmt->bind_result($user_id, $hashed_password, $full_name, $role, $is_verified, $profile_picture_url, $total_rating_sum, $total_rating_count, $contact_number, $address_details, $latitude, $longitude, $is_available, $two_factor_enabled);
            $stmt->fetch();
            $stmt->close();

            if (password_verify($password, $hashed_password)) {
                // Check if user is verified before allowing login
                if (!$is_verified) {
                    echo json_encode([
                        "success" => false, 
                        "message" => "Please verify your email first.",
                        "redirect_to" => "email_verification",
                        "user_email" => $email
                    ]);
                    exit;
                }
                
                // Check if 2FA is enabled and not being skipped
                if ($two_factor_enabled && !isset($data['skip_2fa_check'])) {
                    // Just return requires_2fa response without sending OTP
                    // OTP will be sent by the frontend when the dialog opens
                    echo json_encode([
                        "success" => false,
                        "requires_2fa" => true,
                        "message" => "2FA verification required",
                        "email" => $email,
                        "password" => $password
                    ]);
                    exit;
                }
                
                // Only update last_login, NOT is_logged_in
                $update_last_login = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                $update_last_login->bind_param("i", $user_id);
                $update_last_login->execute();
                $update_last_login->close();
                
                // Set user as logged in - FIX: Declare the variable properly
                // In the social login section (around line 72), remove this block:
                // Set user as logged in
                // $update_login_status = $conn->prepare("UPDATE users SET is_logged_in = 1, last_login = NOW() WHERE id = ?");
                // $update_login_status->bind_param("i", $user_id);
                // $update_login_status->execute();
                // $update_login_status->close();
                
                // Replace with just the last_login update:
                $update_last_login = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                $update_last_login->bind_param("i", $user_id);
                $update_last_login->execute();
                $update_last_login->close();
                
                // LOGIN HISTORY LOGGING
                try {
                    // Get user's IP address
                    $ip_address = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
                    if (strpos($ip_address, ',') !== false) {
                        $ip_address = trim(explode(',', $ip_address)[0]);
                    }
                    
                    // Get device info from the request data (not $_POST since we're using JSON)
                    $device_info_log = $device_info ?? $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
                    
                    // Use GPS-based login history logging (same as dashboard)
                    logLoginHistory($conn, $user_id, null, $device_info_log, $ip_address, $latitude, $longitude);
                } catch (Exception $e) {
                    error_log("Login history logging error: " . $e->getMessage());
                }

                // Clean output buffer before sending JSON response
                ob_clean();
                echo json_encode([
                    "success" => true,
                    "message" => "Login successful",
                    "user" => [
                        "id" => (int)$user_id,
                        "full_name" => $full_name,
                        "email" => $email,
                        "role" => $role,
                        "profile_picture_url" => $profile_picture_url,
                        "is_verified" => (bool)$is_verified,
                        "is_available" => (bool)$is_available,
                        "total_rating_sum" => (float)$total_rating_sum,
                        "total_rating_count" => (int)$total_rating_count,
                        "contact_number" => $contact_number,
                        "address_details" => $address_details,
                        "latitude" => (float)$latitude,
                        "longitude" => (float)$longitude
                    ]
                ]);
            } else {
                echo json_encode(["success" => false, "message" => "Invalid credentials."]);
            }
        } else {
            echo json_encode(["success" => false, "message" => "User not found."]);
        }
    }
} catch (Exception $e) {
    error_log("login.php error: " . $e->getMessage());
    // Clean output buffer before sending JSON response
    ob_clean();
    echo json_encode([
        "success" => false,
        "message" => $e->getMessage()
    ]);
}

$conn->close();
?>
