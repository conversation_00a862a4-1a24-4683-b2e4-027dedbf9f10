import 'package:flutter/material.dart';
import 'package:hanapp/services/app_lifecycle_service.dart';

void main() {
  runApp(StatusUpdateTestApp());
}

class StatusUpdateTestApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Status Update Test',
      home: StatusUpdateTestScreen(),
    );
  }
}

class StatusUpdateTestScreen extends StatefulWidget {
  @override
  _StatusUpdateTestScreenState createState() => _StatusUpdateTestScreenState();
}

class _StatusUpdateTestScreenState extends State<StatusUpdateTestScreen> {
  String _status = 'Unknown';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeService();
  }

  Future<void> _initializeService() async {
    try {
      await AppLifecycleService.instance.initialize();
      setState(() {
        _status = 'Service initialized';
      });
    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
    }
  }

  Future<void> _testOnlineStatus() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await AppLifecycleService.instance.testUpdateStatus(true);
      setState(() {
        _status = 'Online status test completed';
      });
    } catch (e) {
      setState(() {
        _status = 'Error testing online status: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testOfflineStatus() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await AppLifecycleService.instance.testUpdateStatus(false);
      setState(() {
        _status = 'Offline status test completed';
      });
    } catch (e) {
      setState(() {
        _status = 'Error testing offline status: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Status Update Test'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Status:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text(_status),
                    SizedBox(height: 16),
                    if (_isLoading)
                      Center(child: CircularProgressIndicator())
                    else
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton(
                              onPressed: _testOnlineStatus,
                              child: Text('Test Online'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                              ),
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: _testOfflineStatus,
                              child: Text('Test Offline'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red,
                              ),
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Service Info:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text('Is Doer: ${AppLifecycleService.instance.isDoer}'),
                    Text('Is Online: ${AppLifecycleService.instance.isOnline}'),
                    Text('Status Description: ${AppLifecycleService.instance.statusDescription}'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 