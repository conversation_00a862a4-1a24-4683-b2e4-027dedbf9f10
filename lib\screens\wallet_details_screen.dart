import 'package:flutter/material.dart';
import 'package:hanapp/models/user.dart';
import 'package:hanapp/models/transaction.dart';
import 'package:hanapp/services/wallet_service.dart';
import 'package:hanapp/utils/auth_service.dart';
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:intl/intl.dart';

class WalletDetailsScreen extends StatefulWidget {
  const WalletDetailsScreen({super.key});

  @override
  State<WalletDetailsScreen> createState() => _WalletDetailsScreenState();
}

class _WalletDetailsScreenState extends State<WalletDetailsScreen> with WidgetsBindingObserver {
  User? _currentUser;
  double _balance = 0.00;
  List<Transaction> _transactions = [];
  bool _isLoading = true;
  String? _errorMessage;
  String? _selectedCashInMethod;
  String? _selectedBank;

  final WalletService _walletService = WalletService();
  final TextEditingController _amountController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this); // Observe app lifecycle
    _initializeWalletDetailsScreen();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this); // Remove observer
    _amountController.dispose();
    super.dispose();
  }

  // --- App Lifecycle Management ---
  // This is useful if a user completes a payment in BecauseScreen,
  // then navigates to this WalletDetailsScreen, it will refresh the data.
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      debugPrint('App resumed: Refreshing wallet details...');
      _initializeWalletDetailsScreen(); // Re-fetch all data
    }
  }

  Future<void> _initializeWalletDetailsScreen() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    _currentUser = await AuthService.getUser();
    if (_currentUser == null || _currentUser!.id == null) {
      _errorMessage = 'User not logged in. Please log in to view wallet details.';
      setState(() {
        _isLoading = false;
      });
      return;
    }

    // Fetch both balance and transaction history concurrently
    await Future.wait([
      _fetchBalance(),
      _fetchTransactionHistory(),
    ]);

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _fetchBalance() async {
    if (_currentUser == null || _currentUser!.id == null) return;
    final response = await _walletService.getWalletBalance(userId: _currentUser!.id!);
    if (response['success']) {
      setState(() {
        _balance = response['balance'];
      });
    } else {
      _errorMessage = response['message'] ?? 'Failed to load balance.';
      _showSnackBar(_errorMessage!, isError: true);
    }
  }

  Future<void> _fetchTransactionHistory() async {
    if (_currentUser == null || _currentUser!.id == null) return;
    final response = await _walletService.getTransactionHistory(userId: _currentUser!.id!);
    if (response['success']) {
      setState(() {
        _transactions = response['transactions'];
      });
    } else {
      _errorMessage = response['message'] ?? 'Failed to load transaction history.';
      _showSnackBar(_errorMessage!, isError: true);
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isError ? Icons.error_outline : Icons.check_circle_outline,
              color: Colors.white,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: isError ? Colors.red : Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(10),
      ),
    );
  }

  void _cashIn() {
    // Validate payment method selection
    if (_selectedCashInMethod == null) {
      _showSnackBar('Please select a cash-in method.', isError: true);
      return;
    }

    if (_selectedCashInMethod == 'Bank Transfer' && _selectedBank == null) {
      _showSnackBar('Please select a bank for bank transfer.', isError: true);
      return;
    }

    // Validate amount
    final String amountText = _amountController.text.trim();
    if (amountText.isEmpty) {
      _showSnackBar('Please enter an amount.', isError: true);
      return;
    }

    final double? amount = double.tryParse(amountText);
    if (amount == null || amount <= 0) {
      _showSnackBar('Please enter a valid positive amount.', isError: true);
      return;
    }

    if (amount < 200) {
      _showSnackBar('Minimum cash-in amount is ₱200.', isError: true);
      return;
    }

    // For now, just show a placeholder message without backend integration
    String method = _selectedCashInMethod!;
    if (_selectedCashInMethod == 'Bank Transfer' && _selectedBank != null) {
      // Get bank name from the selected bank ID
      final workingBanks = [
        {'id': 'bpi', 'name': 'BPI Direct Debit'},
        {'id': 'chinabank', 'name': 'China Bank Direct Debit'},
        {'id': 'rcbc', 'name': 'RCBC Direct Debit'},
        {'id': 'unionbank', 'name': 'UBP Direct Debit'},
      ];
      final selectedBankName = workingBanks.firstWhere(
        (bank) => bank['id'] == _selectedBank,
        orElse: () => {'name': _selectedBank!},
      )['name'];
      method = '$_selectedCashInMethod ($selectedBankName)';
    }

    // Show success message and clear form
    _showSnackBar('Cash in request for ₱${amount.toStringAsFixed(2)} via $method has been submitted. Backend integration will be added soon.');
    _amountController.clear();
    setState(() {
      _selectedCashInMethod = null;
      _selectedBank = null;
    });
  }



  Widget _buildBankTransferOption() {
    final isSelected = _selectedCashInMethod == 'Bank Transfer';

    // List of working banks
    final workingBanks = [
      {'id': 'bpi', 'name': 'BPI Direct Debit', 'icon': '🏦'},
      {'id': 'chinabank', 'name': 'China Bank Direct Debit', 'icon': '🏦'},
      {'id': 'rcbc', 'name': 'RCBC Direct Debit', 'icon': '🏦'},
      {'id': 'unionbank', 'name': 'UBP Direct Debit', 'icon': '🏦'},
    ];

    return Column(
      children: [
        // Bank Transfer Radio Button
        RadioListTile<String>(
          title: const Text('Bank Transfer'),
          value: 'Bank Transfer',
          groupValue: _selectedCashInMethod,
          contentPadding: EdgeInsets.zero,
          onChanged: (String? value) {
            setState(() {
              _selectedCashInMethod = value;
            });
          },
        ),

        // Bank Dropdown (shown when Bank Transfer is selected)
        if (isSelected)
          Container(
            margin: const EdgeInsets.only(left: 32, right: 0, bottom: 8),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                isExpanded: true,
                value: _selectedBank,
                hint: const Text('Select your bank'),
                items: workingBanks.map((bank) {
                  return DropdownMenuItem<String>(
                    value: bank['id'] as String,
                    child: Row(
                      children: [
                        Text(bank['icon'] as String, style: const TextStyle(fontSize: 20)),
                        const SizedBox(width: 12),
                        Expanded(child: Text(bank['name'] as String)),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (String? value) {
                  setState(() {
                    _selectedBank = value;
                  });
                },
              ),
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tapp Balance'), // Title specific to this screen
        backgroundColor: Constants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
          ? Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, color: Colors.red.shade400, size: 50),
              const SizedBox(height: 10),
              Text(
                _errorMessage!,
                textAlign: TextAlign.center,
                style: const TextStyle(color: Colors.red, fontSize: 16),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: _initializeWalletDetailsScreen,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Constants.primaryColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                ),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      )
          : SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Balance Section - Updated to match image design
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Balance',
                    style: TextStyle(fontSize: 16, color: Colors.black, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '₱${NumberFormat('#,##0.00').format(_balance)}',
                    style: const TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Cash in Method Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Cash in Method',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Colors.black),
                  ),
                  const SizedBox(height: 16),

                  // Amount Input Field
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: TextField(
                      controller: _amountController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        hintText: 'Enter amount (minimum ₱200)',
                        border: InputBorder.none,
                        prefixText: '₱ ',
                        prefixStyle: TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),

                  // GCash E-Wallet Radio Button
                  RadioListTile<String>(
                    title: const Text('GCash E-Wallet'),
                    value: 'GCash E-Wallet',
                    groupValue: _selectedCashInMethod,
                    contentPadding: EdgeInsets.zero,
                    onChanged: (String? value) {
                      setState(() {
                        _selectedCashInMethod = value;
                        _selectedBank = null; // Clear bank selection
                      });
                    },
                  ),

                  // GrabPay E-Wallet Radio Button
                  RadioListTile<String>(
                    title: const Text('GrabPay E-Wallet'),
                    value: 'GrabPay E-Wallet',
                    groupValue: _selectedCashInMethod,
                    contentPadding: EdgeInsets.zero,
                    onChanged: (String? value) {
                      setState(() {
                        _selectedCashInMethod = value;
                        _selectedBank = null; // Clear bank selection
                      });
                    },
                  ),

                  // Maya E-Wallet Radio Button
                  RadioListTile<String>(
                    title: const Text('Maya E-Wallet'),
                    value: 'Maya E-Wallet',
                    groupValue: _selectedCashInMethod,
                    contentPadding: EdgeInsets.zero,
                    onChanged: (String? value) {
                      setState(() {
                        _selectedCashInMethod = value;
                        _selectedBank = null; // Clear bank selection
                      });
                    },
                  ),

                  // Credit/Debit Card Radio Button
                  RadioListTile<String>(
                    title: const Text('Credit/Debit Card'),
                    value: 'Credit/Debit Card',
                    groupValue: _selectedCashInMethod,
                    contentPadding: EdgeInsets.zero,
                    onChanged: (String? value) {
                      setState(() {
                        _selectedCashInMethod = value;
                        _selectedBank = null; // Clear bank selection
                      });
                    },
                  ),

                  // Bank Transfer with Dropdown (Last/Bottom)
                  _buildBankTransferOption(),

                  const SizedBox(height: 16),

                  // Helper text
                  const Text(
                    'Select your preferred withdrawal method to proceed.',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),

                  const SizedBox(height: 20),

                  // Cash in Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _cashIn,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF4285F4), // Blue color like in image
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text(
                        'Cash in',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Transaction History Section
            const Text(
              'Transaction History',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Colors.black),
            ),
            const SizedBox(height: 16),
            _transactions.isEmpty
                ? Center(
              child: Text(
                'No transactions yet.',
                style: TextStyle(fontSize: 16, color: Colors.grey.shade700),
              ),
            )
                : ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _transactions.length,
              itemBuilder: (context, index) {
                final transaction = _transactions[index];
                Color statusColor;
                String statusText = transaction.status;

                switch (transaction.status.toLowerCase()) {
                  case 'completed':
                  case 'settled':
                    statusColor = Colors.green.shade700;
                    statusText = 'Completed';
                    break;
                  case 'pending':
                    statusColor = Colors.orange.shade700;
                    break;
                  case 'in_process':
                    statusColor = Colors.blue.shade700;
                    statusText = 'In Process';
                    break;
                  case 'failed':
                    statusColor = Colors.red.shade700;
                    break;
                  case 'cancelled':
                    statusColor = Colors.grey.shade700;
                    break;
                  case 'expired':
                    statusColor = Colors.purple.shade700;
                    break;
                  default:
                    statusColor = Colors.black54;
                    break;
                }

                return Card(
                  margin: const EdgeInsets.only(bottom: 12.0),
                  elevation: 1,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        Icon(
                          transaction.type == 'cash_in'
                              ? Icons.add_circle_outline
                              : transaction.type == 'withdrawal'
                              ? Icons.remove_circle_outline
                              : Icons.info_outline,
                          color: transaction.type == 'cash_in' ? Colors.green : Colors.red,
                          size: 30,
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                transaction.description ?? '${transaction.type.replaceAll('_', ' ')} via ${transaction.method ?? 'N/A'}',
                                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                DateFormat('MMM dd, yyyy - hh:mm a').format(transaction.transactionDate),
                                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                              ),
                              if (transaction.xenditInvoiceId != null && transaction.xenditInvoiceId!.isNotEmpty)
                                Text(
                                  'Xendit Ref: ${transaction.xenditInvoiceId}',
                                  style: TextStyle(fontSize: 10, color: Colors.blueGrey.shade400),
                                ),
                            ],
                          ),
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              '₱${transaction.amount.toStringAsFixed(2)}',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: transaction.type == 'cash_in' ? Colors.green.shade700 : Colors.red.shade700,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              statusText,
                              style: TextStyle(fontSize: 14, color: statusColor, fontWeight: FontWeight.w600),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
