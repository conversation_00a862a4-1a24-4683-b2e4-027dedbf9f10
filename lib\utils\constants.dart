// lib/utils/constants.dart
import 'package:flutter/material.dart';

const Color primaryColor = Color(0xFF141CC9); // A deep blue
const Color accentColor = Color(0xFFFDD835); // A yellow/gold (if used)
const Color lightGreyColor = Color(0xFFE0E0E0);
const Color textColor = Colors.black87; // For general text
const Color buttonTextColor = Colors.white; // For text on primary buttons
const Color socialButtonBorderColor = primaryColor;
const Color socialButtonTextColor = Colors.black;
const Color secondaryColor = Color(0xFFFFC107);

const EdgeInsets screenPadding = EdgeInsets.symmetric(horizontal: 24.0, vertical: 32.0);

const String xenditPublicKey = 'YOUR_XENDIT_PUBLIC_KEY_HERE';

const String googleWebClientId = "65987075200-5sugdap43ooqp7fan2d5airbqicu883b.apps.googleusercontent.com"; // Get this from Google Cloud Console / Firebase Project Settings
const String facebookAppId = "943661427861810"; // HanApp Facebook App ID (must match Android strings.xml)