<?php
// test_hanapp_balance_payment.php
// Test script for HanApp Balance payment endpoint

// Test data
$testData = [
    'lister_id' => 87, // Replace with actual lister ID
    'application_id' => 123, // Replace with actual application ID
    'doer_fee' => 500.00,
    'transaction_fee' => 25.00,
    'total_amount' => 525.00
];

echo "Testing HanApp Balance Payment Endpoint\n";
echo "======================================\n\n";

echo "Test Data:\n";
echo "- Lister ID: " . $testData['lister_id'] . "\n";
echo "- Application ID: " . $testData['application_id'] . "\n";
echo "- Doer Fee: ₱" . number_format($testData['doer_fee'], 2) . "\n";
echo "- Platform Fee: ₱" . number_format($testData['transaction_fee'], 2) . "\n";
echo "- Total Amount: ₱" . number_format($testData['total_amount'], 2) . "\n\n";

// Initialize cURL
$ch = curl_init();

// Set cURL options
curl_setopt($ch, CURLOPT_URL, 'https://autosell.io/api/wallet/process_hanapp_balance_payment.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen(json_encode($testData))
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

echo "Sending request to endpoint...\n";

// Execute request
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);

curl_close($ch);

echo "Response received!\n";
echo "HTTP Code: " . $httpCode . "\n";

if ($error) {
    echo "cURL Error: " . $error . "\n";
} else {
    echo "Raw Response:\n";
    echo $response . "\n\n";
    
    // Try to decode JSON
    $decodedResponse = json_decode($response, true);
    
    if ($decodedResponse) {
        echo "Parsed Response:\n";
        echo "- Success: " . ($decodedResponse['success'] ? 'true' : 'false') . "\n";
        echo "- Message: " . ($decodedResponse['message'] ?? 'N/A') . "\n";
        
        if ($decodedResponse['success']) {
            echo "- Transaction ID: " . ($decodedResponse['transaction_id'] ?? 'N/A') . "\n";
            echo "- New Lister Balance: ₱" . number_format($decodedResponse['new_lister_balance'] ?? 0, 2) . "\n";
            echo "- New Doer Profit: ₱" . number_format($decodedResponse['new_doer_profit'] ?? 0, 2) . "\n";
            echo "- Job Title: " . ($decodedResponse['job_title'] ?? 'N/A') . "\n";
            echo "- Status: " . ($decodedResponse['status'] ?? 'N/A') . "\n";
        }
    } else {
        echo "Failed to parse JSON response\n";
        echo "JSON Error: " . json_last_error_msg() . "\n";
    }
}

echo "\nTest completed.\n";
?>
