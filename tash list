*   [33m85def5e[m[33m ([m[1;35mrefs/stash[m[33m)[m On main: Location maps functionality
[31m|[m[32m\[m  
[31m|[m * [33mf2b7e91[m index on main: ca253a8 Finish 63,64,56,57,58
[31m|[m[31m/[m  
[31m|[m * [33m1afff34[m[33m ([m[1;31morigin/main[m[33m, [m[1;31morigin/HEAD[m[33m)[m Merge branch 'main' of https://github.com/DEB-Company/hanapp
[31m|[m[31m/[m[34m|[m 
[31m|[m *   [33m605ce22[m chat screen and live location new
[31m|[m [35m|[m[36m\[m  
[31m|[m * [36m|[m [33m16bda48[m chat screen and live location update
* [36m|[m [36m|[m [33mca253a8[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32mcheckpoint[m[33m, [m[1;32mmain[m[33m)[m Finish 63,64,56,57,58
[36m|[m [36m|[m[36m/[m  
[36m|[m[36m/[m[36m|[m   
* [36m|[m [33m863a505[m Fixed confirm button design add animation, fixed review design as per the example, remove pay button in the chats screen
* [36m|[m   [33mcf2ef48[m Merge branch 'main' of https://github.com/DEB-Company/hanapp
[1;31m|[m[1;32m\[m [36m\[m  
[1;31m|[m * [36m|[m [33mfc40989[m fee text
[1;31m|[m [36m|[m[36m/[m  
* [36m/[m [33mc4f5484[m General Implementation of add tip, accept notification in doer, and enhance searching in asap cretion
[36m|[m[36m/[m  
* [33m9450c02[m Fixed dashboard design, fixed autoplay video, removed Login Successful snackbar, fixed overflow I Understand Text in Asap Listing Warning prompt, added use current location button in asap listing creation, public onsite and hybrid
* [33m25826de[m Fix ios file selector error
* [33m55443f3[m Fixed notifications black screen when popup is clicked, fix open file in ios
* [33m319d98a[m[33m ([m[1;31morigin/fix[m[33m)[m Fixed Notifications routes, correct time stamps in chats screen, chat listm notification screen, and combined listing screen
* [33m45750e0[m Fix notifications, as well as remove all the limits to just cavite
*   [33m8c991a7[m Merge branch 'main' of https://github.com/DEB-Company/hanapp
[1;33m|[m[1;34m\[m  
[1;33m|[m * [33m2606c86[m Add files via upload
* [1;34m|[m [33ma0bef42[m Fix verification on registering and login verification
[1;34m|[m[1;34m/[m  
* [33m7763e55[m all done
* [33mda878d8[m unfinished
*   [33ma34c62b[m Merge branch 'main' of https://github.com/DEB-Company/hanapp
[1;35m|[m[1;36m\[m  
[1;35m|[m * [33mb28a4cd[m asap listing updated
[1;35m|[m * [33mdd51e8d[m updated face scan + modal after face scan
* [1;36m|[m [33m4509d56[m Proper logout function, login history fixed, update password with otp implemented, and 2 factor authentication implemented
[1;36m|[m[1;36m/[m  
* [33m0629a46[m Fixed Default pages on login, and audio and file messages in chat
* [33mb819638[m fixed google/facebook login and register
* [33m5748e4f[m tax fee added in payments
* [33mcad8753[m Fixed merged conlicts
*   [33m8f77101[m Update backend API endpoints
[31m|[m[32m\[m  
[31m|[m * [33m6786f27[m Initial implementation of audio sending
[31m|[m * [33m2743dc3[m Add files via upload
[31m|[m * [33m8f780d5[m Corrected build gradle
[31m|[m * [33m2e53419[m Corrected implemtation, no errors
[31m|[m * [33m09c0661[m Initial implementation of audio and file sending in chat
[31m|[m * [33m77e2f42[m Improved role changing in dashboard
[31m|[m * [33medfb5eb[m Fixed default page when changing roles
[31m|[m * [33m31e9ee6[m Chanes in Listing Details and Applicants Screen
[31m|[m * [33mc6188c7[m Many Changes
[31m|[m * [33m98bd216[m Notofications And Reviews (Application Details) Changes
[31m|[m *   [33m00ffb8d[m Done Default page of lister and doer- pulled data from origin main
[31m|[m [33m|[m[34m\[m  
[31m|[m [33m|[m * [33m0a4a44a[m verification show uploaded image, get badge fix status, all cash in method working
[31m|[m * [34m|[m [33m190fd75[m Done Default page of lister and doer
* [34m|[m [34m|[m [33m8544b93[m new file upload
[34m|[m [34m|[m[34m/[m  
[34m|[m[34m/[m[34m|[m   
* [34m|[m [33mf30e0e5[m Update cash_out_service.dart
* [34m|[m [33m72a08a7[m Update profile_withdrawal_screen.dart
[34m|[m[34m/[m  
* [33m8293317[m Changes again
* [33m32fda68[m Small changes to message
* [33m17d38d0[m 1 = ok 4 = ok 5 = ok 6 = ok 7 = ok 8 = ok 9 = ok 10 = 11 = 12 = ok 13 = ok 14 = ok 15 = ok still this so far
* [33m819c29d[m Removed uncessarry snackbars, fix email erification issues - and add a function to verify account when loggin in if verification is interrupted
* [33m106c4b3[m Fixed Forgot Password and Verification - Changed the Register location in to the requested format, drop down boxes, completed all the places in the Philippines for future implementation
* [33m0e8fc19[m Return Button in Register Screens
* [33m9caf9a2[m Fix notification click, added changes in cash out and withdrawal screen (dylar)
* [33mf7b9d5d[m Updated profile withdrawal screen
* [33mc0bcdd5[m Add back button to profile tab in both doer and lister dashboards
* [33m6a74e14[m fix vide player and announcement
* [33mc190b23[m feat: Implement sequential video playback and enhance announcement features
* [33md861fc7[m Remove pubspec.lock from tracking and add to .gitignore
[34m|[m * [33mb0778a1[m[33m ([m[1;31morigin/development[m[33m)[m commit title: Partial merge with boss Lance
[34m|[m * [33mc8af316[m feat: Add comprehensive app features and Android compatibility
[34m|[m *   [33m1d74009[m B Merge branch 'development' of https://github.com/DEB-Company/hanapp into development :wq
[34m|[m [36m|[m[34m\[m  
[34m|[m [36m|[m[34m/[m  
[34m|[m[34m/[m[36m|[m   
* [36m|[m [33m88639a7[m[33m ([m[1;32mdevelopment[m[33m)[m Update iOS Podfile to version 14.0 and fix GoogleUtilities dependencies
* [36m|[m   [33m0afb016[m Merge branch 'development' of https://github.com/DEB-Company/hanapp into development
[1;32m|[m[1;33m\[m [36m\[m  
[1;32m|[m * [36m|[m [33mafb739b[m Merged verification and withdrawal features
* [1;33m|[m [36m|[m [33m2cd032f[m Add announcement feature files and fix dashboard issues
* [1;33m|[m [36m|[m [33m71a5b34[m Revert "verification and withdrawal"
* [1;33m|[m [36m|[m [33m67bb054[m Revert "verification and withdrawal"
[1;33m|[m [1;33m|[m * [33mf35827f[m feat: Enhanced payment system with Maya removal and cash-in improvements
[1;33m|[m [1;33m|[m[1;33m/[m  
[1;33m|[m[1;33m/[m[1;33m|[m   
* [1;33m|[m [33me70d74e[m verification and withdrawal
* [1;33m|[m [33m6434d9d[m verification and withdrawal
[1;33m|[m[1;33m/[m  
* [33m2149a8c[m Initial commit: Add hanapp Flutter project with syntax fix
* [33m16f242f[m[33m ([m[1;31morigin/fix-type-casting-error[m[33m)[m will change later
* [33meb23d5e[m feat: merge with boss dylar & reaaply combined rescan/filter button with slider up to 100km, improved ASAP doer search UX, and word filter integration
* [33m830fa4a[m Add word filtering system and modify combined listings to show only pending ASAP listings
* [33m8f44503[m[33m ([m[1;31morigin/master[m[33m)[m feat: add filter by range (1-100km) with slider for doer search, progressive and manual, UI improvements
* [33mb01df45[m[33m ([m[1;31morigin/feature/ui-improvements-xendit-facebook-fixes[m[33m)[m feat: Implement Firebase Facebook authentication
* [33m84fd3df[m[33m ([m[1;31morigin/feature/authentication-payment-integration[m[33m)[m Major update: Enhanced authentication, payment integration, and user verification
* [33mb8feb90[m Fix login history field name mismatch - backend returns 'history' not 'login_history'
* [33mf6f4466[m Enhanced cache-busting for login system to prevent cached responses for deleted accounts
* [33m3ac360b[m Reduce video and WebView logging noise in console
* [33m8925c71[m Remove video debug logs from dashboard screens and video service
* [33mb742703[m Fix const expression error in login screen dialogs
* [33m400de29[m Add banned_until and is_deleted features to login system with popup dialogs
* [33mabd5390[m full doer view profile implementation
* [33m1057aa5[m