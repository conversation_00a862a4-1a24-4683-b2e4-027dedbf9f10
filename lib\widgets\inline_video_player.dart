import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import 'package:hanapp/models/video.dart';

class InlineVideoPlayer extends StatefulWidget {
  final Video video;
  final List<Video>? videos; // Optional list for sequential playback
  final bool autoplay;
  final double height;
  final double width;

  const InlineVideoPlayer({
    super.key,
    required this.video,
    this.videos,
    this.autoplay = false, // Changed from true to false
    this.height = 200,
    this.width = double.infinity,
  });

  @override
  State<InlineVideoPlayer> createState() => InlineVideoPlayerState();
}

class InlineVideoPlayerState extends State<InlineVideoPlayer> {
  VideoPlayerController? _videoController;
  ChewieController? _chewieController;
  YoutubePlayerController? _youtubeController;
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  int _currentVideoIndex = 0;
  List<Video> _videoList = [];
  bool _isTransitioning = false;

  @override
  void initState() {
    super.initState();
    // Initialize video list - use provided list or single video
    _videoList = widget.videos ?? [widget.video];
    _currentVideoIndex = 0;
    _isTransitioning = false;
    _initializeVideo();
  }

  Future<void> _initializeVideo() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      // Get current video from the list
      final currentVideo = _videoList[_currentVideoIndex];

      if (currentVideo.videoUrl == null || currentVideo.videoUrl!.isEmpty) {
        throw Exception('Video URL is not available');
      }

      if (_isYouTubeVideo(currentVideo.videoUrl!)) {
        await _initializeYouTubeVideo();
      } else {
        await _initializeLocalVideo();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Failed to load video: $e';
      });

    }
  }

  bool _isYouTubeVideo(String url) {
    return url.contains('youtube.com') || url.contains('youtu.be');
  }

  String _extractYouTubeId(String url) {
    final regExp = RegExp(
      r'(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})',
      caseSensitive: false,
    );
    final match = regExp.firstMatch(url);
    return match?.group(1) ?? '';
  }

  void _onYouTubeVideoStateChanged() {
    if (_youtubeController != null && 
        _youtubeController!.value.playerState == PlayerState.ended &&
        !_isTransitioning) {
      _playNextVideo();
    }
  }

  void _onVideoPositionChanged() {
    if (_videoController != null && 
        _videoController!.value.isInitialized &&
        _videoController!.value.duration.inMilliseconds > 0 &&
        !_isTransitioning) {
      final position = _videoController!.value.position.inMilliseconds;
      final duration = _videoController!.value.duration.inMilliseconds;
      
      // Check if video is near completion (within 500ms of end)
      if (position >= duration - 500 && position > 0) {
        _playNextVideo();
      }
    }
  }

  void _playNextVideo() async {
    if (_currentVideoIndex < _videoList.length - 1 && !_isTransitioning) {
      _isTransitioning = true;
      
      // Dispose current controllers first
      _disposeControllers();
      
      // Add a small delay to ensure proper cleanup
      await Future.delayed(const Duration(milliseconds: 100));
      
      setState(() {
        _currentVideoIndex++;
        _hasError = false;
        _isLoading = true;
      });
      
      try {
        await _initializeVideo();
        _isTransitioning = false;
      } catch (error) {
        setState(() {
          _hasError = true;
          _errorMessage = 'Failed to load next video: $error';
          _isLoading = false;
        });
        _isTransitioning = false;
      }
    }
  }

  void _disposeControllers() {
    try {
      _youtubeController?.removeListener(_onYouTubeVideoStateChanged);
      _videoController?.removeListener(_onVideoPositionChanged);
      
      _chewieController?.dispose();
      _chewieController = null;
      
      _videoController?.dispose();
      _videoController = null;
      
      _youtubeController?.dispose();
      _youtubeController = null;
    } catch (e) {
      // Ignore disposal errors
      print('Error disposing controllers: $e');
    }
  }

  Future<void> _initializeYouTubeVideo() async {
    final currentVideo = _videoList[_currentVideoIndex];
    final videoId = _extractYouTubeId(currentVideo.videoUrl!);
    if (videoId.isEmpty) {
      throw Exception('Invalid YouTube URL');
    }

    _youtubeController = YoutubePlayerController(
      initialVideoId: videoId,
      flags: YoutubePlayerFlags(
        autoPlay: widget.autoplay,
        mute: false,
        isLive: false,
        forceHD: true,
        enableCaption: true,
        useHybridComposition: true,
      ),
    );

    // Listen for video end to play next video
    _youtubeController!.addListener(_onYouTubeVideoStateChanged);

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _initializeLocalVideo() async {
    final currentVideo = _videoList[_currentVideoIndex];
    _videoController = VideoPlayerController.networkUrl(Uri.parse(currentVideo.videoUrl!));
    
    await _videoController!.initialize();
    
    // Listen for video completion to play next video
    _videoController!.addListener(_onVideoPositionChanged);
    
    _chewieController = ChewieController(
      videoPlayerController: _videoController!,
      autoPlay: widget.autoplay,
      looping: false,
      allowFullScreen: true,
      allowMuting: true,
      showControls: true,
      materialProgressColors: ChewieProgressColors(
        playedColor: Colors.blue,
        handleColor: Colors.blue,
        backgroundColor: Colors.grey,
        bufferedColor: Colors.lightBlue,
      ),
    );

    setState(() {
      _isLoading = false;
    });
  }

  @override
  void dispose() {
    _disposeControllers();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(12),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Column(
          children: [
            // Video progress indicator for multiple videos
            if (_videoList.length > 1)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                color: Colors.black87,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Video ${_currentVideoIndex + 1} of ${_videoList.length}',
                      style: const TextStyle(color: Colors.white, fontSize: 12),
                    ),
                    Expanded(
                      child: Text(
                        _videoList[_currentVideoIndex].title ?? 'Video ${_currentVideoIndex + 1}',
                        style: const TextStyle(color: Colors.white, fontSize: 12),
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
              ),
            // Video content
            Expanded(
              child: _buildVideoContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.white),
            SizedBox(height: 16),
            Text(
              'Loading video...',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, color: Colors.white, size: 48),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: const TextStyle(color: Colors.white),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _initializeVideo,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_youtubeController != null) {
      return YoutubePlayer(
        controller: _youtubeController!,
        showVideoProgressIndicator: true,
        progressIndicatorColor: Colors.blue,
        progressColors: const ProgressBarColors(
          playedColor: Colors.blue,
          handleColor: Colors.blue,
        ),
      );
    }

    if (_chewieController != null) {
      return Chewie(controller: _chewieController!);
    }

    return const Center(
      child: Text(
        'Video not available',
        style: TextStyle(color: Colors.white),
      ),
    );
  }

  void pauseVideo() {
    _videoController?.pause();
    _youtubeController?.pause();
  }
}