<?php
// hanapp_backend/api/wallet/process_withdrawal.php
// Admin API to approve/reject withdrawal requests and update both withdrawal_requests and transactions tables

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Start output buffering to prevent any unexpected output
ob_start();

// Set up error handler to catch fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        ob_clean();
        http_response_code(500);
        echo json_encode([
            "success" => false,
            "message" => "Server error: " . $error['message'],
            "error_code" => "FATAL_ERROR"
        ]);
    }
});

require_once '../../config/db_connect.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    ob_clean();
    http_response_code(200);
    exit();
}

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("Only POST method is allowed.");
    }

    // Log the start of the request
    error_log("Process withdrawal request started at " . date('Y-m-d H:i:s'));

    // Get JSON input
    $rawInput = file_get_contents('php://input');
    error_log("Process withdrawal raw input: " . $rawInput);

    $input = json_decode($rawInput, true);

    if (!$input) {
        throw new Exception("Invalid JSON input. Raw input: " . $rawInput);
    }

    error_log("Process withdrawal parsed input: " . json_encode($input));

    // Validate required fields
    $withdrawalRequestId = $input['withdrawal_request_id'] ?? null;
    $action = $input['action'] ?? null; // 'approve' or 'reject'
    $adminNotes = $input['admin_notes'] ?? '';

    if (empty($withdrawalRequestId) || !is_numeric($withdrawalRequestId)) {
        throw new Exception("Withdrawal request ID is required and must be numeric.");
    }

    if (!in_array($action, ['approve', 'reject'])) {
        throw new Exception("Action must be either 'approve' or 'reject'.");
    }

    $withdrawalRequestId = intval($withdrawalRequestId);

    // Get withdrawal request details
    $stmt = $conn->prepare("
        SELECT wr.*, u.full_name, u.email, u.total_profit
        FROM withdrawal_requests wr
        JOIN users u ON wr.user_id = u.id
        WHERE wr.id = ? AND wr.status = 'pending'
    ");

    if ($stmt === false) {
        throw new Exception("Failed to prepare withdrawal request check statement.");
    }

    $stmt->bind_param("i", $withdrawalRequestId);

    if (!$stmt->execute()) {
        $stmt->close();
        throw new Exception("Failed to execute withdrawal request check.");
    }

    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        $stmt->close();
        throw new Exception("Withdrawal request not found or already processed.");
    }

    $withdrawalRequest = $result->fetch_assoc();
    $stmt->close();

    $userId = $withdrawalRequest['user_id'];
    $amount = floatval($withdrawalRequest['amount']);
    $method = $withdrawalRequest['method'];

    // Start database transaction for atomicity
    $conn->begin_transaction();

    try {
        if ($action === 'approve') {
            // 1. Update withdrawal request status to approved
            $stmt = $conn->prepare("
                UPDATE withdrawal_requests 
                SET status = 'approved', 
                    admin_notes = ?, 
                    processed_date = NOW(),
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");

            if ($stmt === false) {
                throw new Exception("Failed to prepare withdrawal request update statement.");
            }

            $stmt->bind_param("si", $adminNotes, $withdrawalRequestId);

            if (!$stmt->execute()) {
                $stmt->close();
                throw new Exception("Failed to update withdrawal request: " . $stmt->error);
            }

            $stmt->close();

            // 2. Update corresponding transaction status to completed
            $stmt = $conn->prepare("
                UPDATE transactions 
                SET status = 'completed', 
                    updated_at = CURRENT_TIMESTAMP 
                WHERE user_id = ? 
                AND type = 'cash_out' 
                AND amount = ? 
                AND status = 'pending'
                AND description LIKE ?
                ORDER BY transaction_date DESC 
                LIMIT 1
            ");

            if ($stmt === false) {
                throw new Exception("Failed to prepare transaction update statement.");
            }

            $descriptionPattern = "%Request ID: " . $withdrawalRequestId . "%";
            $stmt->bind_param("ids", $userId, $amount, $descriptionPattern);

            if (!$stmt->execute()) {
                $stmt->close();
                throw new Exception("Failed to update transaction: " . $stmt->error);
            }

            $stmt->close();

            $message = "Withdrawal request approved and processed successfully.";
            error_log("Withdrawal approved: Request $withdrawalRequestId, User $userId, Amount $amount");

        } else { // reject
            // 1. Update withdrawal request status to rejected
            $stmt = $conn->prepare("
                UPDATE withdrawal_requests 
                SET status = 'rejected', 
                    admin_notes = ?, 
                    processed_date = NOW(),
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");

            if ($stmt === false) {
                throw new Exception("Failed to prepare withdrawal request update statement.");
            }

            $stmt->bind_param("si", $adminNotes, $withdrawalRequestId);

            if (!$stmt->execute()) {
                $stmt->close();
                throw new Exception("Failed to update withdrawal request: " . $stmt->error);
            }

            $stmt->close();

            // 2. Update corresponding transaction status to failed
            $stmt = $conn->prepare("
                UPDATE transactions 
                SET status = 'failed', 
                    updated_at = CURRENT_TIMESTAMP 
                WHERE user_id = ? 
                AND type = 'cash_out' 
                AND amount = ? 
                AND status = 'pending'
                AND description LIKE ?
                ORDER BY transaction_date DESC 
                LIMIT 1
            ");

            if ($stmt === false) {
                throw new Exception("Failed to prepare transaction update statement.");
            }

            $descriptionPattern = "%Request ID: " . $withdrawalRequestId . "%";
            $stmt->bind_param("ids", $userId, $amount, $descriptionPattern);

            if (!$stmt->execute()) {
                $stmt->close();
                throw new Exception("Failed to update transaction: " . $stmt->error);
            }

            $stmt->close();

            // 3. Refund the amount back to user's total_profit (since it was deducted when request was created)
            $stmt = $conn->prepare("
                UPDATE users 
                SET total_profit = total_profit + ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ");

            if ($stmt === false) {
                throw new Exception("Failed to prepare profit refund statement.");
            }

            $stmt->bind_param("di", $amount, $userId);

            if (!$stmt->execute()) {
                $stmt->close();
                throw new Exception("Failed to refund user profit: " . $stmt->error);
            }

            $stmt->close();

            $message = "Withdrawal request rejected and funds refunded to user.";
            error_log("Withdrawal rejected: Request $withdrawalRequestId, User $userId, Amount $amount refunded");
        }

        // Commit transaction
        $conn->commit();

        // Success response
        ob_clean();
        echo json_encode([
            "success" => true,
            "message" => $message,
            "withdrawal_details" => [
                "withdrawal_request_id" => $withdrawalRequestId,
                "user_id" => $userId,
                "amount" => $amount,
                "method" => $method,
                "action" => $action,
                "admin_notes" => $adminNotes,
            ]
        ]);

    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        throw $e;
    }

} catch (Exception $e) {
    error_log("Process withdrawal Exception: " . $e->getMessage());
    ob_clean();
    echo json_encode([
        "success" => false,
        "message" => $e->getMessage(),
        "error_code" => "PROCESS_WITHDRAWAL_ERROR"
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
