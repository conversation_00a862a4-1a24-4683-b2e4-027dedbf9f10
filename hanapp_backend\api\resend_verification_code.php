<?php
// hanapp_backend/api/resend_verification_code.php
// <PERSON>les resending the email verification code using SendGrid.

require_once 'config/db_connect.php';
require_once 'utils/email_sender.php'; // Include the new email sender utility

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if (!isset($conn) || $conn->connect_error) {
    error_log("Database connection not established in resend_verification_code.php: " . $conn->connect_error);
    echo json_encode(["success" => false, "message" => "Database connection not established."]);
    exit();
}

$data = json_decode(file_get_contents("php://input"), true);
$email = $data['email'] ?? '';

if (empty($email)) {
    echo json_encode(["success" => false, "message" => "Email is required."]);
    exit();
}

// Check if email exists and is not already verified
$stmt = $conn->prepare("SELECT id, full_name, is_verified FROM users WHERE email = ?");
if ($stmt === false) {
    error_log("Failed to prepare select statement: " . $conn->error);
    echo json_encode(["success" => false, "message" => "Internal server error."]);
    exit();
}
$stmt->bind_param("s", $email);
$stmt->execute();
$stmt->store_result();

if ($stmt->num_rows == 0) {
    echo json_encode(["success" => false, "message" => "Email not found."]);
    $stmt->close();
    exit();
}

$stmt->bind_result($userId, $fullName, $isVerified);
$stmt->fetch();
$stmt->close();

if ($isVerified == 1) {
    echo json_encode(["success" => false, "message" => "Email is already verified."]);
    exit();
}

// Generate a new verification code
    $newVerificationCode = str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);
    
    // Generate unique request ID for idempotency
    $requestId = uniqid('resend_otp_', true);

// Update the database with the new code
$updateStmt = $conn->prepare("UPDATE users SET verification_code = ? WHERE id = ?");
if ($updateStmt === false) {
    error_log("Failed to prepare update statement: " . $conn->error);
    echo json_encode(["success" => false, "message" => "Internal server error."]);
    exit();
}
$updateStmt->bind_param("si", $newVerificationCode, $userId);

if ($updateStmt->execute()) {
    $updateStmt->close();

    // Prepare email content
    $emailSubject = 'New TAPP Email Verification Code';
    $emailBodyHtml = '
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background-color: #f9f9f9; }
                .header { background-color: #141CC9; color: white; padding: 15px; text-align: center; border-radius: 8px 8px 0 0; }
                .content { padding: 20px 0; }
                .code { font-size: 24px; font-weight: bold; color: #141CC9; text-align: center; padding: 15px; border: 1px dashed #141CC9; border-radius: 5px; margin: 20px 0; display: inline-block; }
                .footer { text-align: center; font-size: 12px; color: #777; margin-top: 20px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>TAPP Email Verification</h2>
                </div>
                <div class="content">
                    <p>Hi ' . htmlspecialchars($fullName) . ',</p>
                    <p>You requested a new verification code. Please use the following code to verify your email:</p>
                    <div class="code">' . htmlspecialchars($newVerificationCode) . '</div>
                    <p>This code is valid for a limited time. If you did not request this, please ignore this email.</p>
                    <p>Best regards,<br>The TAPP Team</p>
                </div>
                <div class="footer">
                    <p>&copy; ' . date('Y') . ' TAPP. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
    ';
    $emailBodyText = 'Your new TAPP verification code is: ' . $newVerificationCode . '. This code is valid for a limited time. If you did not request this, please ignore this email.';

    // Generate idempotency key and send verification email
    $idempotencyKey = hash('sha256', $requestId);
    $mailResult = sendEmailViaSendGrid(
        $email, 
        $fullName, 
        $emailSubject, 
        $emailBodyHtml, 
        $emailBodyText,
        null,
        [],
        $idempotencyKey
    );

    if ($mailResult['success']) {
        error_log("New verification email sent to: $email via SendGrid.");
        echo json_encode(["success" => true, "message" => "A new verification code has been sent to your email."]);
    } else {
        error_log("Failed to send new verification email via SendGrid: " . $mailResult['message']);
        echo json_encode(["success" => false, "message" => "Failed to send new verification email. Error: " . $mailResult['message']]);
    }

} else {
    error_log("Failed to update verification code: " . $updateStmt->error);
    echo json_encode(["success" => false, "message" => "Failed to generate new verification code."]);
}

$conn->close();
?>
