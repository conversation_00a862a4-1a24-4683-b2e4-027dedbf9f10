import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:hanapp/screens/components/custom_button.dart';
import 'package:hanapp/utils/auth_service.dart';
import 'package:hanapp/models/user.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:hanapp/screens/select_location_on_map_screen.dart';
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:geocoding/geocoding.dart' as geocoding; // Import for geocoding
import 'package:hanapp/utils/image_utils.dart'; // Import ImageUtils
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hanapp/screens/auth/email_verification_screen.dart'; // NEW: Import EmailVerificationScreen
import 'package:hanapp/services/location_sync_service.dart'; // Import LocationSyncService
import 'dart:async'; // Import for StreamSubscription

class EditProfileScreen extends StatefulWidget {
  const EditProfileScreen({super.key});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  final TextEditingController _fullNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _contactNumberController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  // REMOVED: _otpController is now handled by EmailVerificationScreen

  XFile? _pickedXFile;
  final ImagePicker _picker = ImagePicker();
  User? _currentUser;
  bool _isLoading = false;
  double? _latitude;
  double? _longitude;
  String? _mapSelectedAddress; // Stores the address string that came from map selection

  String? _originalEmail; // To store the original email for comparison
  StreamSubscription<LocationData>? _locationSubscription; // Listen to location updates

  @override
  void initState() {
    super.initState();
    _loadCurrentUserAndPopulateFields();
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _emailController.dispose();
    _contactNumberController.dispose();
    _addressController.dispose();
    _locationSubscription?.cancel(); // Cancel location subscription
    super.dispose();
  }

  Future<void> _loadCurrentUserAndPopulateFields() async {
    setState(() {
      _isLoading = true;
    });

    _currentUser = await AuthService.getUser();

    if (_currentUser != null) {
      await _refreshUserFromDatabase();
    } else {
      await AuthService.refreshUser();
      _currentUser = await AuthService.getUser();
      if (_currentUser == null && mounted) {
        Navigator.of(context).pushReplacementNamed('/login');
        return;
      }
    }

    if (_currentUser != null) {
      print('🔍 Edit Profile - Loading user data:');
      print('🔍 Full Name: ${_currentUser!.fullName}');
      print('🔍 Email: ${_currentUser!.email}');
      print('🔍 Contact Number: ${_currentUser!.contactNumber}');
      print('🔍 Address Details: ${_currentUser!.addressDetails}');
      print('🔍 Latitude: ${_currentUser!.latitude}');
      print('🔍 Longitude: ${_currentUser!.longitude}');

      _fullNameController.text = _currentUser!.fullName;
      _emailController.text = _currentUser!.email;
      _contactNumberController.text = _currentUser!.contactNumber ?? '';
      _addressController.text = _currentUser!.addressDetails ?? '';
      _latitude = _currentUser!.latitude;
      _longitude = _currentUser!.longitude;
      _mapSelectedAddress = _currentUser!.addressDetails;
      _originalEmail = _currentUser!.email;

      print('🔍 After setting controller values:');
      print('🔍 Full Name Controller: ${_fullNameController.text}');
      print('🔍 Email Controller: ${_emailController.text}');
      print('🔍 Contact Number Controller: ${_contactNumberController.text}');
      print('🔍 Address Controller: ${_addressController.text}');
    }
    setState(() {
      _isLoading = false;
    });
    
    // Setup location listener after user data is loaded
    _setupLocationListener();
  }

  // Setup location listener to sync with dashboard updates
  void _setupLocationListener() {
    _locationSubscription = LocationSyncService.instance.locationStream.listen((locationData) {
      // Update for both doers and listers when the screen is mounted
      if (mounted && (_currentUser?.role == 'doer' || _currentUser?.role == 'lister')) {
        setState(() {
          _latitude = locationData.latitude;
          _longitude = locationData.longitude;
          _addressController.text = locationData.addressDetails;
          _mapSelectedAddress = locationData.addressDetails;
        });
        print('EditProfile: Location synced from dashboard (${_currentUser?.role}) - ${locationData.addressDetails}');
      }
    });
    
    // Also check if there's already current location data
    final currentLocationData = LocationSyncService.instance.currentLocationData;
    if (currentLocationData != null && (_currentUser?.role == 'doer' || _currentUser?.role == 'lister')) {
      setState(() {
        _latitude = currentLocationData.latitude;
        _longitude = currentLocationData.longitude;
        _addressController.text = currentLocationData.addressDetails;
        _mapSelectedAddress = currentLocationData.addressDetails;
      });
    }
  }

  Future<void> _refreshUserFromDatabase() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? userIdStr = prefs.getString('user_id');

      if (userIdStr != null) {
        final int userId = int.parse(userIdStr);
        final response = await AuthService().getUserProfileById(userId: userId);

        if (response['success'] && response['user'] != null) {
          final freshUser = User.fromJson(response['user']);
          await AuthService.saveUser(freshUser);
          _currentUser = freshUser;
          print('🔍 Successfully refreshed user data from database');
        } else {
          print('🔍 Failed to refresh user data: ${response['message']}');
        }
      }
    } catch (e) {
      print('🔍 Error refreshing user data: $e');
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
      ),
    );
  }

  Future<void> _pickImage() async {
    final XFile? pickedFile = await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      bool isSameImage = false;
      if (_currentUser != null && _currentUser!.profilePictureUrl != null && _currentUser!.profilePictureUrl!.isNotEmpty) {
        final currentPicUrl = _currentUser!.profilePictureUrl!;
        if (currentPicUrl.startsWith('file://')) {
          isSameImage = pickedFile.path == currentPicUrl.replaceFirst('file://', '');
        } else {
          final pickedFileName = pickedFile.path.split('/').last;
          final currentFileName = currentPicUrl.split('/').last;
          isSameImage = pickedFileName == currentFileName;
        }
      }
      if (isSameImage) {
        if (mounted) {
          await showDialog(
            context: context,
            barrierDismissible: true,
            builder: (context) => AlertDialog(
              title: const Text('Same Image Selected'),
              content: const Text('You selected the same profile picture. Please choose a different image if you want to update your profile picture.'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('OK'),
                ),
              ],
            ),
          );
        }
        return;
      }
      setState(() {
        _pickedXFile = pickedFile;
      });
    }
  }

  Future<void> _selectLocationOnMap() async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SelectLocationOnMapScreen(
          initialLatitude: _latitude,
          initialLongitude: _longitude,
          initialAddress: _addressController.text,
        ),
      ),
    ) as Map<String, dynamic>?;

    if (result != null && mounted) {
      setState(() {
        _latitude = result['latitude'];
        _longitude = result['longitude'];
        _addressController.text = result['address'];
        _mapSelectedAddress = result['address'];
      });
    }
  }

  Future<void> _saveChanges() async {
    if (_currentUser == null || _currentUser!.id == null) {
      if (mounted) Navigator.of(context).pushReplacementNamed('/login');
      return;
    }

    if (_fullNameController.text.isEmpty ||
        _emailController.text.isEmpty ||
        _contactNumberController.text.isEmpty ||
        _addressController.text.isEmpty) {
      _showSnackBar('Please fill in all required fields.', isError: true);
      return;
    }
    if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(_emailController.text)) {
      _showSnackBar('Please enter a valid email address.', isError: true);
      return;
    }

    final String newEmail = _emailController.text.trim();
    if (newEmail != _originalEmail) {
      final bool? isVerified = await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => EmailVerificationScreen(
            email: newEmail,
            isForEmailChange: true,
            userId: _currentUser!.id,
            onVerificationSuccess: (verifiedEmail) {
              _performProfileUpdate(verifiedEmail);
            },
          ),
        ),
      );
      if (isVerified == null || !isVerified) {
        _emailController.text = _originalEmail!;
        _showSnackBar('Email change verification cancelled or failed.', isError: true);
      }
      return;
    }

    if (_pickedXFile == null) {
      final bool? continueUpdate = await showDialog<bool>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Profile Picture Not Changed'),
            content: const Text("You didn't change your profile picture. Do you want to continue updating your profile without changing the picture?"),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('No'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Yes'),
              ),
            ],
          );
        },
      );
      if (continueUpdate != true) {
        return;
      }
    }

    await _performProfileUpdate(newEmail);
  }

  Future<void> _performProfileUpdate(String currentEmailToSave) async {
    setState(() { _isLoading = true; });

    if (_addressController.text.isNotEmpty && (_latitude == null || _longitude == null || _addressController.text != _mapSelectedAddress)) {
      try {
        List<geocoding.Location> locations = await geocoding.locationFromAddress(_addressController.text);
        if (locations.isNotEmpty) {
          _latitude = locations.first.latitude;
          _longitude = locations.first.longitude;
          _mapSelectedAddress = _addressController.text;
        } else {
          final bool? proceed = await _showAddressGeocodingWarningDialog();
          if (proceed != true) {
            setState(() { _isLoading = false; });
            return;
          }
        }
      } catch (e) {
        debugPrint('Geocoding error: $e');
        final bool? proceed = await _showAddressGeocodingWarningDialog(error: e.toString());
        if (proceed != true) {
          setState(() { _isLoading = false; });
          return;
        }
      }
    } else if (_addressController.text.isEmpty && (_latitude != null || _longitude != null)) {
      _latitude = null;
      _longitude = null;
      _mapSelectedAddress = null;
    }

    if (_addressController.text.isNotEmpty && (_latitude == null || _longitude == null)) {
      _showSnackBar('Address requires a precise location. Please select on map or enter a more specific address.', isError: true);
      setState(() { _isLoading = false; });
      return;
    }

    try {
      if (_pickedXFile != null) {
        final uploadResponse = await AuthService().uploadProfilePicture(
          _currentUser!.id.toString(),
          _pickedXFile!,
        );

        if (!uploadResponse['success']) {
          throw Exception(uploadResponse['message'] ?? 'Failed to upload profile picture');
        }

        if (uploadResponse['url'] != null && _currentUser != null) {
          _currentUser = _currentUser!.copyWith(profilePictureUrl: uploadResponse['url']);
          await AuthService.saveUser(_currentUser!);
          print('🔧 Updated local user with new profile picture URL: ${uploadResponse['url']}');
        }
      }

      final response = await AuthService.updateUserProfile(
        userId: _currentUser!.id,
        fullName: _fullNameController.text,
        email: currentEmailToSave,
        contactNumber: _contactNumberController.text,
        addressDetails: _addressController.text,
        latitude: _latitude,
        longitude: _longitude,
      );

      setState(() { _isLoading = false; });

      if (response['success']) {
        await AuthService.refreshUser();
        
        // If location data was updated, sync it with LocationSyncService
        if (_latitude != null && _longitude != null && _currentUser != null) {
          try {
            await LocationSyncService.instance.updateLocation(
              userId: _currentUser!.id,
              latitude: _latitude!,
              longitude: _longitude!,
              addressDetails: _addressController.text,
            );
            debugPrint('✅ Location synced with LocationSyncService after profile update');
          } catch (e) {
            debugPrint('⚠️ Failed to sync location with LocationSyncService: $e');
            // Don't show error to user as profile update was successful
          }
        }
        
        if (mounted) {
          _loadCurrentUserAndPopulateFields();
          _showSnackBar('Profile updated successfully!', isError: false);
        }
      } else {
        setState(() { _isLoading = false; });
        _showSnackBar('Failed to update profile: ${response['message']}', isError: true);
      }
    } catch (e) {
      setState(() { _isLoading = false; });
      debugPrint('Error updating profile: $e');
      _showSnackBar('An unexpected error occurred: $e', isError: true);
    }
  }

  Future<bool?> _showAddressGeocodingWarningDialog({String? error}) async {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Address Warning'),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text(
                  'The address you entered could not be precisely located or is ambiguous. '
                      'The app relies on coordinates for some features.',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                if (error != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text('Error: $error', style: const TextStyle(color: Colors.red)),
                  ),
                const SizedBox(height: 16),
                Text(
                  'What would you like to do?',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Select on Map Instead'),
              onPressed: () {
                Navigator.of(dialogContext).pop(false);
                _selectLocationOnMap();
              },
            ),
            TextButton(
              child: const Text('Proceed Without Exact Location'),
              onPressed: () {
                _latitude = null;
                _longitude = null;
                _mapSelectedAddress = null;
                Navigator.of(dialogContext).pop(true);
              },
            ),
            TextButton(
              child: const Text('Cancel Save'),
              onPressed: () {
                _latitude = null;
                _longitude = null;
                _mapSelectedAddress = null;
                Navigator.of(dialogContext).pop(false);
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading && _currentUser == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Edit Profile'),
          backgroundColor: Constants.primaryColor,
          foregroundColor: Colors.white,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    ImageProvider<Object>? imageProvider = ImageUtils.getProfileImageProvider(
      selectedFile: _pickedXFile,
      storedImageUrl: _currentUser!.profilePictureUrl,
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Profile'),
        backgroundColor: Constants.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () async {
              setState(() {
                _isLoading = true;
              });
              await _refreshUserFromDatabase();
              if (_currentUser != null) {
                _fullNameController.text = _currentUser!.fullName;
                _emailController.text = _currentUser!.email;
                _contactNumberController.text = _currentUser!.contactNumber ?? '';
                _addressController.text = _currentUser!.addressDetails ?? '';
                _latitude = _currentUser!.latitude;
                _longitude = _currentUser!.longitude;
                _mapSelectedAddress = _currentUser!.addressDetails;
                _originalEmail = _currentUser!.email;
              }
              setState(() {
                _isLoading = false;
              });
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Stack(
              alignment: Alignment.bottomRight,
              children: [
                CircleAvatar(
                  radius: 60,
                  backgroundColor: Colors.grey[200],
                  backgroundImage: imageProvider,
                  child: (_pickedXFile == null && (_currentUser!.profilePictureUrl == null || _currentUser!.profilePictureUrl!.isEmpty))
                      ? Icon(
                    Icons.person,
                    size: 60,
                    color: Colors.grey[600],
                  )
                      : null,
                  onBackgroundImageError: imageProvider != null ? (exception, stackTrace) {
                    print('Edit Profile: Error loading profile image: $exception');
                  } : null,
                ),
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: GestureDetector(
                    onTap: _pickImage,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Constants.primaryColor,
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 2),
                      ),
                      child: const Icon(
                        Icons.camera_alt,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            TextFormField(
              controller: _fullNameController,
              decoration: const InputDecoration(
                labelText: 'Full Name',
                border: OutlineInputBorder(borderRadius: BorderRadius.all(Radius.circular(8))),
                prefixIcon: Icon(Icons.person),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'Email',
                border: OutlineInputBorder(borderRadius: BorderRadius.all(Radius.circular(8))),
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _contactNumberController,
              decoration: const InputDecoration(
                labelText: 'Contact Number',
                border: OutlineInputBorder(borderRadius: BorderRadius.all(Radius.circular(8))),
                prefixIcon: Icon(Icons.phone),
              ),
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _addressController,
              decoration: InputDecoration(
                labelText: 'Address',
                border: const OutlineInputBorder(borderRadius: BorderRadius.all(Radius.circular(8))),
                prefixIcon: const Icon(Icons.location_on),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.map),
                  onPressed: _selectLocationOnMap,
                ),
              ),
              onChanged: (text) {
                if (text != _mapSelectedAddress) {
                  setState(() {
                    _latitude = null;
                    _longitude = null;
                  });
                }
              },
            ),
            const SizedBox(height: 24),
            _isLoading
                ? const CircularProgressIndicator()
                : SizedBox(
              width: double.infinity,
              child: CustomButton(
                text: 'Save Changes',
                onPressed: _saveChanges,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
