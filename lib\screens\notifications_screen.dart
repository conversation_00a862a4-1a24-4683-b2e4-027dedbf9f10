import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:hanapp/models/notification.dart'; // Ensure this path is correct
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/screens/listing_details_screen.dart';
import '../models/notification_model.dart';
import 'package:hanapp/services/notification_service.dart';
import 'package:hanapp/utils/auth_service.dart';
import 'package:hanapp/services/application_service.dart';
// Add imports for doer screens
import 'package:hanapp/screens/doer/doer_job_listings_mark_screen.dart';
import 'package:hanapp/screens/lister/lister_reviews_screen.dart';
import 'package:hanapp/screens/doer/application_details_screen.dart';
import 'package:hanapp/screens/lister/lister_application_details_screen.dart';
import 'package:hanapp/screens/chat_screen.dart';
import 'package:hanapp/models/doer_job.dart';
import 'package:hanapp/screens/listing_details_screen.dart';
import 'package:hanapp/screens/lister/public_listing_details_screen.dart';
import 'package:hanapp/screens/review_screen.dart';
import 'package:hanapp/screens/lister/combined_listings_screen.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import 'package:hanapp/services/notification_popup_service.dart';

class NotificationsScreen extends StatefulWidget {
  final String userRole;

  const NotificationsScreen({
    Key? key,
    required this.userRole,
  }) : super(key: key);

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  List<NotificationModel> _notifications = [];
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _fetchNotifications();
    // Note: NotificationPopupService is managed by the parent screen (DoerDashboardScreen or DoerJobListingsScreen)
  }
  @override
void dispose() {
  // Note: NotificationPopupService is managed by the parent screen (DoerDashboardScreen or DoerJobListingsScreen)
  super.dispose();
}

  Future<void> _fetchNotifications() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    final user = await AuthService.getUser();
    if (user == null || user.id == null) {
      setState(() {
        _error = 'User not logged in. Please log in to view notifications.';
        _isLoading = false;
      });
      return;
    }
    final int currentUserId = user.id!;



    // Check user role and use appropriate endpoint
    Map<String, dynamic> response;
    if (user.role == 'lister') {
      response = await NotificationService().getNotifications(userId: currentUserId);
    } else {
      response = await NotificationService().getDoerNotifications(userId: currentUserId);
    }



    if (response['success']) {
      setState(() {
        _notifications = response['notifications'];
        _isLoading = false;
      });
    } else {
      setState(() {
        _error = response['message'] ?? 'Failed to load notifications.';
        _isLoading = false;
      });
    }
  }

  void _handleNotificationTap(NotificationModel notification) async {

    final user = await AuthService.getUser();
    if (user == null) return;

    // Mark notification as read based on user role
    if (!notification.isRead) {
      Map<String, dynamic> response;
      if (user.role == 'lister') {
        response = await NotificationService().markNotificationAsRead(notificationId: notification.id);
      } else {
        response = await NotificationService().markDoerNotificationAsRead(notificationId: notification.id);
      }

      if (response['success']) {
        // Update the notification in the list to mark it as read
        setState(() {
          final index = _notifications.indexWhere((n) => n.id == notification.id);
          if (index != -1) {
            _notifications[index] = _notifications[index].copyWith(isRead: true);
          }
        });
      }
    }

    // Handle navigation based on notification type and user role
    switch (notification.type) {
      case 'message':
      case 'message_received':
        // Navigate to chat conversation
        if (notification.conversationIdForChat != null && notification.conversationIdForChat! > 0) {
          Navigator.pushNamed(
            context,
            '/chat_screen',
            arguments: {
              'conversationId': notification.conversationIdForChat,
              'otherUserId': notification.senderId,
              'listingTitle': notification.relatedListingTitle ?? 'Chat',
              'applicationId': notification.associatedId ?? 0,
              'isLister': user.role == 'lister',
            },
          );
        } else {
          // If conversationIdForChat is null or invalid, navigate to chat list instead
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Unable to open chat: conversation not found'),
              backgroundColor: Colors.orange,
            ),
          );
          Navigator.pushNamed(context, '/chat_list');
        }
        break;
      case 'project_started':
      case 'job_started':
        // Navigate to job details or chat
        if (notification.conversationIdForChat != null) {
          Navigator.pushNamed(
            context, 
            '/chat_screen',
            arguments: {
              'conversationId': notification.conversationIdForChat,
              'otherUserId': notification.senderId,
              'listingTitle': 'Project', // Use generic title since we removed it from notifications
              'applicationId': notification.associatedId ?? 0,
              'isLister': user.role == 'lister',
            },
          );
        } else {
          // Navigate to appropriate dashboard
          if (user.role == 'lister') {
            // Navigate to combined listings screen with completed tab
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const CombinedListingsScreen(initialTabIndex: 2), // 2 is the Completed tab index
              ),
            );
          } else {
            Navigator.pushNamed(context, '/doer_dashboard');
          }
        }
        break;
      case 'application':
        case 'application_submitted':
        case 'application_received':
          // Navigate to application details using associatedId (application_id)
          if (notification.associatedId != null) {
            _navigateToListingFromApplication(notification.associatedId!);
          } else {
            // Fallback to appropriate dashboard if application ID is missing
            if (user.role == 'lister') {
              Navigator.pushNamed(context, '/dashboard');
            } else {
              Navigator.pushNamed(context, '/doer_dashboard');
            }
          }
          break;
      case 'job_completed':
      case 'job_cancelled':
        // Navigate to job details or chat
        if (notification.conversationIdForChat != null) {
          Navigator.pushNamed(
            context, 
            '/chat_screen',
            arguments: {
              'conversationId': notification.conversationIdForChat,
              'otherUserId': notification.senderId,
              'listingTitle': notification.relatedListingTitle ?? 'Completed Job',
              'applicationId': notification.associatedId ?? 0,
              'isLister': user.role == 'lister',
            },
          );
        } else {
          // Navigate to appropriate dashboard
          if (user.role == 'lister') {
            // Navigate to combined listings screen with completed tab
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const CombinedListingsScreen(initialTabIndex: 2), // 2 is the Completed tab index
              ),
            );
          } else {
            // For doers with job_completed notifications, navigate to job listings Complete tab
            if (notification.type == 'job_completed') {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const DoerJobListingsScreenMark(initialTabIndex: 3), // 3 is the Complete tab index
                ),
              );
            } else {
              Navigator.pushNamed(context, '/doer_dashboard');
            }
          }
        }
        break;
      case 'job_completed_by_lister':
        // Navigate to combined listings screen with completed tab for listers
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => Scaffold(
                appBar: AppBar(
                  title: const Text('Jobs Listings'),
                  backgroundColor: Colors.white,
                  foregroundColor: const Color(0xFF141CC9),
                  actions: [
                    IconButton(
                      icon: const Icon(Icons.chat_bubble_outline),
                      onPressed: () {
                        Navigator.of(context).pushNamed('/chat_list');
                      },
                      tooltip: 'Chats',
                    ),
                  ],
                ),
                body: const CombinedListingsScreen(initialTabIndex: 2), // 2 is the Completed tab index
              ),
          ),
        );
        break;
      case 'review_received':
        // Navigate to reviews screen for doers
        if (user.role == 'doer' && notification.associatedId != null) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ReviewScreen(
                reviewerId: notification.senderId ?? 0,
                reviewedUserId: user.id!,
                listingId: notification.associatedId,
                mode: 'view',
              ),
            ),
          );
        } else if (user.role == 'lister') {
          // Navigate to lister reviews screen
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ListerReviewsScreen(
                listerId: user.id!,
              ),
            ),
          );
        } else {
          Navigator.pushNamed(context, '/doer_dashboard');
        }
        break;
      case 'review_submitted':
        // Navigate to reviews screen when user submits a review
        if (user.role == 'lister') {
          // Navigate to lister reviews screen
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ListerReviewsScreen(
                listerId: user.id!,
              ),
            ),
          );
        } else {
          // For doers, navigate to dashboard since they don't have a reviews screen
          Navigator.pushNamed(context, '/doer_dashboard');
        }
        break;
      case 'application_accepted':
      case 'application_rejected':
        // Navigate to application details using associatedId (application_id)
        if (notification.associatedId != null) {
          _navigateToListingFromApplication(notification.associatedId!);
        } else {
          // Fallback to appropriate dashboard if application ID is missing
          if (user.role == 'lister') {
            Navigator.pushNamed(context, '/dashboard');
          } else {
            Navigator.pushNamed(context, '/doer_dashboard');
          }
        }
        break;
      default:
        // For unknown notification types, do not redirect anywhere, just mark as read
        break;
    }
  }

  String _getTimeAgo(DateTime dateTime) {
    final manila = tz.getLocation('Asia/Manila');
    final now = tz.TZDateTime.now(manila);
    final diff = now.difference(dateTime);

    if (diff.isNegative) {
      // Future date
      final absDiff = diff.abs();
      if (absDiff.inDays > 6) {
        return DateFormat('MMM d, yyyy').format(dateTime);
      } else if (absDiff.inDays > 0) {
        return 'in ${absDiff.inDays} day${absDiff.inDays > 1 ? 's' : ''}';
      } else if (absDiff.inHours > 0) {
        return 'in ${absDiff.inHours} hour${absDiff.inHours > 1 ? 's' : ''}';
      } else if (absDiff.inMinutes > 0) {
        return 'in ${absDiff.inMinutes} minute${absDiff.inMinutes > 1 ? 's' : ''}';
      } else {
        return 'in moments';
      }
    } else {
      // Past date
      if (diff.inDays > 6) {
        return DateFormat('MMM d, yyyy').format(dateTime);
      } else if (diff.inDays > 0) {
        return '${diff.inDays} day${diff.inDays > 1 ? 's' : ''} ago';
      } else if (diff.inHours > 0) {
        return '${diff.inHours} hour${diff.inHours > 1 ? 's' : ''} ago';
      } else if (diff.inMinutes > 0) {
        return '${diff.inMinutes} minute${diff.inMinutes > 1 ? 's' : ''} ago';
      } else {
        return 'Just now';
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final DateTime now = DateTime.now();
    final DateTime today = DateTime(now.year, now.month, now.day);
    final DateTime startOfWeek = today.subtract(Duration(days: today.weekday - 1));

    final List<NotificationModel> todayNotifications = _notifications
        .where((n) => n.createdAt.isAfter(today))
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

    final List<NotificationModel> thisWeekNotifications = _notifications
        .where((n) => n.createdAt.isAfter(startOfWeek) && n.createdAt.isBefore(today))
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

    final List<NotificationModel> earlierNotifications = _notifications
        .where((n) => n.createdAt.isBefore(startOfWeek))
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

    return CustomScrollView(
        slivers: [
          if (_isLoading || _error != null || _notifications.isEmpty)
            SliverFillRemaining(
              hasScrollBody: false,
              child: Center(
                child: _isLoading
                    ? const CircularProgressIndicator()
                    : _error != null
                        ? Text(_error!)
                        : const Text('No notifications yet.'),
              ),
            )
          else
            SliverToBoxAdapter(
              child: ListView(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                padding: const EdgeInsets.all(8.0),
                children: [
                  if (todayNotifications.isNotEmpty) ...[
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
                      child: Text('Today', style: Theme.of(context).textTheme.titleLarge),
                    ),
                    ...todayNotifications.map((notification) => _buildNotificationItem(notification)).toList(),
                  ],
                  if (thisWeekNotifications.isNotEmpty) ...[
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
                      child: Text('This week', style: Theme.of(context).textTheme.titleLarge),
                    ),
                    ...thisWeekNotifications.map((notification) => _buildNotificationItem(notification)).toList(),
                  ],
                  if (earlierNotifications.isNotEmpty) ...[
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
                      child: Text('Earlier', style: Theme.of(context).textTheme.titleLarge),
                    ),
                    ...earlierNotifications.map((notification) => _buildNotificationItem(notification)).toList(),
                  ],
                ],
              ),
            ),
        ],
      );
  }

  Widget _buildNotificationItem(NotificationModel notification) {
    IconData icon;
    Color iconColor;
    String typeLabel;
    String actionButtonText;
    Color actionButtonColor;



    switch (notification.type) {
      case 'job_posted':
      case 'project_started':
        icon = Icons.work_outline;
        iconColor = Colors.orange.shade700;
        typeLabel = 'Project Started';
        actionButtonText = 'View Project';
        actionButtonColor = Colors.orange;
        break;
      case 'job_completed':
      case 'project_completed':
        icon = Icons.task_alt;
        iconColor = Colors.green.shade700;
        typeLabel = 'Project Completed';
        actionButtonText = 'View Details';
        actionButtonColor = Colors.green;
        break;
      case 'job_completed_by_lister':
        icon = Icons.task_alt;
        iconColor = Colors.green.shade700;
        typeLabel = 'Job Completed';
        actionButtonText = 'View Completed Jobs';
        actionButtonColor = Colors.green;
        break;
      case 'job_cancelled':
      case 'project_cancelled':
        icon = Icons.cancel_outlined;
        iconColor = Colors.red.shade700;
        typeLabel = 'Project Cancelled';
        actionButtonText = 'View Details';
        actionButtonColor = Colors.red;
        break;
      case 'message_received':
      case 'message':
        icon = Icons.message;
        iconColor = Colors.purple.shade700;
        typeLabel = 'New Message';
        actionButtonText = 'Reply';
        actionButtonColor = Colors.blue;
        break;
      case 'application_received':
      case 'application':
      case 'applying':
        icon = Icons.assignment;
        iconColor = Colors.blue.shade700;
        typeLabel = 'New Application';
        actionButtonText = 'View Application';
        actionButtonColor = Colors.blue;
        break;
      case 'payment_received':
      case 'payment':
        icon = Icons.payment;
        iconColor = Colors.green.shade700;
        typeLabel = 'Payment Received';
        actionButtonText = 'View Payment';
        actionButtonColor = Colors.green;
        break;
      case 'review':
      case 'review_received':
        icon = Icons.star_outline;
        iconColor = Colors.amber.shade700;
        typeLabel = 'New Review';
        actionButtonText = 'View Review';
        actionButtonColor = Colors.amber;
        break;
      case 'review_submitted':
        icon = Icons.star;
        iconColor = Colors.amber.shade700;
        typeLabel = 'Review Submitted';
        actionButtonText = 'View Reviews';
        actionButtonColor = Colors.amber;
        break;
      case 'application_accepted':
        icon = Icons.check_circle_outline;
        iconColor = Colors.green.shade700;
        typeLabel = 'Application Accepted';
        actionButtonText = 'View Details';
        actionButtonColor = Colors.green;
        break;
      case 'application_rejected':
        icon = Icons.cancel_outlined;
        iconColor = Colors.red.shade700;
        typeLabel = 'Application Rejected';
        actionButtonText = 'View Details';
        actionButtonColor = Colors.red;
        break;
      case 'job_started':
        icon = Icons.play_arrow;
        iconColor = Colors.blue.shade700;
        typeLabel = 'Job Started';
        actionButtonText = 'View Chat';
        actionButtonColor = Colors.blue;
        break;
      case 'application_submitted':
        icon = Icons.send;
        iconColor = Colors.blue.shade700;
        typeLabel = 'Application Submitted';
        actionButtonText = 'View Application';
        actionButtonColor = Colors.blue;
        break;
      case 'system':
        icon = Icons.notifications_outlined;
        iconColor = Colors.indigo.shade700;
        typeLabel = 'System Update';
        actionButtonText = 'View';
        actionButtonColor = Colors.indigo;
        break;
      default:
        icon = Icons.info_outline;
        iconColor = Colors.grey.shade700;
        typeLabel = notification.type.replaceAll('_', ' ').split(' ').map((word) => word[0].toUpperCase() + word.substring(1)).join(' ');
        actionButtonText = 'View';
        actionButtonColor = Colors.grey;
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6.0, horizontal: 16.0),
      decoration: BoxDecoration(
        color: notification.isRead ? Colors.white : Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: notification.isRead ? Colors.grey.shade200 : Colors.blue.shade200,
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          onTap: () => _handleNotificationTap(notification),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Larger Profile Avatar - Centered
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: iconColor.withOpacity(0.3),
                      width: 2,
                    ),
                  ),
                  child: CircleAvatar(
                    radius: 24, // Reduced from 28 to 24
                    backgroundColor: Colors.grey.shade100,
                    backgroundImage: notification.senderProfilePictureUrl != null && notification.senderProfilePictureUrl!.isNotEmpty
                        ? NetworkImage(notification.senderProfilePictureUrl!)
                        : null,
                    child: notification.senderProfilePictureUrl == null || notification.senderProfilePictureUrl!.isEmpty
                        ? Icon(
                            Icons.person,
                            color: Colors.grey.shade600,
                            size: 24, // Reduced from 28 to 24
                          )
                        : null,
                  ),
                ),
                const SizedBox(width: 12), // Reduced spacing
                // Content Section
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Type Label and Read Status
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              typeLabel,
                              style: TextStyle(
                                fontWeight: FontWeight.w700,
                                fontSize: 13, // Reduced from 16 to 13
                                color: Colors.black87,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (!notification.isRead)
                            Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: Colors.red.shade500,
                                shape: BoxShape.circle,
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4), // Reduced spacing
                      // Notification Content
                      Text(
                        notification.content,
                        style: TextStyle(
                          color: Colors.grey.shade700,
                          fontSize: 12, // Reduced from 14 to 12
                          height: 1.3, // Reduced line height
                          fontWeight: notification.isRead ? FontWeight.normal : FontWeight.w500,
                        ),
                        maxLines: 2, // Reduced from 3 to 2
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8), // Reduced spacing
                      // Timestamp and Action Button Row (swapped positions)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Timestamp (moved to left)
                          Text(
                            _getTimeAgo(notification.createdAt),
                            style: TextStyle(
                              color: Colors.grey.shade500,
                              fontSize: 10, // Reduced from 12 to 10
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          // Dynamic Action Button (moved to right)
                          GestureDetector(
                            onTap: () => _handleNotificationTap(notification),
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6), // Reduced padding
                              decoration: BoxDecoration(
                                color: actionButtonColor,
                                borderRadius: BorderRadius.circular(16), // Reduced radius
                                boxShadow: [
                                  BoxShadow(
                                    color: actionButtonColor.withOpacity(0.3),
                                    blurRadius: 3, // Reduced shadow
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                              ),
                              child: Text(
                                actionButtonText,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10, // Reduced from 12 to 10
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _navigateToListingFromApplication(int applicationId) async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Fetch application details to get listing ID
      final applicationService = ApplicationService();
      final response = await applicationService.getApplicationDetails(
        applicationId: applicationId,
      );

      // Hide loading indicator
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (response['success']) {
        final application = response['application'];
        if (application?.listingId != null) {
          // Navigate to public listing details with the listing ID
          Navigator.pushNamed(
            context,
            '/public_listing_details',
            arguments: {
              'listing_id': application.listingId,
            },
          );
        } else {
          // Show error if listing ID is not available
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Unable to find listing details for this application.'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } else {
        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response['message'] ?? 'Failed to load application details.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Hide loading indicator if still showing
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('An error occurred: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}



