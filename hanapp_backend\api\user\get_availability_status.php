<?php
// hanapp_backend/api/user/get_availability_status.php
// Handles fetching real-time availability status for a doer

require_once '../db_connect.php';

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Get user_id from query parameters
$user_id = $_GET['user_id'] ?? '';

if (empty($user_id)) {
    echo json_encode(["success" => false, "message" => "User ID is required."]);
    exit();
}

try {
    // Prepare a statement to fetch user's availability status
    $stmt = $conn->prepare("
        SELECT id, role, is_available, full_name
        FROM users 
        WHERE id = ?
    ");
    
    if ($stmt === false) {
        throw new Exception("Failed to prepare statement: " . $conn->error);
    }

    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    $stmt->close();

    if ($user) {
        // Debug logging
        error_log("get_availability_status.php: Processing user: " . $user['full_name'] . " (ID: " . $user['id'] . ")");
        error_log("get_availability_status.php: Role: " . $user['role'] . ", is_available: " . $user['is_available']);
        
        // Only doers have availability status
        if ($user['role'] !== 'doer') {
            echo json_encode([
                "success" => false, 
                "message" => "Only doers have availability status.",
                "user_role" => $user['role']
            ]);
            exit();
        }
        
        // Convert to boolean for consistent response
        $isAvailable = (bool)$user['is_available'];
        
        echo json_encode([
            "success" => true,
            "user_id" => (int)$user['id'],
            "full_name" => $user['full_name'],
            "role" => $user['role'],
            "is_available" => $isAvailable,
            "status_text" => $isAvailable ? 'Available' : 'Unavailable'
        ]);
    } else {
        echo json_encode(["success" => false, "message" => "User not found."]);
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(["success" => false, "message" => "Database error: " . $e->getMessage()]);
} finally {
    if (isset($conn) && $conn instanceof mysqli) {
        $conn->close();
    }
}
?>