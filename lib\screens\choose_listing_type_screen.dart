import 'package:flutter/material.dart';
import 'package:hanapp/screens/components/custom_button.dart';
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/services/notification_service.dart';
import 'package:hanapp/utils/auth_service.dart';
import 'package:hanapp/models/user.dart';
import 'package:hanapp/screens/notifications_screen.dart'; // Add this import
import 'package:provider/provider.dart';
import 'package:hanapp/models/user.dart';

class ChooseListingTypeScreen extends StatefulWidget {
  const ChooseListingTypeScreen({super.key});

  @override
  State<ChooseListingTypeScreen> createState() => _ChooseListingTypeScreenState();
}

class _ChooseListingTypeScreenState extends State<ChooseListingTypeScreen> {
  int _selectedIndex = 2; // Set to 2 to indicate FAB is active (create listing)
  int _unreadNotificationCount = 0;

  @override
  void initState() {
    super.initState();
    _loadUnreadNotificationCount();
  }

  Future<void> _loadUnreadNotificationCount() async {
    try {
      final user = await AuthService.getUser();
      if (user != null) {
        final response = await NotificationService().getUnreadCount(userId: user.id!);
        if (response['success']) {
          setState(() {
            _unreadNotificationCount = response['unread_count'] ?? 0;
          });
        }
      }
    } catch (e) {
      print('Error loading unread notification count: $e');
    }
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  
    switch (index) {
      case 0: // Home
        Navigator.pushReplacementNamed(context, '/lister_dashboard');
        break;
      case 1: // Jobs (View jobs, not create)
        // Navigate back to dashboard and set to jobs tab (index 1)
        Navigator.pushReplacementNamed(
          context, 
          '/lister_dashboard',
          arguments: {'selectedIndex': 1}
        );
        break;
      case 2: // Create Listing (FAB)
        // Show listing type selection dialog
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Create Listing'),
              content: const Text('Choose the type of listing you want to create:'),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _showAsapWarningDialog(context);
                  },
                  child: const Text('ASAP Listing'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _showPublicListingWarningDialog(context);
                  },
                  child: const Text('Public Listing'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Cancel'),
                ),
              ],
            );
          },
        );
        break;
      case 3: // Notifications
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => Scaffold(
              appBar: AppBar(
                title: const Text('Notifications'),
                backgroundColor: Colors.white,
                foregroundColor: Constants.primaryColor,
                iconTheme: IconThemeData(color: Constants.primaryColor),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.chat_bubble_outline),
                    onPressed: () {
                      Navigator.of(context).pushNamed('/chat_list');
                    },
                    tooltip: 'Chats',
                  ),
                ],
              ),
              body: NotificationsScreen(userRole: Provider.of<User>(context).role,
              ),
            ),
          ),
        );
        break;
      case 4: // Profile
        // Navigate back to dashboard and set to profile tab (index 3)
        Navigator.pushReplacementNamed(
          context, 
          '/lister_dashboard',
          arguments: {'selectedIndex': 3}
        );
        break;
    }
  }

  Widget _buildNotificationIcon() {
    return Stack(
      children: [
        Icon(
          Icons.notifications,
          color: _selectedIndex == 3 ? Colors.yellow.shade700 : Colors.white70,
        ),
        if (_unreadNotificationCount > 0)
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(10),
              ),
              constraints: const BoxConstraints(
                minWidth: 16,
                minHeight: 16,
              ),
              child: Text(
                _unreadNotificationCount.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }

  // Function to show the ASAP warning dialog
  Future<void> _showAsapWarningDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false, // User must tap "I Understand, Proceed"
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
          title: const Text(
            'Reminder!',
            style: TextStyle(
              color: Colors.red,
              fontWeight: FontWeight.bold,
              fontSize: 22,
            ),
            textAlign: TextAlign.center,
          ),
          content: const SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text(
                  'ASAP listings are for onsite work only. Any task that can be done online is prohibited.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 15, color: Colors.black87),
                ),
                SizedBox(height: 10),
                Text(
                  'The task you are posting should also be something urgent or something you need immediate help with.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 15, color: Colors.black87),
                ),
                SizedBox(height: 15),
                Text(
                  'If it\'s not urgent, please post it under the Public Listing instead.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15, color: Colors.black87),
                ),
                SizedBox(height: 15),
                Text(
                  'Doers may report you if they see you posting something inappropriate for ASAP. This may cause you to be recommended less — and worse, you could get banned from the platform.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 15, color: Colors.black87),
                ),
              ],
            ),
          ),
          actions: <Widget>[
            Center(
              child: CustomButton(
                text: 'I Understand, Proceed',
                onPressed: () {
                  Navigator.of(dialogContext).pop(); // Close the dialog
                  // Navigate directly to the ASAP listing form
                  Navigator.of(context).pushNamed('/asap_listing_form');
                },
                color: Colors.red.shade700, // Use red color for warning
                textColor: Colors.white,
                borderRadius: 10.0,
                height: 45.0,
                width: 300, // Adjust button width as needed
              ),
            ),
          ],
        );
      },
    );
  }


  // NEW: Function to show the Public Listing warning dialog
  Future<void> _showPublicListingWarningDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false, // User must tap "Understood"
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
          title: const Text(
            'Reminder!',
            style: TextStyle(
              color: Colors.red,
              fontWeight: FontWeight.bold,
              fontSize: 22,
            ),
            textAlign: TextAlign.center,
          ),
          content: const SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text(
                  'Requests like... "Please help me sell", "Sell my...", "Commission for selling." or anything that asks other Doers to sell your items are not allowed on this platform.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 15, color: Colors.black87),
                ),
                SizedBox(height: 15),
                Text(
                  'Doing so may trigger the AI and the system, causing you to be recommended less and worse, you could get banned from the platform.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 15, color: Colors.black87),
                ),
              ],
            ),
          ),
          actions: <Widget>[
            Center(
              child: CustomButton(
                text: 'Understood',
                onPressed: () {
                  Navigator.of(dialogContext).pop(); // Close the dialog
                  // Navigate to the Public Listing form after understanding the warning
                  // IMPORTANT: Ensure '/public_listing_form' is the correct route for your public listing entry form
                  Navigator.of(context).pushNamed('/public_listing_form');
                },
                color: Constants.primaryColor, // Use your primary blue color
                textColor: Colors.white,
                borderRadius: 10.0,
                height: 45.0,
                width: 150, // Adjust button width as needed
              ),
            ),
          ],
        );
      },
    );
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pushReplacementNamed(context, '/lister_dashboard');
          },
        ),
        title: const Text('Choose Listing Type'),
        backgroundColor: Colors.white, // White background
        foregroundColor: Constants.primaryColor, // Blue text
        iconTheme: IconThemeData(color: Constants.primaryColor), // Blue icons
        actions: [
          IconButton(
            icon: const Icon(Icons.chat_bubble_outline),
            onPressed: () {
              Navigator.of(context).pushNamed('/chat_list');
            },
            tooltip: 'Chats',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(height: 40),
            const Text(
              'Choose what you need',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black, // Black text
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),
            Text(
              'Select which type of listing you want to create.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600], // Grey text
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 40),
            Row(
              children: [
                Expanded(
                  child: Card(
                    elevation: 4,
                    child: InkWell(
                      onTap: () => _showAsapWarningDialog(context),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                              decoration: BoxDecoration(
                                color: Constants.primaryColor, // Blue background for button
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: const Text(
                                'ASAP',
                                style: TextStyle(
                                  color: Colors.white, // White text on blue background
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                            const SizedBox(height: 20),
                            Text(
                              'For on-demand services only. We will contact the nearest client within minutes',
                              style: TextStyle(
                                color: Colors.grey[600], // Grey text
                                fontSize: 14,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Card(
                    elevation: 4,
                    child: InkWell(
                      onTap: () => _showPublicListingWarningDialog(context),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 10),
                              decoration: BoxDecoration(
                                color: Constants.primaryColor, // Blue background for button
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: const Text(
                                'Public Listing',
                                style: TextStyle(
                                  color: Colors.white, // White text on blue background
                                  fontWeight: FontWeight.bold,
                                  fontSize: 13,
                                ),
                              ),
                            ),
                            const SizedBox(height: 20),
                            Text(
                              '\nAnyone can apply publicly, Anytime and Anywhere\n',
                              style: TextStyle(
                                color: Colors.grey[600], // Grey text
                                fontSize: 14,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      bottomNavigationBar: BottomAppBar(
        color: Constants.primaryColor, // Blue background
        shape: const CircularNotchedRectangle(), // Notch for FAB
        notchMargin: 8.0, // Margin for FAB notch
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: <Widget>[
            IconButton(
              icon: const Icon(Icons.home),
              color: _selectedIndex == 0 ? Colors.yellow.shade700 : Colors.white70,
              onPressed: () => _onItemTapped(0),
            ),
            IconButton(
              icon: const Icon(Icons.check_circle_outline), // Jobs icon
              color: _selectedIndex == 1 ? Colors.yellow.shade700 : Colors.white70,
              onPressed: () => _onItemTapped(1),
            ),
            const SizedBox(width: 48), // Space for the Floating Action Button
            IconButton(
              icon: _buildNotificationIcon(),
              onPressed: () => _onItemTapped(3),
            ),
            IconButton(
              icon: const Icon(Icons.person),
              color: _selectedIndex == 4 ? Colors.yellow.shade700 : Colors.white70,
              onPressed: () => _onItemTapped(4),
            ),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        transform: Matrix4.translationValues(0, _selectedIndex == 2 ? -8.0 : 0.0, 0),
        child: FloatingActionButton(
          onPressed: () => _onItemTapped(2),
          backgroundColor: Constants.primaryColor,
          shape: const CircleBorder(),
          child: const Icon(Icons.add, color: Colors.white, size: 35),
        ),
      ),
    );
  }
}
