<?php
// hanapp_backend/api/utils/deactivate_completed_listings.php
// Automatically deactivates completed listings from both ASAP and PUBLIC listings
// to prevent doers from seeing or applying to already completed jobs

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../config/db_connect.php';

// This script can be run manually or via cron job
// Recommended cron job: */30 * * * * (every 30 minutes) to regularly clean up completed listings

try {
    $deactivatedCount = 0;
    $errors = [];
    $currentTime = date('Y-m-d H:i:s');
    
    error_log("Completed listings deactivation check started at: $currentTime");
    
    $conn->begin_transaction();
    
    // Deactivate completed ASAP listings
    $asapSql = "
        UPDATE asap_listings 
        SET is_active = FALSE 
        WHERE status = 'completed' 
        AND is_active = TRUE
    ";
    
    $asapResult = $conn->query($asapSql);
    if (!$asapResult) {
        throw new Exception("Failed to deactivate ASAP listings: " . $conn->error);
    }
    
    $asapDeactivated = $conn->affected_rows;
    $deactivatedCount += $asapDeactivated;
    
    error_log("Deactivated $asapDeactivated completed ASAP listings");
    
    // Deactivate completed PUBLIC listings
    $publicSql = "
        UPDATE listingsv2 
        SET is_active = FALSE 
        WHERE status = 'completed' 
        AND is_active = TRUE
    ";
    
    $publicResult = $conn->query($publicSql);
    if (!$publicResult) {
        throw new Exception("Failed to deactivate PUBLIC listings: " . $conn->error);
    }
    
    $publicDeactivated = $conn->affected_rows;
    $deactivatedCount += $publicDeactivated;
    
    error_log("Deactivated $publicDeactivated completed PUBLIC listings");
    

    

    
    $conn->commit();
    
    // Log summary
    $summary = [
        'success' => true,
        'deactivated_count' => $deactivatedCount,
        'asap_deactivated' => $asapDeactivated,
        'public_deactivated' => $publicDeactivated,
        'errors' => $errors,
        'timestamp' => $currentTime
    ];
    
    error_log("Completed listings deactivation completed at $currentTime: " . json_encode($summary));
    
    // If run via web, return JSON response
    if (isset($_SERVER['HTTP_HOST'])) {
        header('Content-Type: application/json');
        echo json_encode($summary);
    } else {
        // If run via CLI, output to console
        echo "Completed listings deactivation completed at $currentTime:\n";
        echo "Total deactivated: $deactivatedCount listings\n";
        echo "ASAP deactivated: $asapDeactivated\n";
        echo "PUBLIC deactivated: $publicDeactivated\n";
        echo "Errors: " . count($errors) . "\n";
        if (!empty($errors)) {
            echo "Error details:\n";
            foreach ($errors as $error) {
                echo "- $error\n";
            }
        }
    }
    
} catch (Exception $e) {
    $conn->rollback();
    $errorMsg = "Completed listings deactivation failed: " . $e->getMessage();
    error_log($errorMsg);
    
    if (isset($_SERVER['HTTP_HOST'])) {
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => $errorMsg
        ]);
    } else {
        echo $errorMsg . "\n";
    }
}
?>