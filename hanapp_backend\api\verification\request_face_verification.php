<?php
// hanapp_backend/api/verification/request_face_verification.php
// Handles submission of a live photo for face verification.

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

require_once '../../config/db_connect.php'; // Adjust path

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {


    $userId = $_POST['user_id'] ?? null;
    $verificationType = $_POST['verification_type'] ?? 'normal'; // Default to 'normal' if not specified

    if (empty($userId) || !is_numeric($userId)) {
        throw new Exception("User ID is required and must be numeric.");
    }

    // Validate verification type
    if (!in_array($verificationType, ['normal', 'badge'])) {
        $verificationType = 'normal'; // Default to normal if invalid type provided
    }

    if (!isset($_FILES['live_photo'])) {
        throw new Exception("Live photo is required for face verification.");
    }

    $uploadDir = '../../uploads/live_photos/images/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    $liveFile = $_FILES['live_photo'];
    // FIXED: Added more common image types, including HEIC variants and WebP
    $allowedTypes = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/heic',       // For iOS HEIC images
        'image/heif',       // Another HEIC variant
        'image/webp',       // Modern web format
    ];
    $maxFileSize = 1 * 1024 * 1024; // 1 MB

    if ($liveFile['error'] !== UPLOAD_ERR_OK) {
        throw new Exception("Live photo upload error: " . $liveFile['error']);
    }
    // Check actual MIME type using finfo_file for better accuracy
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $actualMimeTypeLivePhoto = finfo_file($finfo, $liveFile['tmp_name']);
    finfo_close($finfo);

    if (!in_array($actualMimeTypeLivePhoto, $allowedTypes)) { // Use actualMimeTypeLivePhoto here
        throw new Exception("Live photo: Invalid file type. Allowed: JPG, PNG, GIF, HEIC, WebP. Detected: $actualMimeTypeLivePhoto");
    }
    if ($liveFile['size'] > $maxFileSize) {
        throw new Exception("Live photo: File size exceeds 1MB limit.");
    }

    $liveFileName = uniqid('live_photo_') . '_' . $userId . '.' . pathinfo($liveFile['name'], PATHINFO_EXTENSION);
    $liveFilePath = $uploadDir . $liveFileName;
    if (!move_uploaded_file($liveFile['tmp_name'], $liveFilePath)) {
        throw new Exception("Failed to move live photo.");
    }
    $livePhotoUrl = str_replace('../../', '', $liveFilePath);

    // --- SIMULATED FACE RECOGNITION LOGIC ---
    // In a real application, you would integrate with an AI/ML service here
    // to compare the live photo with the ID photos on record.
    $faceMatchSuccess = true; // FIXED: Set to true for testing - always succeed (was random)

    $newVerificationStatus = $faceMatchSuccess ? 'pending' : 'rejected'; // FIXED: Changed to 'pending' instead of 'verified'
    $message = $faceMatchSuccess ? "Face verification completed successfully! Your verification is now pending review." : "Face verification failed. Please try again.";

    // Update user record in the database
    $conn->begin_transaction();

    $stmt = $conn->prepare("
        UPDATE users
        SET
            live_photo_url = ?,
            verification_status = ?,
            id_verified = ? -- Set id_verified to 1 if face match is successful
        WHERE
            id = ?
    ");
    if ($stmt === false) {
        throw new Exception("Failed to prepare statement: " . $conn->error);
    }

    $idVerifiedValue = $faceMatchSuccess ? 1 : 0;

    $stmt->bind_param("ssii", $livePhotoUrl, $newVerificationStatus, $idVerifiedValue, $userId);

    if (!$stmt->execute()) {
        throw new Exception("Failed to update user face verification data: " . $stmt->error);
    }

    // Update verification record - sync face scan photo and ensure both tables match
    // First, check if verification record exists
    $checkVerificationStmt = $conn->prepare("SELECT id FROM verifications WHERE user_id = ?");
    $checkVerificationStmt->bind_param("i", $userId);
    $checkVerificationStmt->execute();
    $verificationResult = $checkVerificationStmt->get_result();

    if ($verificationResult->num_rows > 0) {
        // Update existing verification record - only update face scan photo, verification type and status, preserve existing photos
        $updateVerificationStmt = $conn->prepare("
            UPDATE verifications
            SET
                face_scan_photo_url = ?,
                verification_type = ?,
                status = ?
            WHERE user_id = ?
        ");
        if ($updateVerificationStmt) {
            $updateVerificationStmt->bind_param("sssi", $livePhotoUrl, $verificationType, $newVerificationStatus, $userId);
            $updateVerificationStmt->execute();

            error_log("request_face_verification.php: Updated verification record for user $userId - added face scan photo and set verification type to $verificationType");
        }
    } else {
        // Create new verification record with all data from users table
        $insertVerificationStmt = $conn->prepare("
            INSERT INTO verifications (
                user_id,
                id_type,
                id_photo_front_url,
                id_photo_back_url,
                brgy_clearance_photo_url,
                face_scan_photo_url,
                confirmation_status,
                verification_type,
                status,
                submitted_at
            ) VALUES (?, 'National ID', ?, ?, ?, ?, TRUE, ?, ?, CURRENT_TIMESTAMP)
        ");

        // Get user's photo data to insert
        $getUserPhotosStmt = $conn->prepare("SELECT id_photo_front_url, id_photo_back_url, brgy_clearance_photo_url FROM users WHERE id = ?");
        $getUserPhotosStmt->bind_param("i", $userId);
        $getUserPhotosStmt->execute();
        $userPhotosResult = $getUserPhotosStmt->get_result();
        $userPhotos = $userPhotosResult->fetch_assoc();
        if ($insertVerificationStmt && $userPhotos) {
            $insertVerificationStmt->bind_param("issssss",
                $userId,
                $userPhotos['id_photo_front_url'],
                $userPhotos['id_photo_back_url'],
                $userPhotos['brgy_clearance_photo_url'],
                $livePhotoUrl,
                $verificationType,
                $newVerificationStatus
            );
            $insertVerificationStmt->execute();

            error_log("request_face_verification.php: Created new verification record for user $userId - synced all photos from users table with verification type $verificationType");
        }
    }

    $conn->commit();

    echo json_encode([
        "success" => $faceMatchSuccess,
        "message" => $message,
        "live_photo_url" => $livePhotoUrl,
        "verification_status" => $newVerificationStatus
    ]);

} catch (Exception $e) {
    if (isset($conn) && $conn instanceof mysqli && $conn->in_transaction) {
        $conn->rollback();
    }
    http_response_code(500);
    error_log("request_face_verification.php: Caught exception: " . $e->getMessage(), 0);
    echo json_encode([
        "success" => false,
        "message" => "Error during face verification: " . $e->getMessage()
    ]);
} finally {
    if (isset($conn) && $conn instanceof mysqli && $conn->ping()) {
        $conn->close();
    }
}
?>
