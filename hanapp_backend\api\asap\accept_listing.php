<?php
// hanapp_backend/api/asap/accept_listing.php
// Handle doer acceptance of ASAP listings and price offers

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../../config/db_connect.php';

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $listingId = $input['listing_id'] ?? null;
    $doerId = $input['doer_id'] ?? null;
    $action = $input['action'] ?? 'accept'; // 'accept', 'reject', or 'offer'
    $offeredPrice = $input['offered_price'] ?? null;
    
    if (!$listingId || !$doerId) {
        throw new Exception('Missing required parameters');
    }
    
    // Check if listing exists and is still pending
    $listingQuery = "SELECT * FROM asap_listings WHERE id = ? AND status = 'pending'";
    $listingStmt = $conn->prepare($listingQuery);
    $listingStmt->bind_param('i', $listingId);
    $listingStmt->execute();
    $listingResult = $listingStmt->get_result();
    
    if ($listingResult->num_rows === 0) {
        throw new Exception('ASAP listing not found or no longer available');
    }
    
    $listing = $listingResult->fetch_assoc();
    
    if ($action === 'offer') {
        // Handle price offer for open price listings
        if (!$listing['is_open_price']) {
            throw new Exception('This listing does not accept price offers');
        }
        
        if (!$offeredPrice || $offeredPrice < 20) {
            throw new Exception('Invalid price offer. Minimum is ₱20.00');
        }
        
        // Insert or update price offer
        $offerQuery = "INSERT INTO asap_price_offers (listing_id, doer_id, offered_price) 
                      VALUES (?, ?, ?) 
                      ON DUPLICATE KEY UPDATE 
                      offered_price = VALUES(offered_price), 
                      status = 'pending', 
                      updated_at = NOW()";
        $offerStmt = $conn->prepare($offerQuery);
        $offerStmt->bind_param('iid', $listingId, $doerId, $offeredPrice);
        $offerStmt->execute();
        
        // Create notification for lister about the price offer
        $notificationQuery = "INSERT INTO notificationsv2 (
            user_id,
            sender_id,
            type,
            title,
            content,
            associated_id,
            related_listing_title
        ) VALUES (?, ?, ?, ?, ?, ?, ?)";
        $notificationStmt = $conn->prepare($notificationQuery);
        
        // Create message content first
        $messageContent = 'A doer offered ₱' . $offeredPrice;
        
        // Create variables for binding
        $notificationType = 'price_offer';
        $notificationTitle = 'New Price Offer';
        
        $notificationStmt->bind_param(
            'iisssis',
            $listing['lister_id'],  // Changed from user_id to lister_id
            $doerId,
            $notificationType,
            $notificationTitle,
            $messageContent,
            $listingId,
            $listing['title']
        );
        $notificationStmt->execute();
        
        echo json_encode([
            'success' => true,
            'message' => 'Price offer submitted successfully',
            'action' => 'offer',
            'offered_price' => $offeredPrice
        ]);
        
    } else {
        // Handle regular accept/reject
        $status = $action === 'accept' ? 'accepted' : 'rejected';
        
        // Check if doer already responded to this listing
        $existingQuery = "SELECT * FROM asap_listing_acceptance WHERE listing_id = ? AND doer_id = ?";
        $existingStmt = $conn->prepare($existingQuery);
        $existingStmt->bind_param('ii', $listingId, $doerId);
        $existingStmt->execute();
        $existingResult = $existingStmt->get_result();
        
        if ($existingResult->num_rows > 0) {
            // Update existing record
            $updateQuery = "UPDATE asap_listing_acceptance SET status = ?, updated_at = NOW() WHERE listing_id = ? AND doer_id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bind_param('sii', $status, $listingId, $doerId);
            $updateStmt->execute();
        } else {
            // Insert new record
            $insertQuery = "INSERT INTO asap_listing_acceptance (listing_id, doer_id, status) VALUES (?, ?, ?)";
            $insertStmt = $conn->prepare($insertQuery);
            $insertStmt->bind_param('iis', $listingId, $doerId, $status);
            $insertStmt->execute();
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Response recorded successfully',
            'action' => $action
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>