import 'dart:async';
import 'package:flutter/material.dart';
import 'package:hanapp/models/balance.dart';
import 'package:hanapp/models/transaction.dart';
import 'package:hanapp/utils/auth_service.dart';
import 'package:hanapp/services/hanapp_balance_service.dart';
import 'package:hanapp/services/cash_in_service.dart';
import 'package:hanapp/services/xendit_service.dart';
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

class HanAppBalanceScreen extends StatefulWidget {
  const HanAppBalanceScreen({super.key});

  @override
  State<HanAppBalanceScreen> createState() => _HanAppBalanceScreenState();
}

class _HanAppBalanceScreenState extends State<HanAppBalanceScreen> with WidgetsBindingObserver {
  final HanAppBalanceService _balanceService = HanAppBalanceService();
  final TextEditingController _amountController = TextEditingController();
  double _currentBalance = 0.0;
  List<Transaction> _transactions = [];
  bool _isLoading = true;
  int? _currentUserId;
  String? _selectedCashInMethod;
  String? _selectedBank;
  String? _selectedCardType; // For credit card type selection
  double _cashInAmount = 500.0; // Default minimum amount



  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _loadUserDataAndFetchBalance();
    _amountController.addListener(_onAmountChanged);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _amountController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // When user returns to app, refresh balance and transactions
    if (state == AppLifecycleState.resumed && _currentUserId != null) {
      print('🔄 App resumed - refreshing balance and transactions');
      _fetchBalanceAndTransactions();
    }
  }

  void _onAmountChanged() {
    final text = _amountController.text;
    if (text.isNotEmpty) {
      final amount = double.tryParse(text);
      if (amount != null && amount > 0) {
        setState(() {
          _cashInAmount = amount;
        });
      }
    }
  }

  Future<void> _loadUserDataAndFetchBalance() async {
    final user = await AuthService.getUser();
    if (user == null || user.id == null) {
      _showSnackBar('User not logged in.', isError: true);
      Navigator.of(context).pop();
      return;
    }
    _currentUserId = user.id;
    _fetchBalanceAndTransactions();
  }

  Future<void> _fetchBalanceAndTransactions() async {
    if (_currentUserId == null || !mounted) return;

    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    // Fetch updated balance and transactions
    final balanceResponse = await _balanceService.getHanAppBalance(userId: _currentUserId!);
    if (balanceResponse['success']) {
      _currentBalance = balanceResponse['balance'];
    } else {
      if (mounted) {
        _showSnackBar('Failed to load balance: ${balanceResponse['message']}', isError: true);
      }
      _currentBalance = 0.0;
    }

    // Fetch Transactions
    final transactionsResponse = await _balanceService.getTransactionHistory(userId: _currentUserId!);
    if (transactionsResponse['success']) {
      _transactions = transactionsResponse['transactions'];
      // _transactions.sort((a, b) => b.createdAt.compareTo(a.createdAt)); // Sort by newest first
    } else {
      if (mounted) {
        _showSnackBar('Failed to load transactions: ${transactionsResponse['message']}', isError: true);
      }
      _transactions = [];
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }



  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
      ),
    );
  }



  void _showPaymentDialog(String redirectUrl, String invoiceId) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Complete Payment'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Amount: ₱${NumberFormat('#,##0.00').format(_cashInAmount)}'),
              const SizedBox(height: 8),
              Text('Payment Method: ${_getPaymentMethodName(_selectedCashInMethod!)}'),
              const SizedBox(height: 16),
              const Text(
                'You will be redirected to complete your payment. Your balance will be updated automatically once payment is confirmed.',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '💡 Payment Instructions:',
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 13),
                    ),
                    SizedBox(height: 4),
                    Text(
                      '• Complete payment in the opened page\n• Return to this app after payment\n• Your balance updates automatically',
                      style: TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                // Launch payment URL - Xendit handles everything automatically
                await _launchPaymentUrl(redirectUrl, invoiceId);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Constants.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Pay Now'),
            ),
          ],
        );
      },
    );
  }

  void _cashIn() async {
    if (_selectedCashInMethod == null) {
      _showSnackBar('Please select a payment method', isError: true);
      return;
    }

    if (_cashInAmount <= 0) {
      _showSnackBar('Please enter a valid amount', isError: true);
      return;
    }

    if (_cashInAmount < 500) {
      _showSnackBar('Minimum cash-in amount is ₱500', isError: true);
      return;
    }

    if (_cashInAmount > 50000) {
      _showSnackBar('Maximum cash-in amount is ₱50,000', isError: true);
      return;
    }

    if (_selectedCashInMethod == 'bank_transfer' && _selectedBank == null) {
      _showSnackBar('Please select a bank', isError: true);
      return;
    }

    if (_selectedCashInMethod == 'card' && _selectedCardType == null) {
      _showSnackBar('Please select your card type', isError: true);
      return;
    }

    // Get current user ID
    if (_currentUserId == null) {
      _showSnackBar('User not found. Please log in again.', isError: true);
      return;
    }

    await _createCashInInvoice();
  }

  Future<void> _createCashInInvoice() async {
    setState(() {
      _isLoading = true;
    });

    _showLoadingDialog();

    try {
      // Get current user data
      final currentUser = await AuthService.getUser();
      if (currentUser == null) {
        _showSnackBar('User not found. Please log in again.', isError: true);
        return;
      }

      // Determine payment method
      String paymentMethod = _selectedCashInMethod!;
      if (_selectedCashInMethod == 'bank_transfer' && _selectedBank != null) {
        paymentMethod = _selectedBank!;
      }

      // Use CashInService to create invoice
      final cashInService = CashInService();
      final result = await cashInService.createCashInInvoice(
        userId: currentUser.id,
        amount: _cashInAmount,
        paymentMethod: paymentMethod,
        cardType: _selectedCardType, // Pass card type for credit cards
        userEmail: currentUser.email,
        userFullName: currentUser.fullName,
      );

      // Hide loading dialog
      if (mounted) Navigator.of(context).pop();

      if (result['success']) {
        final paymentDetails = result['payment_details'];
        final invoiceUrl = paymentDetails['invoice_url'];

        // Show success dialog and launch payment
        _showCashInPaymentDialog(_cashInAmount, invoiceUrl, paymentDetails);
      } else {
        _showSnackBar(result['message'] ?? 'Failed to create payment invoice', isError: true);
      }
    } catch (e) {
      // Hide loading dialog
      if (mounted) Navigator.of(context).pop();
      _showSnackBar('Network error: ${e.toString()}', isError: true);
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showLoadingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 20),
            Text('Creating payment invoice...'),
          ],
        ),
      ),
    );
  }

  void _showCashInPaymentDialog(double amount, String invoiceUrl, Map<String, dynamic> paymentDetails) {
    final baseAmount = paymentDetails['base_amount'] ?? amount;
    final transactionFee = paymentDetails['transaction_fee'] ?? 20.0;
    final totalAmount = paymentDetails['total_amount'] ?? (baseAmount + transactionFee);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cash-in Summary'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Payment Method - Compact
              Text(
                'Payment Method: ${_getPaymentMethodName(_selectedCashInMethod!)}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.blue.shade700,
                ),
              ),
              const SizedBox(height: 12),

              // Amount Breakdown - Compact
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  children: [
                    // Base Amount
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('Cash-in Amount:', style: TextStyle(fontSize: 13)),
                        Text(
                          '₱${NumberFormat('#,##0.00').format(baseAmount)}',
                          style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),

                    // Transaction Fee
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('Transaction Fee:', style: TextStyle(fontSize: 13)),
                        Text(
                          '₱${NumberFormat('#,##0.00').format(transactionFee)}',
                          style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),

                    // Divider
                    Container(
                      height: 1,
                      color: Colors.grey.shade300,
                      margin: const EdgeInsets.symmetric(vertical: 6),
                    ),

                    // Total Amount
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Total to Pay:',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '₱${NumberFormat('#,##0.00').format(totalAmount)}',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Constants.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),

              // Compact Instructions
              Text(
                'Click "Pay Now" to complete payment. Your balance will update automatically.',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();

              // Launch payment URL
              final success = await XenditService.instance.launchPayment(invoiceUrl);
              if (success) {
                // Payment page opened successfully
                // Refresh balance after payment attempt
                _loadUserDataAndFetchBalance();
              } else {
                _showSnackBar('Failed to open payment page', isError: true);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Constants.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Pay Now'),
          ),
        ],
      ),
    );
  }

  Future<void> _launchPaymentUrl(String url, String invoiceId) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        print('🚀 Launching payment URL - Invoice: $invoiceId');

        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        _showSnackBar('Unable to open payment page. Please try again with a different payment method.', isError: true);
      }
    } catch (e) {
      print('Payment URL launch error: $e');
      if (e.toString().contains('Access Denied') || e.toString().contains('403') || e.toString().contains('edgesuite.net')) {
        _showSnackBar('Payment method temporarily unavailable. Please try a different payment method.', isError: true);
      } else {
        _showSnackBar('Error opening payment page: $e', isError: true);
      }
    }
  }















  void _showPaymentSuccessDialog(double amountAdded) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 28),
              const SizedBox(width: 8),
              const Text('Payment Successful!'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '🎉 Congratulations! Your payment has been confirmed.',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '💰 Amount Added: ₱${NumberFormat('#,##0.00').format(amountAdded)}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.green.shade700,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Your Tapp Balance has been updated automatically.',
                      style: TextStyle(fontSize: 14, color: Colors.green.shade600),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Refresh the screen to show updated balance
                _fetchBalanceAndTransactions();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('Great!'),
            ),
          ],
        );
      },
    );
  }

  void _showPaymentFailedDialog(String status) {
    final statusMessage = status == 'EXPIRED'
        ? 'Your payment session has expired.'
        : 'Your payment was not completed.';

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.error_outline, color: Colors.orange, size: 28),
              const SizedBox(width: 8),
              const Text('Payment Not Completed'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(statusMessage),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '💡 What to do next:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 4),
                    Text(
                      '• Try initiating a new payment\n• Check your payment method\n• Contact support if issues persist',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // User can try again
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Constants.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Try Again'),
            ),
          ],
        );
      },
    );
  }



  String _getPaymentMethodName(String method) {
    switch (method) {
      case 'gcash':
        return 'GCash E-Wallet';
      case 'card':
        return 'Credit/Debit Card';
      case 'bpi':
        return 'BPI Bank Transfer';
      case 'chinabank':
        return 'China Bank Transfer';
      case 'rcbc':
        return 'RCBC Bank Transfer';
      case 'unionbank':
        return 'UnionBank Transfer';
      default:
        return method.toUpperCase();
    }
  }

  Widget _buildPaymentOption(String title, String value) {
    final isSelected = _selectedCashInMethod == value;

    return InkWell(
      onTap: () {
        setState(() {
          if (_selectedCashInMethod == value) {
            _selectedCashInMethod = null;
            _selectedBank = null;
          } else {
            _selectedCashInMethod = value;
            if (value != 'bank_transfer') {
              _selectedBank = null;
            }
            if (value != 'card') {
              _selectedCardType = null;
            }
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? Constants.primaryColor : Colors.grey.shade300,
                  width: 2,
                ),
                color: isSelected ? Constants.primaryColor : Colors.transparent,
              ),
              child: isSelected
                  ? const Icon(Icons.check, color: Colors.white, size: 16)
                  : null,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: isSelected ? Constants.primaryColor : Colors.black87,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBankTransferOption() {
    final isSelected = _selectedCashInMethod == 'bank_transfer';

    // List of working banks
    final workingBanks = [
      {'id': 'bpi', 'name': 'BPI Direct Debit', 'icon': '🏦'},
      {'id': 'chinabank', 'name': 'China Bank Direct Debit', 'icon': '🏦'},
      {'id': 'rcbc', 'name': 'RCBC Direct Debit', 'icon': '🏦'},
      {'id': 'unionbank', 'name': 'UBP Direct Debit', 'icon': '🏦'},
    ];

    return Column(
      children: [
        InkWell(
          onTap: () {
            setState(() {
              if (_selectedCashInMethod == 'bank_transfer') {
                _selectedCashInMethod = null;
                _selectedBank = null;
              } else {
                _selectedCashInMethod = 'bank_transfer';
              }
            });
          },
          child: Container(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected ? Constants.primaryColor : Colors.grey.shade300,
                      width: 2,
                    ),
                    color: isSelected ? Constants.primaryColor : Colors.transparent,
                  ),
                  child: isSelected
                      ? const Icon(Icons.check, color: Colors.white, size: 16)
                      : null,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Bank Transfer',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: isSelected ? Constants.primaryColor : Colors.black87,
                    ),
                  ),
                ),
                Icon(
                  isSelected ? Icons.expand_less : Icons.expand_more,
                  color: isSelected ? Constants.primaryColor : Colors.grey,
                ),
              ],
            ),
          ),
        ),
        if (isSelected) ...[
          Container(
            margin: const EdgeInsets.only(left: 60, right: 20, bottom: 10),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                isExpanded: true,
                value: _selectedBank,
                hint: const Text('Select your bank'),
                items: workingBanks.map((bank) {
                  return DropdownMenuItem<String>(
                    value: bank['id'] as String,
                    child: Row(
                      children: [
                        Text(bank['icon'] as String, style: const TextStyle(fontSize: 20)),
                        const SizedBox(width: 12),
                        Expanded(child: Text(bank['name'] as String)),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (String? value) {
                  setState(() {
                    _selectedBank = value;
                  });
                },
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildCardOption() {
    final isSelected = _selectedCashInMethod == 'card';

    return Column(
      children: [
        InkWell(
          onTap: () {
            setState(() {
              if (_selectedCashInMethod == 'card') {
                _selectedCashInMethod = null;
                _selectedCardType = null;
              } else {
                _selectedCashInMethod = 'card';
              }
            });
          },
          child: Container(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected ? Constants.primaryColor : Colors.grey.shade300,
                      width: 2,
                    ),
                    color: isSelected ? Constants.primaryColor : Colors.transparent,
                  ),
                  child: isSelected
                      ? const Icon(Icons.check, color: Colors.white, size: 16)
                      : null,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Credit/Debit Card',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: isSelected ? Constants.primaryColor : Colors.black87,
                    ),
                  ),
                ),
                Icon(
                  isSelected ? Icons.expand_less : Icons.expand_more,
                  color: isSelected ? Constants.primaryColor : Colors.grey,
                ),
              ],
            ),
          ),
        ),
        if (isSelected) ...[
          Container(
            margin: const EdgeInsets.only(left: 60, right: 20, bottom: 10),
            child: Column(
              children: [
                // Philippine Card Option
                RadioListTile<String>(
                  title: const Text('Philippine Bank Card'),
                  subtitle: const Text('Service fee: 3.2% + ₱30'),
                  value: 'philippine',
                  groupValue: _selectedCardType,
                  onChanged: (value) {
                    setState(() {
                      _selectedCardType = value;
                    });
                  },
                  activeColor: Constants.primaryColor,
                ),
                // International PHP Card Option
                RadioListTile<String>(
                  title: const Text('International Card (PHP-billed)'),
                  subtitle: const Text('Service fee: 4.2% + ₱30'),
                  value: 'international_php',
                  groupValue: _selectedCardType,
                  onChanged: (value) {
                    setState(() {
                      _selectedCardType = value;
                    });
                  },
                  activeColor: Constants.primaryColor,
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDivider() {
    return Container(
      height: 1,
      color: Colors.grey.shade200,
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Colors.green;
      case 'in process':
        return Colors.orange;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  double _calculateFee() {
    if (_selectedCashInMethod == null || _cashInAmount <= 0) {
      return 0.0;
    }

    double fee = 0.0;

    switch (_selectedCashInMethod) {
      case 'gcash':
      // GCash: 2.3% + ₱20
        fee = (_cashInAmount * 0.023) + 20.0;
        break;
      case 'card':
        if (_selectedCardType == 'philippine') {
          // Philippine Cards: 3.2% + ₱30
          fee = (_cashInAmount * 0.032) + 30.0;
        } else if (_selectedCardType == 'international_php') {
          // International Cards (PHP): 4.2% + ₱30
          fee = (_cashInAmount * 0.042) + 30.0;
        } else {
          // Default to Philippine rate if card type not selected
          fee = (_cashInAmount * 0.032) + 30.0;
        }
        break;
      case 'bank_transfer':
      case 'bpi':
      case 'bdo':
      case 'metrobank':
      case 'unionbank':
      case 'security_bank':
      case 'rcbc':
      case 'chinabank':
      // Bank Transfer: 1% + ₱20
        fee = (_cashInAmount * 0.01) + 20.0;
        break;
      default:
        fee = 20.0; // Default fee
        break;
    }

    return fee;
  }







  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('Tapp Balance'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
        onRefresh: _fetchBalanceAndTransactions,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Balance Section with Gradient
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Constants.primaryColor,
                      Constants.primaryColor.withOpacity(0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Constants.primaryColor.withOpacity(0.3),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Current Balance',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '₱${NumberFormat('#,##0.00').format(_currentBalance)}',
                      style: const TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),

              // Cash in Method Section
              const Text(
                'Cash in Method',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),

              // Amount Input Field
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: TextField(
                  controller: _amountController,
                  keyboardType: TextInputType.number,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                  ),
                  decoration: InputDecoration(
                    hintText: '₱ Enter amount (Min: ₱500)',
                    hintStyle: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade500,
                    ),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Payment Methods Container
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    _buildPaymentOption('GCash E-Wallet', 'gcash'),
                    _buildDivider(),
                    _buildCardOption(),
                    _buildDivider(),
                    _buildBankTransferOption(),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Fee Summary
              if (_selectedCashInMethod != null && _cashInAmount > 0) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Cash-in Amount:', style: TextStyle(fontSize: 14)),
                          Text('₱${NumberFormat('#,##0.00').format(_cashInAmount)}',
                              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500)),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Service Fee:', style: TextStyle(fontSize: 14)),
                          Text('₱${NumberFormat('#,##0.00').format(_calculateFee())}',
                              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: Colors.red)),
                        ],
                      ),
                      Divider(color: Colors.grey.shade300),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Total to Pay:', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                          Text('₱${NumberFormat('#,##0.00').format(_cashInAmount + _calculateFee())}',
                              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Constants.primaryColor)),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Cash in Button
              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: _selectedCashInMethod != null && _cashInAmount > 0 ? _cashIn : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Constants.primaryColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    'Cash In ₱${NumberFormat('#,##0.00').format(_cashInAmount)}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Security Notice
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.security, color: Colors.blue.shade600, size: 20),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Your payment is secured by Xendit with bank-level encryption',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.blue.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Transaction History Section
              const Text(
                'Transaction History',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: Colors.black),
              ),
              const SizedBox(height: 16),

              // Transaction List
              _transactions.isEmpty
                  ? Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Center(
                  child: Text(
                    'No transactions yet.',
                    style: TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                ),
              )
                  : ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _transactions.length,
                itemBuilder: (context, index) {
                  final transaction = _transactions[index];
                  return Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 5,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                transaction.description ?? 'Transaction',
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                DateFormat('MMM d, yyyy').format(transaction.transactionDate),
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              transaction.status,
                              style: TextStyle(
                                fontSize: 12,
                                color: _getStatusColor(transaction.status),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}