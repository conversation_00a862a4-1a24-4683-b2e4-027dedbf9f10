# Google Sign-In Error Fix - ApiException: 10

## Problem
The app was experiencing a Google Sign-In error with `PlatformException(sign_in_failed, com.google.android.gms.common.api.ApiException: 10: , null, null)`. Error code 10 indicates a `DEVELOPER_ERROR`, which typically means there's a configuration mismatch.

## Root Causes Identified

### 1. Missing Signing Configuration
The `android/app/build.gradle.kts` file was missing the signing configuration that references the keystore file.

### 2. Inconsistent Client IDs
Different parts of the codebase were using different Google OAuth client IDs:
- `GoogleAuthService`: `28340114852-ckvau2c2fpdhllml5v43rf07eofffssb.apps.googleusercontent.com`
- `AuthService`: `758079153152-loocnhnrakp60dscikq2p37q9nt5dalg.apps.googleusercontent.com`
- Backend: `723085540061-v5iheljkimtttiiadt3fqi5uc55qdpv6.apps.googleusercontent.com`

### 3. Incorrect Project Configuration
The configuration files referenced an old project (`hanapp-authentication`) instead of the current Firebase project (`hanappproject`).

## Fixes Applied

### 1. Added Signing Configuration
Updated `android/app/build.gradle.kts` to include proper signing configuration:

```kotlin
signingConfigs {
    getByName("debug") {
        keyAlias = "test"
        keyPassword = "loginhanapp"
        storeFile = file("login.jks")
        storePassword = "loginhanapp"
    }
}

buildTypes {
    getByName("debug") {
        signingConfig = signingConfigs.getByName("debug")
    }
    getByName("release") {
        signingConfig = signingConfigs.getByName("debug")
    }
}
```

### 2. Standardized Client IDs
Updated all services to use the correct client IDs from the Firebase project:

**GoogleAuthService** (`lib/services/google_auth_service.dart`):
```dart
static final GoogleSignIn _googleSignIn = GoogleSignIn(
  serverClientId: '758079153152-loocnhnrakp60dscikq2p37q9nt5dalg.apps.googleusercontent.com',
  scopes: ['email', 'profile'],
);
```

**Backend** (`hanapp_backend/api/social_login.php`):
```php
$googleClientId = '758079153152-loocnhnrakp60dscikq2p37q9nt5dalg.apps.googleusercontent.com';
```

**Configuration** (`lib/utils/google_signin_config.dart`):
```dart
static const String androidClientId = '758079153152-3ep62fkktfqqtrr9aq3jtuc5ba4u33eq.apps.googleusercontent.com';
static const String iosClientId = '758079153152-ktuo5f615qoerei6jqlnabrotve9lno6.apps.googleusercontent.com';
static const String webClientId = '758079153152-loocnhnrakp60dscikq2p37q9nt5dalg.apps.googleusercontent.com';
static const String serverClientId = '758079153152-loocnhnrakp60dscikq2p37q9nt5dalg.apps.googleusercontent.com';
```

### 3. Updated Project Configuration
Updated all references to use the correct Firebase project:
- Project ID: `hanappproject`
- Storage Bucket: `hanappproject.firebasestorage.app`
- Messaging Sender ID: `758079153152`

## Verification

### SHA-1 Fingerprint Verification
The keystore SHA-1 fingerprint (`09:3C:25:E6:92:B5:F7:25:5E:D7:83:16:53:B9:A0:C0:6C:DB:D8:E7`) matches the certificate hash in `google-services.json` (line 38: `093c25e692b5f7255ed7831653b9a0c06cdbd8e7`).

### Build Verification
The project now builds successfully with `flutter build apk --debug`.

## Testing Steps

1. **Clean and rebuild the project:**
   ```bash
   flutter clean
   flutter pub get
   flutter build apk --debug
   ```

2. **Install and test on device:**
   ```bash
   flutter install
   ```

3. **Test Google Sign-In flow:**
   - Open the app
   - Navigate to login screen
   - Tap "Sign in with Google"
   - Verify that the Google account selection dialog appears
   - Complete the sign-in process

## Additional Troubleshooting

If Google Sign-In still fails, check:

1. **Internet Connection**: Ensure the device has internet access
2. **Google Play Services**: Verify Google Play Services is installed and updated
3. **Firebase Console**: Confirm the SHA-1 fingerprint is correctly added to the Firebase project
4. **Client ID**: Double-check that the client ID matches the one in Firebase Console
5. **Package Name**: Verify the package name (`com.example.hanapp`) matches in all configurations

## Files Modified

1. `android/app/build.gradle.kts` - Added signing configuration
2. `lib/services/google_auth_service.dart` - Updated client ID
3. `hanapp_backend/api/social_login.php` - Updated client ID
4. `lib/utils/google_signin_config.dart` - Updated all client IDs and project configuration

## Next Steps

1. Test the Google Sign-In functionality on a physical device
2. Monitor for any additional authentication errors
3. Consider updating to the latest versions of Google Sign-In dependencies
4. Implement proper error handling for different sign-in scenarios
