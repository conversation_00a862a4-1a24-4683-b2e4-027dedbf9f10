import 'package:flutter/material.dart';
import 'package:hanapp/screens/lister/job_listing_screen.dart'; // Your existing job listings screen
import 'package:hanapp/screens/choose_listing_type_screen.dart'; // Corrected import: Screen to post new jobs
import 'package:hanapp/screens/profile_settings_screen.dart'; // Profile settings shared
import 'package:hanapp/screens/notifications_screen.dart'; // Notifications screen
import 'package:hanapp/screens/conversations_screen.dart'; // NEW: Import ConversationsScreen
import 'package:hanapp/screens/unified_chat_screen.dart'; // Unified chat screen
import 'package:hanapp/utils/constants.dart' as Constants; // For colors and padding
import 'package:hanapp/utils/auth_service.dart';
import 'package:hanapp/services/notification_service.dart'; // Add this import
import 'package:hanapp/models/user.dart';
import 'package:hanapp/services/notification_popup_service.dart';
import 'package:hanapp/models/video.dart';
import 'package:hanapp/services/video_service.dart';
import 'package:hanapp/models/announcement.dart';
import 'package:hanapp/services/announcement_service.dart';
import 'package:hanapp/screens/announcement_details_screen.dart';
import 'package:hanapp/screens/announcements_list_screen.dart';
import 'package:hanapp/widgets/video_container.dart';
import 'package:hanapp/widgets/inline_video_player.dart';
import 'package:hanapp/services/user_status_service.dart'; // Import user status service
import 'package:hanapp/utils/location_service.dart';
import 'package:hanapp/widgets/logout_dialog.dart'; // Add this import
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:hanapp/services/notification_popup_service.dart';
// Add timer import
import 'dart:async';

// Removed incorrect import - using doer-specific screens instead
import 'package:hanapp/screens/lister/lister_dashboard_screen.dart'; // Add this missing import

import 'doer_job_listings_mark_screen.dart';
import 'doer_job_listings_screen.dart'; // Import AuthService for logout
  // Add import
import '../../services/location_update_service.dart';
import 'package:hanapp/services/location_sync_service.dart';

import 'package:provider/provider.dart';
import 'package:hanapp/models/user.dart';

class DoerDashboardScreen extends StatefulWidget {
  const DoerDashboardScreen({super.key});

  @override
  State<DoerDashboardScreen> createState() => _DoerDashboardScreenState();
}

class _DoerDashboardScreenState extends State<DoerDashboardScreen> {
  int _selectedIndex = 0; // To control BottomNavigationBar
  int _previousIndex = 0; // Track previous index for back navigation
  int _unreadCount = 0; // Add unread count state
  Timer? _roleCheckTimer; // Timer for polling

  // List of screens for the Doer role's Bottom Navigation Bar
  static final List<Widget> _ownerScreens = <Widget>[
    _DoerHomeScreenContent(), // Index 0: Home tab content
    const DoerJobListingsScreen(), // Index 1: Jobs tab content - using doer-specific screen
    Builder(
  builder: (context) => NotificationsScreen(
    userRole: Provider.of<User>(context).role,
  ),
),
    const ProfileSettingsScreen(), // Index 3: Profile tab content
  ];

  // Titles corresponding to each screen/tab
  static const List<String> _screenTitles = <String>[
    'Doer',
    'Job Listings',
    'Notifications',
    'Profile',
  ];

  @override
  void initState() {
    super.initState();
    _loadUnreadCount();
    // Set the callback for navigating to notifications
    NotificationPopupService().setNavigateToNotificationsCallback(() {
      print('DEBUG: Notification callback triggered, current index: $_selectedIndex, navigating to notifications');
      setState(() {
        _previousIndex = _selectedIndex; // Store current index before navigating
        _selectedIndex = 2; // Navigate to notifications tab
      });
      print('DEBUG: After setState, new index: $_selectedIndex, title should be: ${_screenTitles[_selectedIndex]}');
      _loadUnreadCount(); // Refresh unread count when navigating to notifications
    });
    // Start notification polling for dashboard
    print('DEBUG: DoerDashboardScreen - Starting notification polling service');
    NotificationPopupService().startPolling(context);
    _verifyUserStatus();
    _startRolePolling();
    
    // Add initial poll with delay to ensure context is ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      print('DEBUG: DoerDashboardScreen - Starting notification polling service (post frame callback)');
      NotificationPopupService().startPolling(context);
    });
  }

  @override
  void dispose() {
    print('DEBUG: DoerDashboardScreen - Stopping notification polling service');
    NotificationPopupService().stopPolling();
    _roleCheckTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadUnreadCount() async {
    final user = await AuthService.getUser();
    if (user != null && user.id != null) {
      final response = await NotificationService().getUnreadCount(userId: user.id!);
      if (response['success']) {
        setState(() {
          _unreadCount = response['unread_count'] ?? 0;
        });
      }
    }
  }

  Future<void> _verifyUserStatus() async {
    final user = await AuthService.getUser();
    if (user != null && user.id != null) {
      // First check for role mismatch
      final roleUpdated = await UserStatusService.checkAndUpdateRoleMismatch(
        context: context,
        userId: user.id!,
        showUpdateDialog: true,
      );
      
      // If role was updated, we don't need to do further verification
      if (roleUpdated) {
        return;
      }
      
      // Otherwise, proceed with normal user status verification
      await UserStatusService.verifyUserStatusWithInterval(
        context: context,
        userId: user.id!,
        action: 'check',
        forceCheck: false,
      );
    }
  }

  void _startRolePolling() {
    _roleCheckTimer?.cancel();
    _roleCheckTimer = Timer.periodic(const Duration(minutes: 2), (timer) async {
      final user = await AuthService.getUser();
      if (user != null && user.id != null) {
        await UserStatusService.checkAndUpdateRoleMismatch(
          context: context,
          userId: user.id!,
          showUpdateDialog: false,
        );
      }
    });
  }

  void _onItemTapped(int index) {
    // Adjust index for _ownerScreens list as BottomAppBar has a FAB space
    int actualScreenIndex = index;
    if (index == 3) { // Notifications icon (index 3 in BottomAppBar row)
      actualScreenIndex = 2; // Corresponds to NotificationsScreen in _ownerScreens
      _loadUnreadCount(); // Refresh unread count when notifications tab is tapped
      // Refresh notification service context when switching to notifications
      print('DEBUG: Switching to notifications tab, refreshing notification service context');
      NotificationPopupService().startPolling(context);
    } else if (index == 4) { // Profile icon (index 4 in BottomAppBar row)
      actualScreenIndex = 3; // Corresponds to ProfileSettingsScreen in _ownerScreens
    }

    setState(() {
      _previousIndex = _selectedIndex; // Store current index before navigating
      _selectedIndex = actualScreenIndex;
    });
    
    // Always refresh the notification service context when switching tabs
    print('DEBUG: Tab switched to index $actualScreenIndex, refreshing notification service context');
    NotificationPopupService().startPolling(context);
  }

  Future<void> _logout() async {
    // Pause the video if it is playing
    final videoPlayerState = _DoerHomeScreenContent.videoPlayerKey.currentState;
    if (videoPlayerState != null && mounted) {
      videoPlayerState.pauseVideo();
    }
    
    final shouldLogout = await showLogoutDialog(context);
    if (shouldLogout && mounted) {
      Navigator.of(context).pushNamedAndRemoveUntil(
        '/login',
        (route) => false, // Remove all previous routes
      );
    }
  }

  Widget _buildNotificationIcon() {
    return Stack(
      children: [
        Icon(
          Icons.notifications,
          color: _selectedIndex == 2 ? Colors.yellow.shade700 : Colors.white70,
        ),
        if (_unreadCount > 0)
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(10),
              ),
              constraints: const BoxConstraints(
                minWidth: 16,
                minHeight: 16,
              ),
              child: Text(
                _unreadCount > 99 ? '99+' : _unreadCount.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    print('DEBUG: Building AppBar with selectedIndex: $_selectedIndex, title: ${_screenTitles[_selectedIndex]}');
    return Scaffold(
      appBar: AppBar(
        title: Text(_screenTitles[_selectedIndex]), // Dynamic title based on selected tab
        backgroundColor: Colors.white, // Consistent app bar color
        foregroundColor: Constants.primaryColor, // White icons/text for app bar
        leading: (_selectedIndex == 2 || _selectedIndex == 3) ? IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            setState(() {
              _selectedIndex = _previousIndex; // Return to previous screen
            });
          },
        ) : null,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh), // Refresh notifications icon
            onPressed: () {
              print('DEBUG: Manual notification refresh triggered');
              NotificationPopupService().startPolling(context);
              _loadUnreadCount();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Refreshing notifications...'),
                  duration: Duration(seconds: 1),
                ),
              );
            },
            tooltip: 'Refresh Notifications',
          ),
          IconButton(
            icon: const Icon(Icons.chat_bubble_outline), // Chat icon
            onPressed: () {
              // Navigate to the new ConversationsScreen
              Navigator.of(context).pushNamed('/chat_list');
            },
            tooltip: 'Chats',
          ),
        ],
      ),
      body: _ownerScreens.elementAt(_selectedIndex), // Display the selected screen content
      bottomNavigationBar: BottomAppBar(
        color: Constants.primaryColor, // Consistent bottom app bar color
        shape: const CircularNotchedRectangle(), // Shape for FAB notch
        notchMargin: 8.0, // Margin for FAB notch
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: <Widget>[
            IconButton(
              icon: const Icon(Icons.home),
              color: _selectedIndex == 0 ? Colors.yellow.shade700 : Colors.white70,
              onPressed: () => _onItemTapped(0),
            ),
            IconButton(
              icon: const Icon(Icons.list_alt), // Jobs tab icon
              color: _selectedIndex == 1 ? Colors.yellow.shade700 : Colors.white70,
              onPressed: () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => const DoerJobListingsScreenMark()));
              },
            ),
            const SizedBox(width: 48), // Space for the Floating Action Button
            IconButton(
              icon: _buildNotificationIcon(), // Use the custom notification icon with badge
              onPressed: () => _onItemTapped(3), // Pass original index for logic
            ),
            IconButton(
              icon: const Icon(Icons.person),
              color: _selectedIndex == 3 ? Colors.yellow.shade700 : Colors.white70, // Corrected index for Profile
              onPressed: () => _onItemTapped(4), // Pass original index for logic
            ),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Navigate to doer job listings screen
          Navigator.push(context, MaterialPageRoute(builder: (context) => const DoerJobListingsScreen()));
        },
        backgroundColor: Constants.primaryColor, // HANAPP Blue
        shape: const CircleBorder(),
        child: const Icon(Icons.search, color: Colors.white, size: 35), // Search icon for job listings
      ),
    );
  }
}

// --- Updated Widget for the Doer Home Screen Content with new design ---
class _DoerHomeScreenContent extends StatefulWidget {
  static final GlobalKey<InlineVideoPlayerState> videoPlayerKey = GlobalKey<InlineVideoPlayerState>();
  @override
  State<_DoerHomeScreenContent> createState() => _DoerHomeScreenContentState();
}

class _DoerHomeScreenContentState extends State<_DoerHomeScreenContent> {
  List<Video> _videos = [];
  List<Announcement> _announcements = [];
  bool _isLoadingVideos = false;
  bool _isLoadingAnnouncements = false;
  final VideoService _videoService = VideoService();
  final AnnouncementService _announcementService = AnnouncementService();
  
  // New variables for location and role
  String _userLocation = 'Getting location...';
  bool _isLoadingLocation = true;
  User? _currentUser;
  bool _isLoadingRoleSwitch = false;
  final LocationService _locationService = LocationService();
  final AuthService _authService = AuthService();

  // In _DoerHomeScreenContentState class:
  Timer? _locationUpdateTimer;
  
  @override
  void initState() {
    super.initState();
    print('DEBUG: Doer Home Screen initState called');
    _loadVideos();
    _loadAnnouncements();
    _loadCurrentUser();
    _getCurrentLocation();
    
    // Update location every 5 minutes when doer is available
    _startLocationUpdates();
  }
  
  void _startLocationUpdates() {
  _locationUpdateTimer = Timer.periodic(Duration(seconds: 15), (timer) {
    if (_currentUser?.role == 'doer' && _currentUser?.isAvailable == true) {
      _getCurrentLocation();
    }
  });
}
  
  @override
  void dispose() {
    _locationUpdateTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadCurrentUser() async {
    final user = await AuthService.getUser();
    setState(() {
      _currentUser = user;
    });
  }

  
  // In _DoerHomeScreenContentState class, modify _getCurrentLocation method:
  Future<void> _getCurrentLocation() async {
    try {
      setState(() {
        _isLoadingLocation = true;
      });
  
      Position? position = await _locationService.getCurrentLocation();
      if (position != null) {
        // Update location using LocationSyncService to broadcast changes
        if (_currentUser != null) {
          List<Placemark> placemarks = await placemarkFromCoordinates(
            position.latitude,
            position.longitude,
          );
          
          String address = '';
          if (placemarks.isNotEmpty) {
            Placemark place = placemarks[0];
            
            if (place.locality != null && place.locality!.isNotEmpty) {
              address += place.locality!;
            }
            if (place.administrativeArea != null && place.administrativeArea!.isNotEmpty) {
              if (address.isNotEmpty) address += ', ';
              address += place.administrativeArea!;
            }
            if (place.country != null && place.country!.isNotEmpty) {
              if (address.isNotEmpty) address += ', ';
              address += place.country!;
            }
          }
          
          // Use LocationSyncService to update and broadcast location
          await LocationSyncService.instance.updateLocation(
            userId: _currentUser!.id,
            latitude: position.latitude,
            longitude: position.longitude,
            addressDetails: address.isNotEmpty ? address : null,
          );
          
          setState(() {
            _userLocation = address.isNotEmpty ? address : 'Location unavailable';
            _isLoadingLocation = false;
          });
        } else {
          setState(() {
            _userLocation = 'Location unavailable';
            _isLoadingLocation = false;
          });
        }
      } else {
        setState(() {
          _userLocation = 'Location unavailable';
          _isLoadingLocation = false;
        });
      }
    } catch (e) {
      print('Error getting location: $e');
      setState(() {
        _userLocation = 'Location unavailable';
        _isLoadingLocation = false;
      });
    }
  }

  Future<void> _switchRole() async {
    if (_currentUser == null || _isLoadingRoleSwitch) return;
    
    setState(() {
      _isLoadingRoleSwitch = true;
    });

    try {
      String newRole = (_currentUser!.role == 'doer') ? 'lister' : 'doer';
      
      // Single API call that handles both role update and user data refresh
      final response = await _authService.updateRole(
        userId: _currentUser!.id.toString(), 
        role: newRole
      );

      if (response['success']) {
        // The updateRole method already updates local user data
        // Just reload to ensure UI consistency
        await _loadCurrentUser();
        
        // Navigate to the appropriate dashboard based on the NEW role
        if (mounted && _currentUser!.role == 'lister') {
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const ListerDashboardScreen()),
            (Route<dynamic> route) => false,
          );
        }
      } else {
        _showErrorSnackBar('Failed to switch role: ${response['message']}');
      }
    } catch (e) {
      _showErrorSnackBar('Error switching role: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingRoleSwitch = false;
        });
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Widget _buildLocationHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 5.0),
      decoration: BoxDecoration(
        color: Constants.primaryColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.location_on,
            color: Colors.white,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _isLoadingLocation
                ? const Text(
                    'Getting location...',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  )
                : Text(
                    _userLocation,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoleToggle() {
    if (_currentUser == null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(25),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: _currentUser!.role != 'lister' ? _switchRole : null,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              decoration: BoxDecoration(
                color: _currentUser!.role == 'lister' ? Colors.black : Colors.transparent,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                'LISTER',
                style: TextStyle(
                  color: _currentUser!.role == 'lister' ? Colors.white : Colors.grey[600],
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
          ),
          const SizedBox(width: 4),
          GestureDetector(
            onTap: _currentUser!.role != 'doer' ? _switchRole : null,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              decoration: BoxDecoration(
                color: _currentUser!.role == 'doer' ? Colors.black : Colors.transparent,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                'DOER',
                style: TextStyle(
                  color: _currentUser!.role == 'doer' ? Colors.white : Colors.grey[600],
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
          ),
          if (_isLoadingRoleSwitch) ...[
            const SizedBox(width: 8),
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildHanAppLogo() {
    return Center(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        child: Column(
          children: [
            Text(
              'HAN',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Constants.primaryColor,
                letterSpacing: 2,
              ),
            ),
            Text(
              'APP',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Constants.primaryColor,
                letterSpacing: 2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _loadVideos() async {
    try {
      setState(() {
        _isLoadingVideos = true;
      });

      // Get video announcements from cms_ads table
      final announcements = await _announcementService.getActiveVideos();
      
      // Filter announcements that have valid video URLs
      final validVideoAnnouncements = announcements.where((announcement) => 
        announcement.videoUrl != null && 
        announcement.videoUrl!.isNotEmpty
      ).toList();
      
      // Convert valid announcements to videos for compatibility with existing video player
      final videos = validVideoAnnouncements.map((announcement) => Video(
        id: announcement.id,
        title: announcement.title,
        description: announcement.description,
        link: announcement.link,
        imagePath: announcement.imagePath,
        videoPath: announcement.videoPath,
        videoUrl: announcement.videoUrl,
        category: announcement.category,
        isActive: announcement.isActive,
        createdAt: announcement.createdAt,
        updatedAt: announcement.updatedAt,
      )).toList();
      
      setState(() {
        _videos = videos;
        _isLoadingVideos = false;
      });
    } catch (e) {
      print('Error loading videos: $e');
      setState(() {
        _isLoadingVideos = false;
      });
    }
  }

  Future<void> _loadAnnouncements() async {
    try {
      setState(() {
        _isLoadingAnnouncements = true;
      });

      final announcements = await _announcementService.getActiveAnnouncements();
      
      setState(() {
        _announcements = announcements;
        _isLoadingAnnouncements = false;
      });
    } catch (e) {
      print('Error loading announcements: $e');
      setState(() {
        _isLoadingAnnouncements = false;
      });
    }
  }

  Widget _buildAnnouncementArea() {
    if (_isLoadingAnnouncements) {
      return Card(
        margin: const EdgeInsets.only(bottom: 24.0),
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Container(
          height: 120,
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    final announcementCount = _announcements.length;
    final countText = announcementCount == 0 
        ? 'No announcements available'
        : announcementCount == 1 
            ? '1 announcement available'
            : '$announcementCount announcements available';

    return Card(
      margin: const EdgeInsets.only(bottom: 24.0),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: announcementCount > 0 ? () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AnnouncementsListScreen(),
            ),
          );
        } : null,
        child: Container(
          height: 120,
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'Announcement Area',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                countText,
                style: TextStyle(
                  fontSize: 14,
                  color: announcementCount > 0 ? Colors.blue : Colors.grey,
                  fontWeight: announcementCount > 0 ? FontWeight.w500 : FontWeight.normal,
                ),
              ),
              if (announcementCount > 0) ...[
                const SizedBox(height: 8),
                const Row(
                  children: [
                    Text(
                      'Tap to view all announcements',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                    SizedBox(width: 4),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 12,
                      color: Colors.grey,
                    ),
                  ],
                ),
              ]
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVideoContent() {
    if (_isLoadingVideos) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.white),
            SizedBox(height: 16),
            Text(
              'Loading videos...',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
      );
    }

    if (_videos.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.play_circle_fill, size: 60, color: Colors.white70),
            SizedBox(height: 16),
            Text(
              'Auto video play, promotions and tutorial',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // Show the first video with autoplay and pass all videos for sequential playback
    return InlineVideoPlayer(
      key: _DoerHomeScreenContent.videoPlayerKey,
      video: _videos.first,
      videos: _videos, // Pass the entire list for sequential playback
      autoplay: false, // Changed from true to false
      height: 168, // 200 - 32 (padding)
      width: double.infinity,
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: Constants.screenPadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // User Location Header
          _buildLocationHeader(),
          const SizedBox(height: 16),
          
          // Role Toggle
          Center(child: _buildRoleToggle()),
          const SizedBox(height: 16),

          // "Need Jobs?" card
          // HanApp Logo Display
          Container(
            margin: const EdgeInsets.symmetric(vertical: 24.0, horizontal: 16.0),
            child: Image.asset(
              'assets/2.png',
              fit: BoxFit.contain,
              height: 140,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  height: 120,
                  color: Colors.grey[300],
                  child: const Center(
                    child: Icon(Icons.image, size: 50, color: Colors.grey),
                  ),
                );
              },
            ),
          ),
                    
          // Announcement/Promo Code Area
          _buildAnnouncementArea(),

          // Auto video play, promotions and tutorial
          Card(
            margin: const EdgeInsets.only(bottom: 24.0),
            elevation: 4,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Container(
              height: 200,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(12),
              ),
              child: _buildVideoContent(),
            ),
          ),
        ],
      ),
    );
  }
}