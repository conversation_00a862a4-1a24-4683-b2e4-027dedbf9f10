import 'package:flutter/material.dart';
import '../utils/constants.dart';
import 'package:hanapp/models/user.dart'; // Your User model
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/services/review_service.dart';
import 'package:hanapp/models/user_review.dart';
import 'package:intl/intl.dart'; // For date formatting
import 'package:hanapp/services/user_service.dart'; // Import UserService
import 'package:hanapp/utils/image_utils.dart'; // Import ImageUtils
import 'package:hanapp/services/chat_service.dart'; // ADD: Import ChatService
import 'package:hanapp/screens/chat_screen.dart'; // ADD: Import ChatScreen
import 'package:hanapp/utils/auth_service.dart'; // ADD: Import AuthService

class ViewProfileScreen extends StatefulWidget {
  final int userId; // The ID of the user whose profile is being viewed

  const ViewProfileScreen({super.key, required this.userId});

  @override
  State<ViewProfileScreen> createState() => _ViewProfileScreenState();
}

class _ViewProfileScreenState extends State<ViewProfileScreen> {
  User? _profileUser; // The user whose profile is being viewed
  User? _currentUser; // Add missing current user variable
  bool _isLoadingProfile = true;
  String? _profileErrorMessage;

  List<Review> _reviews = [];
  bool _isLoadingReviews = false;
  String? _reviewsErrorMessage;
  double _averageRating = 0.0;
  int _totalReviews = 0;
  
  // Real-time availability status
  bool? _realTimeAvailability;
  bool _isLoadingAvailability = false;

  final UserService _userService = UserService();
  final ReviewService _reviewService = ReviewService();
  final ChatService _chatService = ChatService(); // ADD: Declare ChatService instance

  @override
  void initState() {
    super.initState();
    _fetchUserProfileAndReviews();
    _loadCurrentUser(); // Load current user data
  }

  Future<void> _loadCurrentUser() async {
    // ADD: Proper current user loading
    try {
      _currentUser = await AuthService.getUser();
    } catch (e) {
      debugPrint('Error loading current user: $e');
    }
  }

  Future<void> _fetchUserProfileAndReviews() async {
    setState(() {
      _isLoadingProfile = true;
      _isLoadingReviews = true;
      _profileErrorMessage = null;
      _reviewsErrorMessage = null;
    });

    try {
      // --- Fetch User Profile ---
      final userResponse = await _userService.getUserProfile(widget.userId);
      if (userResponse['success']) {
        _profileUser = userResponse['user'];
        // Fetch real-time availability for doers
        if (_profileUser?.role == 'doer') {
          _fetchRealTimeAvailability();
        }
      } else {
        _profileErrorMessage = userResponse['message'] ?? 'Failed to load user profile.';
        _profileUser = null; // Ensure user is null if fetching fails
      }

      // --- Fetch Reviews for the profile user ---
      final reviewResponse = await _reviewService.getReviewsForUser(userId: widget.userId);
      if (reviewResponse['success']) {
        _reviews = reviewResponse['reviews'];
        _calculateOverallRating(); // Calculate average and total from fetched reviews
      } else {
        _reviews = []; // Clear reviews on error
        _reviewsErrorMessage = reviewResponse['message'] ?? 'Failed to load reviews.';
        if (_reviewsErrorMessage!.contains('Empty response')) { // Refine message for empty responses
          _reviewsErrorMessage = 'No reviews yet for this user.';
        }
      }
    } catch (e) {
      debugPrint('Error fetching profile or reviews: $e');
      _profileErrorMessage = 'Network error loading profile: ${e.toString()}';
      _reviewsErrorMessage = 'Network error loading reviews: ${e.toString()}';
    } finally {
      setState(() {
        _isLoadingProfile = false;
        _isLoadingReviews = false;
      });
    }
  }

  void _calculateOverallRating() {
    if (_reviews.isEmpty) {
      _averageRating = 0.0;
      _totalReviews = 0;
      return;
    }

    double sumRatings = 0.0;
    for (var review in _reviews) {
      sumRatings += review.rating;
    }
    _averageRating = sumRatings / _reviews.length;
    _totalReviews = _reviews.length;
  }

  Future<void> _fetchRealTimeAvailability() async {
    if (_profileUser?.role != 'doer') {
      return; // Only fetch for doers
    }

    setState(() {
      _isLoadingAvailability = true;
    });

    try {
      final response = await _userService.getAvailabilityStatus(widget.userId);
      if (response['success']) {
        setState(() {
          _realTimeAvailability = response['is_available'];
        });
      } else {
        debugPrint('Failed to fetch real-time availability: ${response['message']}');
      }
    } catch (e) {
      debugPrint('Error fetching real-time availability: $e');
    } finally {
      setState(() {
        _isLoadingAvailability = false;
      });
    }
  }

  String _formatLocationForDisplay(String? fullAddress) {
    if (fullAddress == null || fullAddress.isEmpty) {
      return 'Address not available';
    }
    
    // Split the address by commas and trim whitespace
    List<String> addressParts = fullAddress.split(',').map((part) => part.trim()).toList();
    
    // Show only the last three parts of the address
    if (addressParts.length > 3) {
      List<String> lastThreeParts = addressParts.sublist(addressParts.length - 3);
      return lastThreeParts.join(', ');
    }
    
    // If address has 3 or fewer parts, return as is
    return fullAddress;
  }

  Future<void> _startChat(int targetUserId, String targetUserFullName) async {
    if (_currentUser == null || _currentUser!.id == null) {
      _showSnackBar('Error: Current user not logged in.', isError: true);
      return;
    }

    // Prevent self-chatting
    if (_currentUser!.id == targetUserId) {
      _showSnackBar('You cannot chat with yourself.', isError: true);
      return;
    }

    setState(() {
      // Show loading state while creating/getting conversation
    });

    try {
      // Create or get conversation for direct profile chat
      // For direct profile chats, we'll use a special listing ID of 0 to indicate it's a direct chat
      final response = await _chatService.createOrGetConversation(
        listingId: 0, // Special ID for direct profile chats
        listingType: 'DIRECT', // Special type for direct chats
        listerId: _currentUser!.role == 'lister' ? _currentUser!.id! : targetUserId,
        doerId: _currentUser!.role == 'doer' ? _currentUser!.id! : targetUserId,
      );

      if (response['success']) {
        final int conversationId = response['conversation_id'];
        
        _showSnackBar('Chat initiated!');
        if (mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => ChatScreen(
                conversationId: conversationId, // Use the actual conversation ID
                otherUserId: targetUserId,
                listingTitle: 'Direct Chat with ${targetUserFullName}',
                applicationId: 0, // No specific application for direct profile chat
                isLister: _currentUser!.role == 'lister', // Determine based on current user's role
              ),
            ),
          );
        }
      } else {
        _showSnackBar('Failed to start chat: ${response['message']}', isError: true);
      }
    } catch (e) {
      if (e is FormatException) {
        _showSnackBar('Server sent invalid data. Check your backend for errors.', isError: true);
      } else {
        _showSnackBar('Network error: $e', isError: true);
      }
      debugPrint('Error starting chat: $e');
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isError ? Icons.error_outline : Icons.check_circle_outline,
              color: Colors.white,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: isError ? Colors.red : Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(10),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoadingProfile || _profileUser == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('View Profile'), backgroundColor: Constants.primaryColor, foregroundColor: Colors.white),
        body: Center(
          child: _profileErrorMessage != null
              ? Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              _profileErrorMessage!,
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
          )
              : const CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('View Profile'),
        backgroundColor: Constants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(24.0),
              width: double.infinity,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black12,
                    blurRadius: 10,
                    offset: Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 60,
                    backgroundImage: ImageUtils.createProfileImageProvider(_profileUser!.profilePictureUrl),
                    backgroundColor: Constants.lightGreyColor,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _profileUser!.fullName,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Constants.textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _formatLocationForDisplay(_profileUser!.addressDetails),
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    // Ensure createdAt is not null before formatting
                    'Started on: ${DateFormat('MMM dd, yyyy').format(_profileUser!.createdAt ?? DateTime.now())}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        _averageRating.toStringAsFixed(1),
                        style: const TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const Icon(Icons.star, color: Colors.amber, size: 28),
                      Text(
                        ' (${_totalReviews})',
                        style: const TextStyle(
                          fontSize: 18,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  // Filter buttons for reviews
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _buildFilterButton('All', _reviews.length),
                        _buildFilterButton('5', _reviews.where((r) => r.rating == 5).length),
                        _buildFilterButton('4', _reviews.where((r) => r.rating == 4).length),
                        _buildFilterButton('3', _reviews.where((r) => r.rating == 3).length),
                        _buildFilterButton('2', _reviews.where((r) => r.rating == 2).length),
                        _buildFilterButton('1', _reviews.where((r) => r.rating == 1).length),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  // --- Role, Verification, and Status Row ---
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Chip(
                        label: Text(_profileUser!.role.toUpperCase()),
                        avatar: Icon(_profileUser!.role == 'doer' ? Icons.handyman : Icons.person, size: 18),
                        backgroundColor: Colors.blue.shade50,
                      ),
                      const SizedBox(width: 8),
                      if (_profileUser!.isVerified)
                        Tooltip(
                          message: 'Email Verified',
                          child: const Icon(Icons.verified, color: Colors.green, size: 22),
                        ),
                      if (_profileUser!.isIdVerified)
                        Tooltip(
                          message: 'ID Verified',
                          child: const Icon(Icons.verified_user, color: Colors.blue, size: 22),
                        ),
                      if (_profileUser!.isBadgeAcquired)
                        Tooltip(
                          message: 'Badge Acquired',
                          child: const Icon(Icons.workspace_premium, color: Colors.amber, size: 22),
                        ),
                      if (_profileUser!.role == 'doer')
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: _isLoadingAvailability
                              ? const SizedBox(
                                  width: 80,
                                  height: 32,
                                  child: Center(
                                    child: SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(strokeWidth: 2),
                                    ),
                                  ),
                                )
                              : Chip(
                                  label: Text((_realTimeAvailability ?? _profileUser!.isAvailable ?? false) ? 'Available' : 'Unavailable'),
                                  backgroundColor: (_realTimeAvailability ?? _profileUser!.isAvailable ?? false) ? Colors.green.shade50 : Colors.red.shade50,
                                  avatar: Icon(
                                    (_realTimeAvailability ?? _profileUser!.isAvailable ?? false) ? Icons.check_circle : Icons.cancel,
                                    color: (_realTimeAvailability ?? _profileUser!.isAvailable ?? false) ? Colors.green : Colors.red,
                                    size: 18,
                                  ),
                                ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // --- Action Buttons ---
                  // Remove the Connect button section
                  // Padding(
                  //   padding: const EdgeInsets.symmetric(vertical: 8.0),
                  //   child: SizedBox(
                  //     width: double.infinity,
                  //     child: ElevatedButton.icon(
                  //       onPressed: () {
                  //         _startChat(
                  //           _profileUser!.id!,
                  //           _profileUser!.fullName,
                  //         );
                  //       },
                  //       icon: const Icon(Icons.chat, size: 18),
                  //       label: const Text('Connect'),
                  //       style: ElevatedButton.styleFrom(
                  //         backgroundColor: Constants.primaryColor,
                  //         foregroundColor: Colors.white,
                  //         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  //         padding: const EdgeInsets.symmetric(vertical: 12),
                  //       ),
                  //     ),
                  //   ),
                  // ),
                ],
              ),
            ),
            // Reviews Section
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Reviews',
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: Constants.textColor,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _isLoadingReviews
                      ? const Center(child: CircularProgressIndicator())
                      : _reviewsErrorMessage != null
                      ? Center(
                    child: Text(
                      _reviewsErrorMessage!,
                      style: const TextStyle(color: Colors.red),
                      textAlign: TextAlign.center,
                    ),
                  )
                      : _reviews.isEmpty
                      ? const Center(
                    child: Text(
                      'No reviews yet.',
                      style: TextStyle(fontSize: 16, color: Colors.grey),
                    ),
                  )
                      : ListView.builder(
                    physics: const NeverScrollableScrollPhysics(), // To allow parent SingleChildScrollView to scroll
                    shrinkWrap: true,
                    itemCount: _reviews.length,
                    itemBuilder: (context, index) {
                      final review = _reviews[index];
                      return _buildReviewCard(review);
                    },
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterButton(String label, int count) {
    // Basic styling for filter buttons
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0),
      child: OutlinedButton(
        onPressed: () {
          // Implement filtering logic here based on 'label'
          _showSnackBar('Filtering by $label (${count}) - (Coming Soon)');
        },
        style: OutlinedButton.styleFrom(
          foregroundColor: Constants.primaryColor,
          side: const BorderSide(color: Constants.primaryColor),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
        child: Text(
          '$label ($count)',
          style: const TextStyle(fontSize: 14),
        ),
      ),
    );
  }

  Widget _buildReviewCard(Review review) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12.0),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundImage: ImageUtils.createProfileImageProvider(review.listerProfilePictureUrl),
                  backgroundColor: Colors.grey.shade200,
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        review.listerFullName,
                        style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                      ),
                      Row(
                        children: List.generate(5, (index) {
                          return Icon(
                            index < review.rating ? Icons.star : Icons.star_border,
                            color: Colors.amber,
                            size: 18,
                          );
                        }),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        DateFormat('MMM d, yyyy').format(review.createdAt), // Use reviewedAt for review date
                        style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (review.reviewContent.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  review.reviewContent,
                  style: TextStyle(fontSize: 14, color: Constants.textColor.withOpacity(0.9)),
                ),
              ),
            // NEW: Display review images if available
            if (review.reviewImageUrls.isNotEmpty) ...[
              const SizedBox(height: 12),
              SizedBox(
                height: 80, // Fixed height for image scroll view
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: review.reviewImageUrls.length,
                  itemBuilder: (context, index) {
                    final imageUrl = review.reviewImageUrls[index];
                    return Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8.0),
                        child: Image.network(
                          imageUrl,
                          width: 80,
                          height: 80,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 80,
                              height: 80,
                              color: Colors.grey.shade300,
                              child: const Icon(Icons.broken_image, color: Colors.grey),
                              alignment: Alignment.center,
                            );
                          },
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
            // Display Doer's reply if available
            if (review.doerReplyMessage != null && review.doerReplyMessage!.isNotEmpty) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Doer\'s Reply:', // Indicate it's the Doer's reply
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Constants.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      review.doerReplyMessage!,
                      style: const TextStyle(fontSize: 14, color: Colors.black54),
                    ),
                    if (review.repliedAt != null)
                      Text(
                        'Replied on: ${DateFormat('MMM d, yyyy').format(review.repliedAt!)}',
                        style: const TextStyle(fontSize: 11, color: Colors.grey),
                      ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
