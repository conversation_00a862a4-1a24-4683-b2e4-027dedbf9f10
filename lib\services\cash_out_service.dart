import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:hanapp/utils/api_config.dart';

class CashOutService {
  /// Create a cash-out (withdrawal) request via backend
  Future<Map<String, dynamic>> createCashOutRequest({
    required int userId,
    required double amount, // Net amount user will receive
    required double requestedAmount, // Original amount user requested (before fees)
    required double serviceFee, // Service fee charged for the withdrawal
    required String currency, // Currency of withdrawal ('PHP' or 'USD')
    required String withdrawalMethod,
    required String accountDetails,
  }) async {
    try {
      print('CashOutService: Creating cash-out request for user $userId, requested amount ₱$requestedAmount, net amount $amount $currency, service fee ₱$serviceFee PHP');

      final response = await http.post(
        Uri.parse(ApiConfig.cashOutEndpoint),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'user_id': userId,
          'amount': amount,
          'requested_amount': requestedAmount,
          'service_fee': serviceFee,
          'currency': currency,
          'withdrawal_method': withdrawalMethod,
          'account_details': accountDetails,
        }),
      );

      print('CashOutService: Response status code: ${response.statusCode}');
      print('CashOutService: Response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);

        if (responseData['success'] == true) {
          return {
            'success': true,
            'message': responseData['message'],
            'transaction_details': responseData['transaction_details'],
          };
        } else {
          return {
            'success': false,
            'message': responseData['message'] ?? 'Cash-out request failed',
          };
        }
      } else {
        return {
          'success': false,
          'message': 'Server error: ${response.statusCode}',
        };
      }
    } catch (e) {
      print('CashOutService: Error creating cash-out request: $e');
      return {
        'success': false,
        'message': 'Network error: ${e.toString()}',
      };
    }
  }

  /// Validate cash-out amount
  static bool isValidAmount(double amount) {
    return amount >= 200 && amount <= 100000; // Min ₱200, Max ₱100,000
  }

  /// Get minimum withdrawal amount
  static double getMinimumAmount() {
    return 200.0;
  }

  /// Get maximum withdrawal amount
  static double getMaximumAmount() {
    return 100000.0;
  }

  /// Format withdrawal method display name
  static String formatWithdrawalMethod(String method) {
    switch (method) {
      case 'Bank Transfer':
        return 'Bank Transfer';
      case 'GCash':
        return 'GCash';
      case 'Maya':
        return 'Maya';
      case 'Credit Card':
        return 'Credit/Debit Card';
      default:
        return method;
    }
  }

  /// Validate account details based on withdrawal method
  static bool isValidAccountDetails(String method, String accountDetails) {
    if (accountDetails.trim().isEmpty) return false;

    switch (method) {
      case 'GCash':
      case 'Maya':
      // Should be a valid Philippine mobile number
        final phoneRegex = RegExp(r'^(09|\+639)\d{9}$');
        return phoneRegex.hasMatch(accountDetails.replaceAll(RegExp(r'[\s\-\(\)]'), ''));

      case 'Credit Card':
      // Should be a valid card number (basic validation)
        final cardRegex = RegExp(r'^\d{13,19}$');
        return cardRegex.hasMatch(accountDetails.replaceAll(RegExp(r'[\s\-]'), ''));

      case 'Bank Transfer':
      // Should have bank name prefix and account number
      // Format: "BPI Direct Debit - **********"
        if (accountDetails.contains(' - ')) {
          final parts = accountDetails.split(' - ');
          if (parts.length == 2) {
            final bankName = parts[0].trim();
            final accountNumber = parts[1].trim();
            // Check if bank name is valid and account number is at least 8 digits
            final validBanks = ['BPI Direct Debit', 'China Bank Direct Debit', 'RCBC Direct Debit', 'UBP Direct Debit'];
            return validBanks.contains(bankName) && accountNumber.length >= 8 && RegExp(r'^\d+$').hasMatch(accountNumber);
          }
        }
        return accountDetails.length >= 10; // Fallback for old format

      default:
        return accountDetails.length >= 5; // Generic minimum length
    }
  }

  /// Get account details placeholder text
  static String getAccountDetailsPlaceholder(String method) {
    switch (method) {
      case 'GCash':
        return 'Enter your GCash mobile number (e.g., ***********)';
      case 'Maya':
        return 'Enter your Maya mobile number (e.g., ***********)';
      case 'Credit Card':
        return 'Enter your card number (e.g., 1234 5678 9012 3456)';
      case 'Bank Transfer':
        return 'Please enter your account number';
      default:
        return 'Enter your account details';
    }
  }

  /// Get withdrawal methods available
  static List<Map<String, String>> getWithdrawalMethods() {
    return [
      {'id': 'GCash', 'name': 'GCash E-Wallet', 'icon': '💳'},
      {'id': 'Maya', 'name': 'Maya E-Wallet', 'icon': '💳'},
      {'id': 'Credit Card', 'name': 'Credit/Debit Card', 'icon': '💳'},
      {'id': 'Bank Transfer', 'name': 'Bank Transfer', 'icon': '🏦'},
    ];
  }
}
