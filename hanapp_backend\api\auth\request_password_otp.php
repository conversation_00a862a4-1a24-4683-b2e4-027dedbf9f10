<?php
require_once '../../config/db_connect.php';
require_once '../utils/email_sender.php';

// Set timezone at the beginning
date_default_timezone_set('Asia/Manila'); // or your local timezone

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(["success" => false, "message" => "Only POST method allowed"]);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['user_id'])) {
    echo json_encode(["success" => false, "message" => "User ID is required"]);
    exit();
}

$userId = $input['user_id'];

try {
    // Get user email and first name
    $userStmt = $conn->prepare("SELECT email, first_name FROM users WHERE id = ?");
    $userStmt->bind_param("i", $userId);
    $userStmt->execute();
    $userResult = $userStmt->get_result();
    
    if ($userResult->num_rows === 0) {
        echo json_encode(["success" => false, "message" => "User not found"]);
        exit();
    }
    
    $user = $userResult->fetch_assoc();
    $email = $user['email'];
    $firstName = $user['first_name'];
    
    // Generate OTP
    $otp = sprintf("%06d", mt_rand(1, 999999));
    
    // Calculate expiry time (10 minutes from now) using current timezone
    $expiresAt = date('Y-m-d H:i:s', strtotime('+10 minutes'));
    
    // Store OTP in database
    $stmt = $conn->prepare("INSERT INTO password_otp_codes (user_id, otp_code, expires_at) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE otp_code = VALUES(otp_code), expires_at = VALUES(expires_at), created_at = NOW()");
    $stmt->bind_param("iss", $userId, $otp, $expiresAt);
    
    if ($stmt->execute()) {
        // Send OTP email
        $subject = "Password Change Verification - TAPP";
        $htmlMessage = "
        <h2>Password Change Verification</h2>
        <p>Hello $firstName,</p>
        <p>You have requested to change your password. Please use the following OTP code to verify your identity:</p>
        <h3 style='color: #141CC9; font-size: 24px; text-align: center; padding: 20px; background-color: #f5f5f5; border-radius: 8px;'>$otp</h3>
        <p><strong>This code will expire in 10 minutes.</strong></p>
        <p>If you did not request this change, please ignore this email.</p>
        <p>Best regards,<br>TAPP Team</p>
        ";
        
        $textMessage = "Password Change Verification - TAPP\n\nHello $firstName,\n\nYou have requested to change your password. Please use the following OTP code: $otp\n\nThis code will expire in 10 minutes.\n\nIf you did not request this change, please ignore this email.\n\nBest regards,\nTAPP Team";
        
        // Generate idempotency key and send OTP
        $requestId = uniqid('pwd_otp_', true);
        $idempotencyKey = hash('sha256', $requestId);
        $emailResult = sendEmailViaSendGrid(
            $email, 
            $firstName, 
            $subject, 
            $htmlMessage, 
            $textMessage,
            null, // templateId
            [], // dynamicTemplateData
            $idempotencyKey
        );
        
        if ($emailResult['success']) {
            echo json_encode(["success" => true, "message" => "OTP sent successfully"]);
        } else {
            echo json_encode(["success" => false, "message" => "Failed to send OTP email: " . $emailResult['message']]);
        }
    } else {
        echo json_encode(["success" => false, "message" => "Failed to generate OTP"]);
    }
    
} catch (Exception $e) {
    error_log("Password OTP Request Error: " . $e->getMessage());
    echo json_encode(["success" => false, "message" => "Server error occurred"]);
}

$conn->close();
?>