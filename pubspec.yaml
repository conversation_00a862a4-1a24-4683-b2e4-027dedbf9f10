name: hanapp
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  http: ^1.2.1 # For making HTTP requests
  shared_preferences: ^2.2.3 # For local storage (e.g., user session)
  image_picker: ^1.1.0 # For picking profile pictures
  intl: ^0.19.0 # For date formatting
  flutter_datetime_picker_plus: ^2.1.0 # For date picker
  cached_network_image: ^3.3.1 # NEW: For loading network images efficiently
  flutter_rating_bar: ^4.0.1 # NEW: For rating stars
  geolocator: ^11.0.0 # NEW: For location services
  google_maps_flutter: ^2.6.0 # NEW: For displaying maps
  url_launcher: ^6.2.5 # NEW: For launching maps/directions
  webview_flutter: ^4.4.2 # NEW: For payment processing WebView
  geocoding: ^3.0.0
  http_parser: ^4.0.2 # Added for MediaType in MultipartFile
  provider: ^6.0.5 # Add this line
  flutter_polyline_points: ^2.0.0
  firebase_core: ^3.15.1 # Use the latest stable version
  firebase_auth: ^5.6.2 # Use the latest stable version
  google_sign_in: ^6.2.1 # Use stable version with known API
  flutter_facebook_auth: ^6.1.1 # Add this for future Facebook integration
  camera: ^0.10.5+9 # Use the latest stable version
  path_provider: ^2.0.11 # Often needed with camera for temporary file storage
  path: ^1.8.3
  permission_handler: ^11.0.1 # For requesting permissions
  qr_flutter: ^4.1.0 # Required for QR code generation
  device_info_plus: ^9.1.1 # For getting device model information
  flutter_widget_from_html_core: ^0.16.0 # For rendering HTML content in terms & conditions and privacy policy
  share_plus: ^7.2.1
  video_player: ^2.8.3
  chewie: ^1.7.5
  youtube_player_flutter: ^9.0.4
  flutter_inappwebview: 6.0.0
  font_awesome_flutter: ^10.7.0
  crypto: ^3.0.3
  dart_ipify: ^1.0.0 # For getting public IP address
  file_selector: ^1.0.3 
  flutter_sound: ^9.2.13 # Better cross-platform audio recording
  audioplayers: ^5.2.1 # For audio playback
  mime: ^1.0.4 # For file type detection
  google_mlkit_face_detection: ^0.13.1
  timezone: ^0.9.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_native_splash: ^2.3.11 # Ensure this is here
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.13.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/1.png
    - assets/2.png
    - assets/3.png
    - assets/11.png
    - assets/22.png
    - assets/33.png

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

flutter_icons:
  android: true
  ios: true
  image_path: "assets/hanapp_logo.png"
