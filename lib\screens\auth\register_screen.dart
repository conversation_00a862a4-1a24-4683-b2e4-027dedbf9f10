import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart' as geocoding;
import 'package:hanapp/utils/auth_service.dart';
import 'package:hanapp/screens/auth/login_screen.dart';
import 'package:hanapp/screens/auth/email_verification_screen.dart';
import 'package:intl/intl.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'dart:convert';
import 'package:google_maps_flutter/google_maps_flutter.dart'; // Import Google Maps
import 'package:geocoding/geocoding.dart'; // Import Geocoding
import 'package:hanapp/utils/location_service.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hanapp/utils/philippine_locations.dart';

import '../../utils/constants.dart' as Constants;
import '../components/custom_button.dart';
import '../../utils/image_utils.dart'; // Import ImageUtils

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final PageController _pageController = PageController();
  int _currentPageIndex = 0;
  final List<GlobalKey<FormState>> _formKeysForPages = List.generate(5, (_) => GlobalKey<FormState>());

  // Controllers for all form fields
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _middleNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _birthdayController = TextEditingController();
  DateTime? _selectedDate;

  // Individual address controllers for UI input
  final TextEditingController _streetController = TextEditingController();
  final TextEditingController _barangayController = TextEditingController();
  final TextEditingController _cityMunicipalityController = TextEditingController();
  final TextEditingController _provinceController = TextEditingController();
  final TextEditingController _countryController = TextEditingController();
  final TextEditingController _locationSearchController = TextEditingController(); // For map search

  // Dropdown state variables
  String? _selectedProvince;
  String? _selectedMunicipality;
  String? _selectedBarangay;
  List<String> _availableMunicipalities = [];
  List<String> _availableBarangays = [];

  final TextEditingController _contactNumberController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();

  String? _selectedRole; // 'lister' or 'doer'
  String? _selectedGender; // 'Male', 'Female', 'Other', 'Prefer not to say'

  File? _profileImage; // For storing the selected image file
  final ImagePicker _picker = ImagePicker();

  bool _isLoading = false;
  bool _passwordVisible = false;
  bool _confirmPasswordVisible = false;
  bool _isQualified = true; // State for qualification based on address (Philippines validation)

  final LocationService _locationService = LocationService();
  bool _isGettingLocation = false;

  // Map related variables
  GoogleMapController? _mapController;
  LatLng _selectedLocation = const LatLng(14.5995, 120.9842); // Default to a central point in Philippines (Manila)
  Marker? _locationMarker;
  double? _selectedLatitude;
  double? _selectedLongitude;

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _pageController.addListener(() {
      setState(() {
        _currentPageIndex = _pageController.page?.round() ?? 0;
      });
    });

    // Pre-fill Country and set Province to Metro Manila for validation
    _countryController.text = 'Philippines';
    _selectedProvince = 'Metro Manila';
    _provinceController.text = 'Metro Manila'; // Pre-fill for validation
    _availableMunicipalities = PhilippineLocations.getMunicipalities('Metro Manila');
    
    // Add this line to populate barangays initially for trial
    if (_availableMunicipalities.isNotEmpty) {
      _availableBarangays = PhilippineLocations.getBarangays('Metro Manila', _availableMunicipalities.first);
    }
    
    _provinceController.addListener(_validateAddress); // Listen for changes on province
    _updateLocationMarker(_selectedLocation); // Set initial marker
  }

  @override
  void dispose() {
    _pageController.dispose();
    _firstNameController.dispose();
    _middleNameController.dispose();
    _lastNameController.dispose();
    _birthdayController.dispose();
    _streetController.dispose();
    _barangayController.dispose();
    _cityMunicipalityController.dispose();
    _provinceController.removeListener(_validateAddress);
    _provinceController.dispose();
    _countryController.dispose();
    _locationSearchController.dispose();
    _contactNumberController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _mapController?.dispose(); // Dispose map controller
    super.dispose();
  }

  void _validateAddress() {
    setState(() {
      // Remove location restriction - allow all provinces
      _isQualified = true;
    });
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime(2000), // Default to a reasonable year
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _birthdayController.text = DateFormat('MMMM d, yyyy').format(picked);
      });
    }
  }

  Future<void> _pickImage() async {
    final XFile? pickedFile = await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _profileImage = File(pickedFile.path);
      });
    }
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    _mapController?.animateCamera(CameraUpdate.newLatLngZoom(_selectedLocation, 15.0));
  }

  void _onMapTap(LatLng latLng) {
    setState(() {
      _selectedLocation = latLng;
      _updateLocationMarker(latLng);
      _selectedLatitude = latLng.latitude;
      _selectedLongitude = latLng.longitude;
    });
    _reverseGeocodeLocation(latLng); // Update address fields based on map tap
  }

  void _updateLocationMarker(LatLng latLng) {
    setState(() {
      _locationMarker = Marker(
        markerId: const MarkerId('selected_location'),
        position: latLng,
        infoWindow: const InfoWindow(title: 'Selected Location'),
      );
    });
  }

  Future<void> _geocodeAddress() async {
    final address = _locationSearchController.text.trim();
    if (address.isEmpty) {
      _showSnackBar('Please enter an address to search.', isError: true);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      List<Location> locations = await geocoding.locationFromAddress(address);
      if (locations.isNotEmpty) {
        final latLng = LatLng(locations.first.latitude, locations.first.longitude);
        setState(() {
          _selectedLocation = latLng;
          _updateLocationMarker(latLng);
          _selectedLatitude = latLng.latitude;
          _selectedLongitude = latLng.longitude;
        });
        _mapController?.animateCamera(CameraUpdate.newLatLngZoom(latLng, 15.0));
        _showSnackBar('Location found!');
        _reverseGeocodeLocation(latLng); // Fill individual address fields
      } else {
        _showSnackBar('Address not found. Please try a more specific address.', isError: true);
      }
    } catch (e) {
      _showSnackBar('Error geocoding address: $e', isError: true);
      debugPrint('Geocoding error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _reverseGeocodeLocation(LatLng latLng) async {
    try {
      List<Placemark> placemarks = await geocoding.placemarkFromCoordinates(latLng.latitude, latLng.longitude);
      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        debugPrint('Placemark debug: street=${placemark.street}, subLocality=${placemark.subLocality}, locality=${placemark.locality}, subAdministrativeArea=${placemark.subAdministrativeArea}, administrativeArea=${placemark.administrativeArea}, name=${placemark.name}, country=${placemark.country}');

        // --- STREET ---
        String street = placemark.street ?? placemark.thoroughfare ?? placemark.name ?? '';
        _streetController.text = _capitalize(street);

        // --- PROVINCE ---
        String province = placemark.administrativeArea ?? '';
        if (province == 'Calabarzon') {
          // Try to infer the province from other fields by fuzzy matching
          List<String> candidates = [placemark.subAdministrativeArea, placemark.locality, placemark.name]
            .whereType<String>()
            .toList();
          String? matchedProvince = PhilippineLocations.getProvinces().firstWhere(
            (prov) => candidates.any((c) => c.toLowerCase().contains(prov.toLowerCase()) || prov.toLowerCase().contains(c.toLowerCase())),
            orElse: () => '',
          );
          if (matchedProvince != null && matchedProvince.isNotEmpty) {
            province = matchedProvince;
          } else {
            province = '';
          }
        }
        if (PhilippineLocations.getProvinces().contains(province)) {
          setState(() {
            _selectedProvince = province;
            _provinceController.text = province;
            _availableMunicipalities = PhilippineLocations.getMunicipalities(province);
          });
        } else {
          setState(() {
            _selectedProvince = null;
            _provinceController.text = '';
            _availableMunicipalities = [];
          });
        }

        // --- CITY/MUNICIPALITY ---
        List<String> cityCandidates = [placemark.locality, placemark.subAdministrativeArea, placemark.administrativeArea, placemark.name]
          .whereType<String>()
          .toList();
        String municipality = cityCandidates.firstWhere((c) => _availableMunicipalities.contains(c), orElse: () => '');
        if (municipality.isEmpty) {
          // Fuzzy match
          municipality = _availableMunicipalities.firstWhere(
            (m) => cityCandidates.any((c) => c.toLowerCase().contains(m.toLowerCase()) || m.toLowerCase().contains(c.toLowerCase())),
            orElse: () => '',
          );
        }
        if (municipality.isNotEmpty) {
          setState(() {
            _selectedMunicipality = municipality;
            _cityMunicipalityController.text = municipality;
            _availableBarangays = PhilippineLocations.getBarangays(_selectedProvince ?? '', municipality);
          });
        }

        // --- BARANGAY ---
        List<String> barangayCandidates = [placemark.subLocality, placemark.thoroughfare, placemark.name]
          .whereType<String>()
          .toList();
        String barangay = barangayCandidates.firstWhere((b) => _availableBarangays.contains(b), orElse: () => '');
        if (barangay.isEmpty) {
          // Fuzzy match
          barangay = _availableBarangays.firstWhere(
            (b) => barangayCandidates.any((c) => c.toLowerCase().contains(b.toLowerCase()) || b.toLowerCase().contains(c.toLowerCase())),
            orElse: () => '',
          );
        }
        if (barangay.isNotEmpty) {
          setState(() {
            _selectedBarangay = barangay;
            _barangayController.text = barangay;
          });
        } else if (_availableBarangays.contains('Poblacion')) {
          setState(() {
            _selectedBarangay = 'Poblacion';
            _barangayController.text = 'Poblacion';
          });
        }

        // --- COUNTRY ---
        String country = placemark.country ?? '';
        _countryController.text = _capitalize(country);
      }
    } catch (e) {
      debugPrint('Reverse geocoding error: $e');
    }
  }

  String _capitalize(String s) {
    if (s.isEmpty) return s;
    return s[0].toUpperCase() + s.substring(1);
  }

  Future<void> _getCurrentLocationAndFillFields() async {
    setState(() {
      _isGettingLocation = true;
    });
    
    try {
      // Get current position
      Position? position = await _locationService.getCurrentLocation();
      
      if (position != null) {
        // Update map location
        final LatLng currentLatLng = LatLng(position.latitude, position.longitude);
        _selectedLatitude = position.latitude;
        _selectedLongitude = position.longitude;
        _selectedLocation = currentLatLng;
        
        // Update map marker and camera
        _updateLocationMarker(currentLatLng);
        _mapController?.animateCamera(CameraUpdate.newLatLng(currentLatLng));
        
        // Automatically fill address fields using reverse geocoding
        await _reverseGeocodeLocation(currentLatLng);
        
        //_showSnackBar('Location detected and address fields filled automatically!', isError: false);
      }
    } catch (e) {
      _showSnackBar('Failed to get current location: $e', isError: true);
    } finally {
      setState(() {
        _isGettingLocation = false;
      });
    }
  }

  bool _validateCurrentPage() {
    // Validate the current page's form fields
    if (_formKeysForPages[_currentPageIndex].currentState?.validate() == false) {
      return false;
    }

    // Additional specific validations for each page
    switch (_currentPageIndex) {
      case 0: // Personal Details (Name & Birthday)
        if (_selectedDate == null) {
          _showSnackBar('Please select your birthday.', isError: true);
          return false;
        }
        break;
      case 1: // Address Details (including map location)
        // Ensure all individual address fields are filled
        if (_streetController.text.isEmpty ||
            _barangayController.text.isEmpty ||
            _cityMunicipalityController.text.isEmpty ||
            _provinceController.text.isEmpty ||
            _countryController.text.isEmpty) {
          _showSnackBar('Please fill all address fields.', isError: true);
          return false;
        }
        // Ensure a location is selected on the map
        if (_selectedLatitude == null || _selectedLongitude == null) {
          _showSnackBar('Please select your location on the map or search for it.', isError: true);
          return false;
        }
        break;
      case 2: // Contact & Gender Details
        if (_selectedGender == null) {
          _showSnackBar('Please select your gender.', isError: true);
          return false;
        }
        break;
      case 3: // Account Credentials
      // Password match is handled by validator in TextFormField
        break;
      case 4: // Role Selection (final page)
        if (_selectedRole == null) {
          _showSnackBar('Please select your role (Lister or Doer).', isError: true);
          return false;
        }
        break;
    }
    return true;
  }

  void _goToNextPage() {
    if (_validateCurrentPage()) {
      if (_currentPageIndex < _formKeysForPages.length - 1) {
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeIn,
        );
      }
    }
  }

  void _goToPreviousPage() {
    if (_currentPageIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _handleBackNavigation() {
    if (_currentPageIndex > 0) {
      // Go to previous page if not on the first page
      _goToPreviousPage();
    } else {
      // Show confirmation dialog if on the first page
      _showExitConfirmationDialog();
    }
  }

  void _showExitConfirmationDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Exit Registration'),
          content: const Text('Are you sure you want to exit the registration process? Your progress will be lost.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(), // Close dialog
              child: const Text('Cancel'),
            ),
            TextButton(
               onPressed: () {
                 Navigator.of(context).pop(); // Close dialog
                 Navigator.of(context).pushReplacement(
                   MaterialPageRoute(builder: (context) => const LoginScreen()),
                 );
               },
               child: const Text('Exit', style: TextStyle(color: Colors.red)),
             ),
          ],
        );
      },
    );
  }

  Future<void> _register() async {
    // Perform a final validation of all fields across all pages
    bool allFieldsValid = true;
    for (int i = 0; i < _formKeysForPages.length; i++) {
      if (_formKeysForPages[i].currentState?.validate() == false) {
        allFieldsValid = false;
        _pageController.animateToPage(i, duration: const Duration(milliseconds: 300), curve: Curves.easeIn);
        _showSnackBar('Please fill all required fields correctly.', isError: true);
        return;
      }
    }


    if (_selectedRole == null) {
      _showSnackBar('Please select your role (Lister or Doer).', isError: true);
      _pageController.animateToPage(4, duration: const Duration(milliseconds: 300), curve: Curves.easeIn); // Go to role page
      return;
    }
    if (_selectedGender == null) {
      _showSnackBar('Please select your gender.', isError: true);
      _pageController.animateToPage(2, duration: const Duration(milliseconds: 300), curve: Curves.easeIn); // Go to gender page
      return;
    }
    if (_selectedDate == null) {
      _showSnackBar('Please select your birthday.', isError: true);
      _pageController.animateToPage(0, duration: const Duration(milliseconds: 300), curve: Curves.easeIn); // Go to personal page
      return;
    }
    if (_selectedLatitude == null || _selectedLongitude == null) {
      _showSnackBar('Please select your location on the map or search for it.', isError: true);
      _pageController.animateToPage(1, duration: const Duration(milliseconds: 300), curve: Curves.easeIn); // Go to address page
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Combine individual address fields into a single string for addressDetails
    final String combinedAddressDetails = [
      _streetController.text.trim(),
      _barangayController.text.trim(),
      _cityMunicipalityController.text.trim(),
      _provinceController.text.trim(),
      _countryController.text.trim(),
    ].where((s) => s.isNotEmpty).join(', '); // Join non-empty parts with a comma and space

    // Convert _profileImage to base64
    String? base64Image;
    if (_profileImage != null) {
      base64Image = await ImageUtils.fileToBase64(_profileImage!);
    }

    final response = await AuthService.register(
      firstName: _firstNameController.text.trim(),
      middleName: _middleNameController.text.trim(),
      lastName: _lastNameController.text.trim(),
      birthday: DateFormat('yyyy-MM-dd').format(_selectedDate!),
      addressDetails: combinedAddressDetails, // Pass the combined string for display
      gender: _selectedGender!,
      contactNumber: _contactNumberController.text.trim(),
      email: _emailController.text.trim(),
      password: _passwordController.text,
      role: _selectedRole!,
      latitude: _selectedLatitude,   // Pass latitude to backend
      longitude: _selectedLongitude, // Pass longitude to backend
      profileImageBase64: base64Image, // Pass base64 encoded image to backend
    );

    setState(() {
      _isLoading = false;
    });

    // Add email empty check before proceeding to verification screen
    if (_emailController.text.trim().isEmpty) {
      _showSnackBar('Email is required for verification.', isError: true);
      return;
    }

    if (response['success']) {
      // Extract user ID from registration response and convert to int
      final userIdString = response['user']?['id']?.toString();
      final userId = userIdString != null ? int.tryParse(userIdString) : null;
      
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => EmailVerificationScreen(
            email: _emailController.text.trim(),
            userId: userId, // Now passing int? instead of String?
            isForEmailChange: false, // This is initial registration
          ),
        ),
      );
    } else {
      //_showSnackBar('Registration failed:  {response['message']}', isError: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sign Up'),
        backgroundColor: const Color(0xFF141CC9),
        foregroundColor: Colors.white,
        automaticallyImplyLeading: false,
        leading: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              _handleBackNavigation();
            },
            child: const Center(
              child: Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: 28,
              ),
            ),
          ),
        ),
      ),
      body: Column(
        children: [
          // Linear progress indicator for pages
          LinearProgressIndicator(
            value: (_currentPageIndex + 1) / _formKeysForPages.length,
            backgroundColor: Colors.grey.shade300,
            color: const Color(0xFF141CC9),
          ),
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(), // Disable manual swiping
              children: [
                // Page 1: Profile Picture, Name Fields, Birthday
                _buildPersonalDetailsPage(),
                // Page 2: Address Details (now with individual fields and map)
                _buildAddressDetailsPage(),
                // Page 3: Gender & Contact Number
                _buildContactGenderDetailsPage(),
                // Page 4: Email & Password
                _buildAccountCredentialsPage(),
                // Page 5: Role Selection & Register
                _buildRoleSelectionPage(),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (_currentPageIndex > 0)
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _goToPreviousPage,
                      style: OutlinedButton.styleFrom(
                        foregroundColor: const Color(0xFF141CC9),
                        side: const BorderSide(color: Color(0xFF141CC9)),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      ),
                      child: const Text('Back'),
                    ),
                  ),
                if (_currentPageIndex > 0 && _currentPageIndex < _formKeysForPages.length - 1)
                  const SizedBox(width: 16),
                if (_currentPageIndex < _formKeysForPages.length - 1)
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _goToNextPage,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF141CC9),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      ),
                      child: const Text('Continue'),
                    ),
                  ),
                if (_currentPageIndex == _formKeysForPages.length - 1)
                  Expanded(
                    child: _isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : ElevatedButton(
                      onPressed: _register,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF141CC9),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      ),
                      child: const Text(
                        'Get Started',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalDetailsPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Form(
        key: _formKeysForPages[0], // Unique key for this page's form
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Create Your Account',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Color(0xFF141CC9)),
            ),
            const SizedBox(height: 24),

            // Profile Picture Upload Section
            Center(
              child: Stack(
                children: [
                  CircleAvatar(
                    radius: 60,
                    backgroundColor: Colors.grey.shade200,
                    backgroundImage: _profileImage != null 
                        ? FileImage(_profileImage!) 
                        : null,
                    child: _profileImage == null
                        ? Icon(
                      Icons.camera_alt,
                      size: 50,
                      color: Colors.grey.shade600,
                    )
                        : null,
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: GestureDetector(
                      onTap: _pickImage,
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: const Color(0xFF141CC9),
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                        ),
                        child: const Icon(
                          Icons.add_a_photo,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            TextButton(
              onPressed: _pickImage,
              child: const Text('Upload your profile picture'),
            ),
            const SizedBox(height: 24),

            // Name Fields
            TextFormField(
              controller: _firstNameController,
              decoration: InputDecoration(
                labelText: 'First Name',
                hintText: 'Enter your first name',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                prefixIcon: const Icon(Icons.person),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your first name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _middleNameController,
              decoration: InputDecoration(
                labelText: 'Middle Name (Optional)',
                hintText: 'Enter your middle name',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                prefixIcon: const Icon(Icons.person_outline),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _lastNameController,
              decoration: InputDecoration(
                labelText: 'Last Name',
                hintText: 'Enter your last name',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                prefixIcon: const Icon(Icons.person),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your last name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Birthday Field
            TextFormField(
              controller: _birthdayController,
              readOnly: true,
              onTap: () => _selectDate(context),
              decoration: InputDecoration(
                labelText: 'Birthday',
                hintText: 'Select your birthday',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                prefixIcon: const Icon(Icons.calendar_today),
              ),
              validator: (value) {
                if (_selectedDate == null) {
                  return 'Please select your birthday';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddressDetailsPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Form(
        key: _formKeysForPages[1], // Unique key for this page's form
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Where do you live?',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 32),

            // Map Section
            Container(
              height: 250, // Fixed height for the map
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade300, width: 1),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: GoogleMap(
                  onMapCreated: _onMapCreated,
                  initialCameraPosition: CameraPosition(
                    target: _selectedLocation,
                    zoom: 15.0,
                  ),
                  markers: _locationMarker != null ? {_locationMarker!} : {},
                  onTap: _onMapTap,
                  myLocationButtonEnabled: true,
                  myLocationEnabled: true,
                  zoomControlsEnabled: true,
                ),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _locationSearchController,
              decoration: InputDecoration(
                labelText: 'Search Location',
                hintText: 'Enter address or landmark',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _isLoading
                    ? const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
                    : IconButton(
                  icon: const Icon(Icons.send),
                  onPressed: _geocodeAddress,
                ),
              ),
              onFieldSubmitted: (value) => _geocodeAddress(), // Trigger on Enter
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _isGettingLocation ? null : _getCurrentLocationAndFillFields,
              icon: _isGettingLocation
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                    )
                  : const Icon(Icons.my_location),
              label: Text(_isGettingLocation ? 'Getting Location...' : 'Use My Current Location'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF141CC9),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Selected Coordinates: Lat: ${_selectedLatitude?.toStringAsFixed(5) ?? 'N/A'}, Long: ${_selectedLongitude?.toStringAsFixed(5) ?? 'N/A'}',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
            ),
            const SizedBox(height: 24),

            // Individual Address Fields
            TextFormField(
              controller: _streetController,
              decoration: InputDecoration(
                labelText: 'Street',
                hintText: 'Enter your street address',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                prefixIcon: const Icon(Icons.location_on),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your street address';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedBarangay,
              decoration: InputDecoration(
                labelText: 'Barangay',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                prefixIcon: const Icon(Icons.location_on),
              ),
              items: _availableBarangays.map((String barangay) {
                return DropdownMenuItem<String>(
                  value: barangay,
                  child: Text(barangay),
                );
              }).toList(),
              onChanged: (String? newValue) {
                setState(() {
                  _selectedBarangay = newValue;
                  _barangayController.text = newValue ?? '';
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please select your barangay';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedMunicipality,
              decoration: InputDecoration(
                labelText: 'City/Municipality',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                prefixIcon: const Icon(Icons.location_city),
              ),
              items: _availableMunicipalities.map((String municipality) {
                return DropdownMenuItem<String>(
                  value: municipality,
                  child: Text(municipality),
                );
              }).toList(),
              onChanged: (_selectedProvince == null || _availableMunicipalities.isEmpty) ? null : (String? newValue) {
                setState(() {
                  _selectedMunicipality = newValue;
                  _cityMunicipalityController.text = newValue ?? '';
                  _selectedBarangay = null;
                  _barangayController.clear();
                  // Ensure we properly populate barangays when municipality is selected
                  if (_selectedProvince != null && newValue != null) {
                    _availableBarangays = PhilippineLocations.getBarangays(_selectedProvince!, newValue);
                  } else {
                    _availableBarangays = [];
                  }
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please select your city/municipality';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedProvince,
              decoration: InputDecoration(
                labelText: 'Province',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                prefixIcon: const Icon(Icons.area_chart),
              ),
              items: PhilippineLocations.getProvinces().map((String province) {
                return DropdownMenuItem<String>(
                  value: province,
                  child: Text(province),
                );
              }).toList(),
              onChanged: (String? newValue) {
                setState(() {
                  _selectedProvince = newValue;
                  _provinceController.text = newValue ?? '';
                  _selectedMunicipality = null;
                  _selectedBarangay = null;
                  _cityMunicipalityController.clear();
                  _barangayController.clear();
                  // Properly populate municipalities when province is selected
                  if (newValue != null) {
                    _availableMunicipalities = PhilippineLocations.getMunicipalities(newValue);
                  } else {
                    _availableMunicipalities = [];
                  }
                  _availableBarangays = [];
                });
                _validateAddress();
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please select your province';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _countryController,
              decoration: InputDecoration(
                labelText: 'Country',
                hintText: 'Enter your country',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                prefixIcon: const Icon(Icons.public),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your country';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),

          ],
        ),
      ),
    );
  }

  Widget _buildContactGenderDetailsPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Form(
        key: _formKeysForPages[2], // Unique key for this page's form
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Contact & Gender Details:',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 32),
            DropdownButtonFormField<String>(
              value: _selectedGender,
              hint: const Text('Select Gender'),
              decoration: InputDecoration(
                labelText: 'Gender',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                prefixIcon: const Icon(Icons.people),
              ),
              items: <String>['Male', 'Female', 'Other', 'Prefer not to say']
                  .map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
              onChanged: (String? newValue) {
                setState(() {
                  _selectedGender = newValue;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please select your gender';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _contactNumberController,
              keyboardType: TextInputType.phone,
              decoration: InputDecoration(
                labelText: 'Contact Number',
                hintText: 'e.g., +639123456789',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                prefixIcon: const Icon(Icons.phone),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your contact number';
                }
                if (!RegExp(r'^[0-9+]+$').hasMatch(value)) {
                  return 'Please enter a valid contact number';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountCredentialsPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Form(
        key: _formKeysForPages[3], // Unique key for this page's form
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Set up your account',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 32),
            TextFormField(
              controller: _emailController,
              keyboardType: TextInputType.emailAddress,
              decoration: InputDecoration(
                labelText: 'Email',
                hintText: 'Enter your email address',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                prefixIcon: const Icon(Icons.email),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your email';
                }
                if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value)) {
                  return 'Please enter a valid email address';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _passwordController,
              obscureText: !_passwordVisible,
              decoration: InputDecoration(
                labelText: 'Password',
                hintText: 'Enter your password',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                prefixIcon: const Icon(Icons.lock),
                suffixIcon: IconButton(
                  icon: Icon(
                    _passwordVisible ? Icons.visibility : Icons.visibility_off,
                  ),
                  onPressed: () {
                    setState(() {
                      _passwordVisible = !_passwordVisible;
                    });
                  },
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a password';
                }
                if (value.length < 6) {
                  return 'Password must be at least 6 characters long';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _confirmPasswordController,
              obscureText: !_confirmPasswordVisible,
              decoration: InputDecoration(
                labelText: 'Confirm Password',
                hintText: 'Re-enter your password',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                prefixIcon: const Icon(Icons.lock),
                suffixIcon: IconButton(
                  icon: Icon(
                    _confirmPasswordVisible ? Icons.visibility : Icons.visibility_off,
                  ),
                  onPressed: () {
                    setState(() {
                      _confirmPasswordVisible = !_confirmPasswordVisible;
                    });
                  },
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please confirm your password';
                }
                if (value != _passwordController.text) {
                  return 'Passwords do not match';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleOption(BuildContext context, String title, IconData icon, String roleValue, bool isSelected) {
    return Expanded(
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedRole = roleValue;
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 20),
          decoration: BoxDecoration(
            color: isSelected ? const Color(0xFF141CC9) : Colors.blue.shade50, // Selected color vs. light blue
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? const Color(0xFF141CC9) : Colors.grey.shade300,
              width: 2,
            ),
            boxShadow: isSelected
                ? [
              BoxShadow(
                color: const Color(0xFF141CC9).withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ]
                : [],
          ),
          child: Column(
            children: [
              Icon(
                icon,
                size: 50,
                color: isSelected ? Colors.white : const Color(0xFF141CC9),
              ),
              const SizedBox(height: 10),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isSelected ? Colors.white : Colors.black87,
                ),
              ),
              Text(
                '(${roleValue == 'lister' ? 'Post Jobs' : 'Find Jobs'})',
                style: TextStyle(
                  fontSize: 12,
                  color: isSelected ? Colors.white70 : Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRoleSelectionPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Form(
        key: _formKeysForPages[4], // Unique key for this page's form
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center, // Center vertically
          crossAxisAlignment: CrossAxisAlignment.center, // Center horizontally
          children: [
            const Text(
              'Choose Your Role',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'Don\'t worry, you can switch roles inside',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 32),
            Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildRoleOption(
                    context,
                    'Lister',
                    Icons.edit, // Icon for Lister (e.g., pencil)
                    'lister',
                    _selectedRole == 'lister',
                  ),
                  const SizedBox(width: 20), // Space between buttons
                  _buildRoleOption(
                    context,
                    'Doer',
                    Icons.build, // Icon for Doer (e.g., hammer)
                    'doer',
                    _selectedRole == 'doer',
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
            // Already have an account? Login
            TextButton(
              onPressed: () {
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(builder: (context) => const LoginScreen()),
                );
              },
              child: Text(
                'Already have an account? Login',
                style: TextStyle(color: Colors.grey.shade700),
              ),
            ),
            const SizedBox(height: 24), // Space before social logins
          ],
        ),
      ),
    );
  }
}
