<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $applicationId = $input['application_id'] ?? null;
    $listerId = $input['lister_id'] ?? null;
    $doerFee = $input['doer_fee'] ?? null;
    $totalAmount = $input['total_amount'] ?? null;
    $paymentMethod = $input['payment_method'] ?? null;
    $userEmail = $input['user_email'] ?? null;
    $userFullName = $input['user_full_name'] ?? null;
    
    if (!$applicationId || !$listerId || !$doerFee || !$totalAmount || !$paymentMethod || !$userEmail || !$userFullName) {
        throw new Exception('Missing required parameters');
    }
    
    // Get application details
    $stmt = $pdo->prepare("SELECT doer_id, listing_id FROM applications WHERE id = ?");
    $stmt->execute([$applicationId]);
    $application = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$application) {
        throw new Exception('Application not found');
    }
    
    $doerId = $application['doer_id'];
    $listingId = $application['listing_id'];
    
    // Create Xendit invoice
    $xenditApiKey = 'xnd_development_your_api_key_here'; // Replace with actual API key
    $xenditUrl = 'https://api.xendit.co/v2/invoices';
    
    // Generate unique external ID
    $externalId = 'job_payment_' . $applicationId . '_' . time();
    
    // Determine payment methods based on selected method (case-insensitive)
    $paymentMethods = [];
    $paymentMethodLower = strtolower(trim($paymentMethod));
    error_log("DEBUG - Payment method: Original='$paymentMethod', Normalized='$paymentMethodLower'");
    switch ($paymentMethodLower) {
        case 'gcash':
            $paymentMethods = ['GCASH'];
            break;
        case 'paymaya':
            $paymentMethods = ['PAYMAYA'];
            break;
        case 'grabpay':
            $paymentMethods = ['GRABPAY'];
            break;
        case 'card':
        case 'credit/debit card':
        case 'philippine_card':
        case 'international_card':
            $paymentMethods = ['CREDIT_CARD', 'DEBIT_CARD'];
            break;
        case 'bank_transfer':
            $paymentMethods = ['BANK_TRANSFER'];
            break;
        case 'bpi':
            $paymentMethods = ['DD_BPI'];
            break;
        case 'bdo':
            $paymentMethods = ['BDO'];
            break;
        case 'metrobank':
            $paymentMethods = ['METROBANK'];
            break;
        case 'unionbank':
            $paymentMethods = ['DD_UBP'];
            break;
        case 'rcbc':
            $paymentMethods = ['DD_RCBC'];
            break;
        case 'chinabank':
            $paymentMethods = ['DD_CHINABANK'];
            break;
        default:
            $paymentMethods = ['GCASH', 'PAYMAYA', 'CREDIT_CARD', 'DEBIT_CARD', 'BANK_TRANSFER'];
    }

    error_log("DEBUG - Configured payment methods: " . json_encode($paymentMethods));
    
    $invoiceData = [
        'external_id' => $externalId,
        'amount' => $totalAmount,
        'description' => "Job Payment - Application #$applicationId",
        'invoice_duration' => 86400, // 24 hours
        'customer' => [
            'given_names' => $userFullName,
            'email' => $userEmail,
        ],
        'customer_notification_preference' => [
            'invoice_created' => ['email'],
            'invoice_reminder' => ['email'],
            'invoice_paid' => ['email'],
            'invoice_expired' => ['email']
        ],
        'payment_methods' => $paymentMethods,
        'success_redirect_url' => 'https://yourapp.com/hanapp_backend/job_payment_success.html?application_id=' . $applicationId . '&doer_fee=' . $doerFee,
        'failure_redirect_url' => 'https://yourapp.com/hanapp_backend/job_payment_failed.html?application_id=' . $applicationId,
        'metadata' => [
            'application_id' => $applicationId,
            'lister_id' => $listerId,
            'doer_id' => $doerId,
            'doer_fee' => $doerFee,
            'total_amount' => $totalAmount,
            'payment_method' => $paymentMethod
        ]
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $xenditUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($invoiceData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Basic ' . base64_encode($xenditApiKey . ':'),
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode !== 200) {
        throw new Exception('Failed to create Xendit invoice: ' . $response);
    }
    
    $invoiceResponse = json_decode($response, true);
    
    if (!$invoiceResponse || !isset($invoiceResponse['invoice_url'])) {
        throw new Exception('Invalid response from Xendit');
    }
    
    // Store invoice details in database
    $stmt = $pdo->prepare("
        INSERT INTO job_payment_invoices (
            application_id, lister_id, doer_id, listing_id, 
            xendit_invoice_id, external_id, amount, doer_fee, 
            payment_method, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())
    ");
    $stmt->execute([
        $applicationId,
        $listerId,
        $doerId,
        $listingId,
        $invoiceResponse['id'],
        $externalId,
        $totalAmount,
        $doerFee,
        $paymentMethod
    ]);
    
    echo json_encode([
        'success' => true,
        'invoice_url' => $invoiceResponse['invoice_url'],
        'invoice_id' => $invoiceResponse['id'],
        'external_id' => $externalId
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
