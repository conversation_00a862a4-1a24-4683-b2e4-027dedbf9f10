import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geocoding/geocoding.dart' as geocoding;
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geocoding/geocoding.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:hanapp/utils/asap_listing_service.dart';
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/utils/word_filter_service.dart';
import 'package:hanapp/widgets/banned_words_dialog.dart';
import 'package:hanapp/services/image_upload_service.dart';
import 'package:hanapp/utils/location_service.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:math' as math;

import '../components/custom_button.dart';

class AsapListingFormScreen extends StatefulWidget {
  const AsapListingFormScreen({super.key});

  @override
  State<AsapListingFormScreen> createState() => _AsapListingFormScreenState();
}

class _AsapListingFormScreenState extends State<AsapListingFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _locationSearchController = TextEditingController();
  final TextEditingController _locationAddressController = TextEditingController();

  GoogleMapController? _mapController;
  LatLng _selectedLocation = const LatLng(14.5995, 120.9842); // Center of Philippines (Manila)
  Marker? _locationMarker;
  double? _listingLatitude;
  double? _listingLongitude;

  // Philippines geographical bounds
  static final LatLngBounds _philippinesBounds = LatLngBounds(
    southwest: const LatLng(4.0, 114.0), // Southwest corner of Philippines
    northeast: const LatLng(21.0, 127.0), // Northeast corner of Philippines
  );

  String? _preferredDoerGender;
  List<File> _selectedImages = [];
  final ImagePicker _picker = ImagePicker();



  bool _isLoading = false;
  bool _isGettingLocation = false; // Add this variable
  final LocationService _locationService = LocationService(); // Add this variable
  bool _isOpenPrice = false;

  @override
  void initState() {
    super.initState();
  }

  // Check if a location is within Philippines bounds
  bool _isLocationInPhilippines(LatLng location) {
    return location.latitude >= _philippinesBounds.southwest.latitude &&
           location.latitude <= _philippinesBounds.northeast.latitude &&
           location.longitude >= _philippinesBounds.southwest.longitude &&
           location.longitude <= _philippinesBounds.northeast.longitude;
  }

  // Check if an address contains Philippines-related keywords
  bool _isAddressInPhilippines(String address) {
    final philippinesKeywords = [
      'philippines', 'filipino', 'pinoy', 'pinay', 'metro manila', 'manila',
      'quezon city', 'caloocan', 'las piñas', 'makati', 'malabon', 'mandaluyong',
      'marikina', 'muntinlupa', 'navotas', 'parañaque', 'pasay', 'pasig',
      'san juan', 'taguig', 'valenzuela', 'pateros'
    ];

    final lowerAddress = address.toLowerCase();
    return philippinesKeywords.any((keyword) => lowerAddress.contains(keyword));
  }

  // Validate location using reverse geocoding (same as sign-up logic)
  Future<bool> _validateLocationByPhilippines(LatLng location) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(
        location.latitude,
        location.longitude,
      );

      if (placemarks.isNotEmpty) {
        final placemark = placemarks[0];
        // Check if location is in Philippines
        final country = placemark.country?.toLowerCase() ?? '';
        final province = placemark.administrativeArea?.toLowerCase() ?? '';

        bool isQualified = (country == 'philippines') ||
                          (province.contains('philippines'));

        print('🔍 Location validation: Country = ${placemark.country}, Province = ${placemark.administrativeArea}, Qualified = $isQualified');
        return isQualified;
      }

      return false;
    } catch (e) {
      print('❌ Error validating location: $e');
      return false;
    }
  }

  // Custom input formatter for Philippine peso amounts
  static final RegExp _pesoRegex = RegExp(r'^\d*\.?\d{0,2}$');

  List<TextInputFormatter> get _pesoInputFormatters => [
    FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
    TextInputFormatter.withFunction((oldValue, newValue) {
      // Allow empty string
      if (newValue.text.isEmpty) {
        return newValue;
      }

      // Check if the new value matches our peso format
      if (_pesoRegex.hasMatch(newValue.text)) {
        // Ensure only one decimal point
        if (newValue.text.split('.').length <= 2) {
          return newValue;
        }
      }

      // If invalid, keep the old value
      return oldValue;
    }),
  ];

  @override
  void dispose() {
    _titleController.dispose();
    _priceController.dispose();
    _descriptionController.dispose();
    _locationSearchController.dispose();
    _locationAddressController.dispose();
    _mapController?.dispose();
    super.dispose();
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).clearSnackBars();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isError ? Icons.error : Icons.check_circle,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: isError ? Colors.red.shade600 : Colors.green.shade600,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 6,
      ),
    );
  }

  Future<void> _geocodeAddress() async {
    final address = _locationSearchController.text.trim();
    if (address.isEmpty) {
      _showSnackBar('Please enter an address to search.', isError: true);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // First check if the address contains Philippines keywords
      if (!_isAddressInPhilippines(address)) {
        _showSnackBar("Please enter a location within Philippines.", isError: true);
        setState(() {
          _isLoading = false;
        });
        return;
      }

      List<geocoding.Location> locations = await geocoding.locationFromAddress(address + ', Philippines');
      if (locations.isNotEmpty) {
        geocoding.Location location = locations.first;
        LatLng latLng = LatLng(location.latitude, location.longitude);

        // Validate using the same logic as sign-up
        bool isValidPhilippinesLocation = await _validateLocationByPhilippines(latLng);
        if (!isValidPhilippinesLocation) {
          _showSnackBar("Please enter a location within Philippines.", isError: true);
          setState(() {
            _isLoading = false;
          });
          return;
        }

        setState(() {
          _selectedLocation = latLng;
          _updateLocationMarker(latLng);
          _listingLatitude = latLng.latitude;
          _listingLongitude = latLng.longitude;
          // Set the searched address immediately
          _locationAddressController.text = address;
        });
        _mapController?.animateCamera(CameraUpdate.newLatLngZoom(latLng, 16.0));
        _showSnackBar('🔍 Location found! Tap the map to fine-tune if needed.');

        // Also try to get a more detailed address via reverse geocoding
        _reverseGeocodeLocation(latLng);
      } else {
        _showSnackBar("Sorry, it's currently unavailable in your area. We're planning to expand soon.", isError: true);
      }
    } catch (e) {
      _showSnackBar('Error geocoding address: $e', isError: true);
      debugPrint('Geocoding error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }



  Future<void> _proceedToDoerSearch() async {
    print('🔍 Starting form validation...');

    if (_formKey.currentState!.validate()) {
      print('✅ Basic form validation passed');

      // Update price validation for open price
      if (!_isOpenPrice) {
        final price = double.tryParse(_priceController.text);
        print('💰 Price validation: ${_priceController.text} -> $price');
        if (price == null || price < 20) {
          print('❌ Price validation failed');
          _showSnackBar('❌ Doer Fee must be at least Php 20. Please check the Doer Fee field.', isError: true);
          return;
        }
      }

      print('📍 Location validation: lat=$_listingLatitude, lng=$_listingLongitude, address=${_locationAddressController.text}');
      if (_listingLatitude == null || _listingLongitude == null || _locationAddressController.text.isEmpty) {
        print('❌ Location validation failed');
        _showSnackBar('📍 Please select a location on the map. Tap on the map to choose your location.', isError: true);
        return;
      }

      print('👤 Preferred doer validation: $_preferredDoerGender');
      if (_preferredDoerGender == null) {
        print('❌ Preferred doer validation failed');
        _showSnackBar('👤 Please select a preferred doer (Any, Male, or Female) in the Preferred Doer field.', isError: true);
        return;
      }





      print('✅ All validations passed, proceeding with listing creation...');
    } else {
      print('❌ Basic form validation failed');
      _showSnackBar('❌ Please fill in all required fields correctly.', isError: true);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Check for banned words in title and description
    print('AsapListingForm: Starting word filter check...');
    print('AsapListingForm: Title: "${_titleController.text.trim()}"');
    print('AsapListingForm: Description: "${_descriptionController.text.trim()}"');

    try {
      final wordFilterService = WordFilterService();
      final fieldsToCheck = {
        'title': _titleController.text.trim(),
        'description': _descriptionController.text.trim(),
      };

      final bannedWordsByField = await wordFilterService.checkMultipleFields(fieldsToCheck);
      
      print('AsapListingForm: Banned words result: $bannedWordsByField');
      
      if (bannedWordsByField.isNotEmpty) {
        setState(() {
          _isLoading = false;
        });
        
        print('AsapListingForm: Showing banned words dialog');
        // Show popup dialog with banned words
        await BannedWordsDialog.show(context, bannedWordsByField);
        return;
      } else {
        print('AsapListingForm: No banned words found, proceeding with creation');
      }
    } catch (e) {
      print('AsapListingForm: Error checking banned words: $e');
      // Continue with creation if word filter fails
    }

    // Upload images if any are selected
    List<String> imageUrls = [];
    if (_selectedImages.isNotEmpty) {
      print('🖼️ Uploading ${_selectedImages.length} images...');
      final uploadResult = await ImageUploadService().uploadListingImages(_selectedImages);

      if (uploadResult['success'] == true) {
        imageUrls = List<String>.from(uploadResult['image_urls'] ?? []);
        print('✅ Images uploaded successfully: $imageUrls');
      } else {
        print('❌ Image upload failed: ${uploadResult['message']}');
        _showSnackBar('Failed to upload images: ${uploadResult['message']}', isError: true);
        setState(() {
          _isLoading = false;
        });
        return;
      }
    }
    // No placeholder images - let the detail screens handle empty image lists





    setState(() {
      _isLoading = false;
    });

    // Navigate to doer search with form data (don't create listing yet)
    if (mounted) {
      Navigator.of(context).pushNamed(
        '/asap_doer_search',
        arguments: {
          'listing_data': {
            'title': _titleController.text.trim(),
            'description': _descriptionController.text.trim(),
            'price': _isOpenPrice ? 0.0 : double.parse(_priceController.text),
            'is_open_price': _isOpenPrice,
            'latitude': _listingLatitude!,
            'longitude': _listingLongitude!,
            'location_address': _locationAddressController.text.trim(),
            'preferred_doer_gender': _preferredDoerGender ?? 'Any',
            'pictures_urls': imageUrls,

          },
          'listing_latitude': _listingLatitude,
          'listing_longitude': _listingLongitude,
          'preferred_doer_gender': _preferredDoerGender ?? 'Any',
          'max_distance': 10.0,
        },
      );
    }
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    // Set camera bounds to show Philippines area
    _mapController?.setMapStyle('''
      [
        {
          "featureType": "administrative.province",
          "elementType": "geometry.fill",
          "stylers": [
            {
              "visibility": "on"
            }
          ]
        }
      ]
    ''');
  }

  void _onMapTap(LatLng position) async {
    // First check basic coordinate bounds
    if (!_isLocationInPhilippines(position)) {
      _showSnackBar("Please select a location within Philippines.", isError: true);
      return;
    }

    // Then validate using reverse geocoding (same as sign-up logic)
    bool isValidPhilippinesLocation = await _validateLocationByPhilippines(position);
    if (!isValidPhilippinesLocation) {
      _showSnackBar("Please select a location within Philippines.", isError: true);
      return;
    }

    setState(() {
      _selectedLocation = position;
      _listingLatitude = position.latitude;
      _listingLongitude = position.longitude;
      _updateLocationMarker(position);
    });
    _reverseGeocodeLocation(position);
    _showSnackBar('📍 Location selected successfully!');
    print('📍 Location selected in Philippines: Lat: ${position.latitude}, Lng: ${position.longitude}');
  }

  void _updateLocationMarker(LatLng position) {
    setState(() {
      _locationMarker = Marker(
        markerId: const MarkerId('selected_location'),
        position: position,
        infoWindow: const InfoWindow(title: 'Selected Location'),
      );
    });
  }

  Future<void> _getCurrentLocationAndFillFields() async {
    setState(() {
      _isGettingLocation = true;
    });

    try {
      // Get current position
      Position? position = await _locationService.getCurrentLocation();

      if (position != null) {
        // Update map location
        final LatLng currentLatLng = LatLng(position.latitude, position.longitude);
        
        // Validate if location is in Philippines
        bool isValidLocation = await _validateLocationByPhilippines(currentLatLng);
        if (!isValidLocation) {
          _showSnackBar('Current location is outside Philippines. Please select a location within Philippines.', isError: true);
          return;
        }
        
        _listingLatitude = position.latitude;
        _listingLongitude = position.longitude;

        setState(() {
          _selectedLocation = currentLatLng;
          _updateLocationMarker(currentLatLng);
        });

        // Move map camera to current location
        _mapController?.animateCamera(CameraUpdate.newLatLngZoom(currentLatLng, 15.0));

        // Fill address field using reverse geocoding
        await _reverseGeocodeLocation(currentLatLng);

        _showSnackBar('Current location detected and set successfully!', isError: false);
      }
    } catch (e) {
      _showSnackBar('Failed to get current location: $e', isError: true);
    } finally {
      setState(() {
        _isGettingLocation = false;
      });
    }
  }

  Future<void> _reverseGeocodeLocation(LatLng position) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];

        // Build a comprehensive address string
        List<String> addressParts = [];

        if (place.street != null && place.street!.isNotEmpty) {
          addressParts.add(place.street!);
        }
        if (place.subLocality != null && place.subLocality!.isNotEmpty) {
          addressParts.add(place.subLocality!);
        }
        if (place.locality != null && place.locality!.isNotEmpty) {
          addressParts.add(place.locality!);
        }
        if (place.administrativeArea != null && place.administrativeArea!.isNotEmpty) {
          addressParts.add(place.administrativeArea!);
        }
        if (place.country != null && place.country!.isNotEmpty) {
          addressParts.add(place.country!);
        }

        String address = addressParts.isNotEmpty
            ? addressParts.join(', ')
            : 'Lat: ${position.latitude.toStringAsFixed(6)}, Lng: ${position.longitude.toStringAsFixed(6)}';

        setState(() {
          _locationAddressController.text = address;
        });

        print('📍 Location selected: $address');
        _showSnackBar('📍 Location selected successfully!');
      } else {
        // Fallback to coordinates if no placemark found
        String address = 'Lat: ${position.latitude.toStringAsFixed(6)}, Lng: ${position.longitude.toStringAsFixed(6)}';
        setState(() {
          _locationAddressController.text = address;
        });
        print('📍 Location selected (coordinates): $address');
        _showSnackBar('📍 Location selected using coordinates!');
      }
    } catch (e) {
      debugPrint('Reverse geocoding error: $e');
      // Fallback to coordinates if geocoding fails
      String address = 'Lat: ${position.latitude.toStringAsFixed(6)}, Lng: ${position.longitude.toStringAsFixed(6)}';
      setState(() {
        _locationAddressController.text = address;
      });
      _showSnackBar('📍 Location selected (geocoding unavailable)!');
    }
  }

  Future<void> _pickImage() async {
    if (_selectedImages.length >= 5) {
      _showSnackBar('Maximum 5 images allowed.', isError: true);
      return;
    }

    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        _selectedImages.add(File(image.path));
      });
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  Widget _buildFeeRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            'Php ${amount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildDivider() {
    return Divider(
      height: 1,
      thickness: 1,
      color: Colors.grey.shade200,
      indent: 20,
      endIndent: 20,
    );
  }







  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create ASAP Listing'),
        backgroundColor: Constants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title
              TextFormField(
                controller: _titleController,
                decoration: InputDecoration(
                  labelText: 'Title',
                  hintText: 'Type title here',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a title';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Doer Fee Section
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Doer Fee Input with Toggle Switch
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Doer Fee Input Field
                      Expanded(
                        child: TextFormField(
                          controller: _priceController,
                          enabled: !_isOpenPrice, // Disable when open price is selected
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          inputFormatters: _pesoInputFormatters,
                          decoration: InputDecoration(
                            labelText: _isOpenPrice ? 'Open Price (Doers will offer)' : 'Doer Fee *',
                            hintText: _isOpenPrice ? 'Doers will submit their price offers' : 'e.g., 20.00',
                            helperText: _isOpenPrice
                                ? 'Minimum doer offer: Php 20.00'
                                : 'Minimum: Php 20.00',
                            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                            prefixText: _isOpenPrice ? '' : 'Php ',
                            prefixStyle: const TextStyle(
                              color: Colors.black87,
                              fontWeight: FontWeight.w500,
                            ),
                            filled: _isOpenPrice,
                            fillColor: _isOpenPrice ? Colors.grey.shade100 : null,
                          ),
                          validator: (value) {
                            if (_isOpenPrice) {
                              return null; // No validation needed for open price
                            }
                            
                            if (value == null || value.isEmpty) {
                              return 'Please enter a doer fee';
                            }

                            // Check if it matches our peso format
                            if (!_pesoRegex.hasMatch(value)) {
                              return 'Invalid format. Use numbers and decimal point only (e.g., 20.00)';
                            }

                            final price = double.tryParse(value);
                            if (price == null) {
                              return 'Please enter a valid number';
                            }

                            if (price <= 0) {
                              return 'Doer fee must be greater than zero';
                            }

                            if (price < 20) {
                              return 'Minimum doer fee is Php 20.00';
                            }

                            if (price > 999999.99) {
                              return 'Maximum doer fee is Php 999,999.99';
                            }

                            return null;
                          },
                          onChanged: (_) {},
                        ),
                      ),
                      const SizedBox(width: 16),
                      // Open Price Toggle Switch
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Switch(
                            value: _isOpenPrice,
                            onChanged: (value) {
                              setState(() {
                                _isOpenPrice = value;
                                if (_isOpenPrice) {
                                  _priceController.clear();
                                }
                              });

                            },
                            activeColor: Constants.primaryColor,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Open Price',
                            style: TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                              fontStyle: FontStyle.italic,
                              color: Colors.grey.shade700,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  
                  // Open Price Info Card
                  if (_isOpenPrice)
                    Container(
                      margin: const EdgeInsets.only(top: 12),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade50,
                        border: Border.all(color: Colors.orange.shade200),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.info_outline, color: Colors.orange.shade600, size: 20),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Doers will receive notifications and can offer their own price (minimum ₱20). You can review and accept the best offer.',
                              style: TextStyle(
                                color: Colors.orange.shade700,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 16),

              // Description
              TextFormField(
                controller: _descriptionController,
                maxLines: 3,
                decoration: InputDecoration(
                  labelText: 'Description',
                  hintText: 'Describe your task',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a description';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),

              // Location Search
              TextFormField(
                controller: _locationSearchController,
                decoration: InputDecoration(
                  labelText: 'Search Location in Philippines',
                  hintText: 'e.g., Manila, Quezon City, Makati, Taguig',
                  helperText: '📍 Service is available throughout Philippines',
                  helperStyle: TextStyle(color: Constants.primaryColor, fontSize: 12),
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  suffixIcon: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (_isGettingLocation)
                        const Padding(
                          padding: EdgeInsets.all(8),
                          child: SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                        )
                      else
                        IconButton(
                          icon: const Icon(Icons.search),
                          onPressed: _geocodeAddress,
                        ),
                    ],
                  ),
                ),
                onFieldSubmitted: (value) => _geocodeAddress(),
              ),
              const SizedBox(height: 16),

              // Use Current Location Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isGettingLocation ? null : _getCurrentLocationAndFillFields,
                  icon: _isGettingLocation
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                        )
                      : const Icon(Icons.my_location),
                  label: Text(_isGettingLocation ? 'Getting Location...' : 'Use My Current Location'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Constants.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Map
              Container(
                height: 200,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade300, width: 1),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: GoogleMap(
                    onMapCreated: _onMapCreated,
                    initialCameraPosition: CameraPosition(
                      target: _selectedLocation,
                      zoom: 6.0, // Zoom level to show most of Philippines
                    ),
                    markers: _locationMarker != null ? {_locationMarker!} : {},
                    onTap: _onMapTap,
                    myLocationButtonEnabled: true,
                    myLocationEnabled: true,
                    zoomControlsEnabled: true,
                    cameraTargetBounds: CameraTargetBounds(_philippinesBounds),
                    minMaxZoomPreference: const MinMaxZoomPreference(9.0, 18.0),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _locationAddressController,
                readOnly: true,
                decoration: InputDecoration(
                  labelText: 'Selected Location *',
                  hintText: _locationAddressController.text.isEmpty
                      ? 'Tap on the map to select location'
                      : null,
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  prefixIcon: const Icon(Icons.location_on),
                  suffixIcon: _locationAddressController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear, color: Colors.grey),
                          onPressed: () {
                            setState(() {
                              _locationAddressController.clear();
                              _listingLatitude = null;
                              _listingLongitude = null;
                              _locationMarker = null;
                            });
                          },
                        )
                      : null,
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select a location on the map';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),

              // Preferred Doer
              DropdownButtonFormField<String>(
                value: _preferredDoerGender,
                hint: const Text('Select preferred doer'),
                decoration: InputDecoration(
                  labelText: 'Preferred Doer *',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  prefixIcon: const Icon(Icons.person_search),
                ),
                items: <String>['Any', 'Male', 'Female']
                    .map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    _preferredDoerGender = newValue;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'Please select a preferred doer';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),

              // Images Section
              const Text(
                'Images (Optional)',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  ..._selectedImages.asMap().entries.map((entry) {
                    int idx = entry.key;
                    File image = entry.value;
                    return Stack(
                      children: [
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.file(
                              image,
                              width: 80,
                              height: 80,
                              fit: BoxFit.contain, // Show full image without cropping
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  width: 80,
                                  height: 80,
                                  color: Colors.grey.shade200,
                                  child: Icon(Icons.broken_image, color: Colors.grey.shade400),
                                );
                              },
                            ),
                          ),
                        ),
                        Positioned(
                          top: 4,
                          right: 4,
                          child: GestureDetector(
                            onTap: () => _removeImage(idx),
                            child: Container(
                              padding: const EdgeInsets.all(2),
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(Icons.close, color: Colors.white, size: 16),
                            ),
                          ),
                        ),
                      ],
                    );
                  }),
                  if (_selectedImages.length < 5)
                    GestureDetector(
                      onTap: _pickImage,
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey.shade300, width: 2),
                          color: Colors.grey.shade100,
                        ),
                        child: const Icon(Icons.add_a_photo, color: Colors.grey),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 24),
              const SizedBox(height: 32),

              // Next Button
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : CustomButton(
                text: 'Next',
                onPressed: _proceedToDoerSearch,
                color: Constants.primaryColor,
                textColor: Colors.white,
                borderRadius: 25.0,
                height: 50.0,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Show "Coming Soon" snackbar
  void _showComingSoonSnackbar(String paymentMethod) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$paymentMethod - Coming Soon'),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  // Payment option with coming soon functionality
  Widget _buildPaymentOptionWithComingSoon(String title, String value, {String? feeText}) {
    return InkWell(
      onTap: () {
        _showComingSoonSnackbar(title);
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.grey.shade400,
                  width: 2,
                ),
                color: Colors.transparent,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.normal,
                  color: Colors.grey.shade600,
                ),
              ),
            ),
            if (feeText != null)
              Text(
                feeText,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade500,
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Card option with coming soon functionality
  Widget _buildCardOptionWithComingSoon() {
    return InkWell(
      onTap: () {
        _showComingSoonSnackbar('Credit/Debit Card');
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.grey.shade400,
                  width: 2,
                ),
                color: Colors.transparent,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                'Credit/Debit Card',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.normal,
                  color: Colors.grey.shade600,
                ),
              ),
            ),
            Text(
              'Service fee: 3.2% + ₱30',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Bank transfer dropdown with coming soon functionality
  Widget _buildBankTransferDropdownWithComingSoon() {
    return InkWell(
      onTap: () {
        _showComingSoonSnackbar('Bank Transfer');
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.grey.shade400,
                  width: 2,
                ),
                color: Colors.transparent,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                'Bank Transfer',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.normal,
                  color: Colors.grey.shade600,
                ),
              ),
            ),
            Text(
              'Service fee: 1% + ₱20',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}