import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:hanapp/utils/api_config.dart';

class AsapAcceptanceService {
  Future<Map<String, dynamic>> respondToAsapListing({
    required int listingId,
    required int doerId,
    required String action, // 'accept' or 'reject' or 'offer'
    double? offeredPrice, // New parameter for price offers
  }) async {
    try {
      final requestBody = {
        'listing_id': listingId,
        'doer_id': doerId,
        'action': action,
      };
      
      // Add offered price if provided
      if (offeredPrice != null) {
        requestBody['offered_price'] = offeredPrice;
      }
      
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/asap/accept_listing.php'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data;
      } else {
        return {
          'success': false,
          'message': 'Server error: ${response.statusCode}',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: $e',
      };
    }
  }
}