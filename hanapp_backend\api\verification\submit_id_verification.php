<?php
// hanapp_backend/api/verification/submit_id_verification.php
// Handles submission of ID verification documents

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

require_once '../../config/db_connect.php';

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $userId = $_POST['user_id'] ?? null;
    $idType = $_POST['id_type'] ?? null;
    $confirmation = $_POST['confirmation'] ?? null;

    if (empty($userId) || !is_numeric($userId)) {
        throw new Exception("User ID is required and must be numeric.");
    }

    if (empty($idType)) {
        throw new Exception("ID type is required.");
    }

    if (!isset($_FILES['id_photo_front']) || !isset($_FILES['id_photo_back']) || !isset($_FILES['brgy_clearance_photo'])) {
        throw new Exception("All required photos (ID front, ID back, Barangay clearance) are required.");
    }

    // Create upload directories
    $uploadDirs = [
        'id_front' => '../../uploads/id_photos/front/',
        'id_back' => '../../uploads/id_photos/back/',
        'brgy_clearance' => '../../uploads/brgy_clearance/'
    ];

    foreach ($uploadDirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
        }
    }

    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/heic', 'image/heif', 'image/webp'];
    $maxFileSize = 1 * 1024 * 1024; // 1 MB

    $uploadedFiles = [];

    // Debug: Log received files
    error_log("submit_id_verification.php: Received files: " . print_r(array_keys($_FILES), true));

    // Process each file
    $files = [
        'id_photo_front' => ['dir' => $uploadDirs['id_front'], 'prefix' => 'id_front'],
        'id_photo_back' => ['dir' => $uploadDirs['id_back'], 'prefix' => 'id_back'],
        'brgy_clearance_photo' => ['dir' => $uploadDirs['brgy_clearance'], 'prefix' => 'brgy_clearance']
    ];

    foreach ($files as $fileKey => $config) {
        $file = $_FILES[$fileKey];
        
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception("$fileKey upload error: " . $file['error']);
        }

        // Check MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $actualMimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        if (!in_array($actualMimeType, $allowedTypes)) {
            throw new Exception("$fileKey: Invalid file type. Allowed: JPG, PNG, GIF, HEIC, WebP. Detected: $actualMimeType");
        }

        if ($file['size'] > $maxFileSize) {
            throw new Exception("$fileKey: File size exceeds 1MB limit.");
        }

        // Generate unique filename
        $fileName = uniqid($config['prefix'] . '_') . '_' . $userId . '.' . pathinfo($file['name'], PATHINFO_EXTENSION);
        $filePath = $config['dir'] . $fileName;
        
        if (!move_uploaded_file($file['tmp_name'], $filePath)) {
            throw new Exception("Failed to move $fileKey.");
        }

        $uploadedFiles[$fileKey] = str_replace('../../', '', $filePath);
    }

    // Debug: Log uploaded files
    error_log("submit_id_verification.php: Uploaded files: " . print_r($uploadedFiles, true));

    // Start database transaction
    $conn->begin_transaction();

    // Update users table with photo URLs
    $updateUserStmt = $conn->prepare("
        UPDATE users 
        SET 
            id_photo_front_url = ?,
            id_photo_back_url = ?,
            brgy_clearance_photo_url = ?,
            verification_status = 'pending',
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
    ");
    
    if (!$updateUserStmt) {
        throw new Exception("Failed to prepare user update statement: " . $conn->error);
    }

    $updateUserStmt->bind_param("sssi", 
        $uploadedFiles['id_photo_front'], 
        $uploadedFiles['id_photo_back'], 
        $uploadedFiles['brgy_clearance_photo'], 
        $userId
    );

    if (!$updateUserStmt->execute()) {
        throw new Exception("Failed to update user: " . $updateUserStmt->error);
    }

    // Check if verification record already exists
    $checkStmt = $conn->prepare("SELECT id FROM verifications WHERE user_id = ?");
    if (!$checkStmt) {
        throw new Exception("Failed to prepare check statement: " . $conn->error);
    }
    
    $checkStmt->bind_param("i", $userId);
    $checkStmt->execute();
    $result = $checkStmt->get_result();

    if ($result->num_rows > 0) {
        // Update existing verification record - sync all photos to match users table
        $updateVerificationStmt = $conn->prepare("
            UPDATE verifications
            SET
                id_type = ?,
                id_photo_front_url = ?,
                id_photo_back_url = ?,
                brgy_clearance_photo_url = ?,
                face_scan_photo_url = (SELECT live_photo_url FROM users WHERE id = ?),
                confirmation_status = ?,
                status = 'pending',
                submitted_at = CURRENT_TIMESTAMP,
                reviewed_at = NULL,
                reviewer_id = NULL,
                rejection_reason = NULL
            WHERE user_id = ?
        ");

        if (!$updateVerificationStmt) {
            throw new Exception("Failed to prepare verification update statement: " . $conn->error);
        }

        $confirmationBool = ($confirmation === 'true' || $confirmation === true) ? 1 : 0;

        $updateVerificationStmt->bind_param("sssiiiii",
            $idType,
            $uploadedFiles['id_photo_front'],
            $uploadedFiles['id_photo_back'],
            $uploadedFiles['brgy_clearance_photo'],
            $userId, // for face_scan_photo_url subquery
            $confirmationBool,
            $userId  // for WHERE clause
        );

        if (!$updateVerificationStmt->execute()) {
            throw new Exception("Failed to update verification: " . $updateVerificationStmt->error);
        }

        error_log("submit_id_verification.php: Updated verification record for user $userId - synced all photos");
    } else {
        // Create new verification record - sync all photos to match users table
        $insertVerificationStmt = $conn->prepare("
            INSERT INTO verifications (
                user_id,
                id_type,
                id_photo_front_url,
                id_photo_back_url,
                brgy_clearance_photo_url,
                face_scan_photo_url,
                confirmation_status,
                status,
                submitted_at
            ) VALUES (?, ?, ?, ?, ?, (SELECT live_photo_url FROM users WHERE id = ?), ?, 'pending', CURRENT_TIMESTAMP)
        ");

        if (!$insertVerificationStmt) {
            throw new Exception("Failed to prepare verification insert statement: " . $conn->error);
        }

        $confirmationBool = ($confirmation === 'true' || $confirmation === true) ? 1 : 0;

        $insertVerificationStmt->bind_param("isssiii",
            $userId,
            $idType,
            $uploadedFiles['id_photo_front'],
            $uploadedFiles['id_photo_back'],
            $uploadedFiles['brgy_clearance_photo'],
            $userId, // for face_scan_photo_url subquery
            $confirmationBool
        );

        if (!$insertVerificationStmt->execute()) {
            throw new Exception("Failed to insert verification: " . $insertVerificationStmt->error);
        }

        error_log("submit_id_verification.php: Created new verification record for user $userId - synced all photos");
    }

    $conn->commit();

    echo json_encode([
        "success" => true,
        "message" => "ID verification submitted successfully! Your verification is now pending review.",
        "id_photo_front_url" => $uploadedFiles['id_photo_front'],
        "id_photo_back_url" => $uploadedFiles['id_photo_back'],
        "brgy_clearance_photo_url" => $uploadedFiles['brgy_clearance_photo'],
        "verification_status" => "pending"
    ]);

} catch (Exception $e) {
    if (isset($conn) && $conn instanceof mysqli && $conn->in_transaction) {
        $conn->rollback();
    }
    http_response_code(500);
    error_log("submit_id_verification.php: Caught exception: " . $e->getMessage(), 0);
    echo json_encode([
        "success" => false,
        "message" => "Error during ID verification submission: " . $e->getMessage()
    ]);
} finally {
    if (isset($conn) && $conn instanceof mysqli && $conn->ping()) {
        $conn->close();
    }
}
?>
