<?php
// hanapp_backend/api/wallet/create_job_payment.php
// Creates Xendit invoice for job payments with proper fee handling

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

require_once '../../config/db_connect.php';

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(["success" => false, "message" => "Method not allowed"]);
    exit();
}

if (!isset($conn) || $conn->connect_error) {
    error_log("create_job_payment.php: Database connection not established: " . $conn->connect_error);
    echo json_encode(["success" => false, "message" => "Database connection not established."]);
    exit();
}

try {
    // Log the start of the request
    error_log("Job payment request started at " . date('Y-m-d H:i:s'));

    // Get JSON input
    $rawInput = file_get_contents('php://input');
    error_log("Job payment raw input: " . $rawInput);

    $input = json_decode($rawInput, true);

    if (!$input) {
        throw new Exception("Invalid JSON input. Raw input: " . $rawInput);
    }

    error_log("Job payment parsed input: " . json_encode($input));

    // Validate required fields
    $listerId = $input['lister_id'] ?? null;
    $applicationId = $input['application_id'] ?? null;
    $doerFee = $input['doer_fee'] ?? null;
    $transactionFee = $input['transaction_fee'] ?? 25.0; // Default ₱25 platform fee
    $paymentMethod = $input['payment_method'] ?? 'gcash';
    $listerEmail = $input['lister_email'] ?? null;
    $listerFullName = $input['lister_full_name'] ?? null;

    if (empty($listerId) || !is_numeric($listerId)) {
        throw new Exception("Lister ID is required and must be numeric.");
    }

    if (empty($applicationId) || !is_numeric($applicationId)) {
        throw new Exception("Application ID is required and must be numeric.");
    }

    if (empty($doerFee) || !is_numeric($doerFee) || $doerFee <= 0) {
        throw new Exception("Doer fee is required and must be a positive number.");
    }

    $doerFee = floatval($doerFee);
    $transactionFee = floatval($transactionFee);
    $totalAmount = $doerFee + $transactionFee;

    // Get application details and validate
    $stmt = $conn->prepare("
        SELECT a.id, a.doer_id, a.listing_id, a.status, l.title as listing_title, 
               u.full_name as doer_name, u.email as doer_email
        FROM applicationsv2 a
        JOIN listings l ON a.listing_id = l.id
        JOIN users u ON a.doer_id = u.id
        WHERE a.id = ? AND a.lister_id = ? AND a.status = 'in_progress'
    ");

    if ($stmt === false) {
        throw new Exception("Failed to prepare application lookup statement.");
    }

    $stmt->bind_param("ii", $applicationId, $listerId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        $stmt->close();
        throw new Exception("Application not found or not in progress.");
    }

    $application = $result->fetch_assoc();
    $stmt->close();

    $doerId = $application['doer_id'];
    $listingTitle = $application['listing_title'];

    // Get lister details if not provided
    $stmt = $conn->prepare("SELECT full_name, email FROM users WHERE id = ?");
    if ($stmt === false) {
        throw new Exception("Failed to prepare lister lookup statement.");
    }

    $stmt->bind_param("i", $listerId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        $stmt->close();
        throw new Exception("Lister not found.");
    }

    $lister = $result->fetch_assoc();
    $stmt->close();

    // Use database lister info if not provided
    if (empty($listerFullName)) {
        $listerFullName = $lister['full_name'];
    }
    if (empty($listerEmail)) {
        $listerEmail = $lister['email'];
    }

    // Xendit API configuration
    $xenditSecretKey = 'xnd_production_k5NqlGpmZlTPGEvBlYrk7a9ukwr8b2DzfQtEh3YThOcZazymwOlXwFT5ZEHIZm2';
    $xenditBaseUrl = 'https://api.xendit.co';

    // Generate unique external ID
    $timestamp = time();
    $externalId = "hanapp_jobpay_{$applicationId}_{$listerId}_{$timestamp}";

    // Create Xendit invoice request
    $invoiceData = [
        'external_id' => $externalId,
        'amount' => $totalAmount,
        'description' => "Job Payment: {$listingTitle} (₱" . number_format($doerFee, 2) . " + ₱" . number_format($transactionFee, 2) . " fee)",
        'invoice_duration' => 86400, // 24 hours
        'currency' => 'PHP',
        'customer' => [
            'given_names' => $listerFullName,
            'email' => $listerEmail,
        ],
        'customer_notification_preference' => [
            'invoice_created' => ['email'],
            'invoice_reminder' => ['email'],
            'invoice_paid' => ['email'],
            'invoice_expired' => ['email'],
        ],
        'success_redirect_url' => 'https://hanapp.com/payment/success',
        'failure_redirect_url' => 'https://hanapp.com/payment/failed',
        'items' => [
            [
                'name' => "Job Payment - {$listingTitle}",
                'quantity' => 1,
                'price' => $totalAmount,
                'category' => 'Service Payment',
            ]
        ],
        'metadata' => [
            'application_id' => $applicationId,
            'lister_id' => $listerId,
            'doer_id' => $doerId,
            'doer_fee' => $doerFee,
            'transaction_fee' => $transactionFee,
            'payment_type' => 'job_payment',
        ],
    ];

    // Configure payment methods
    $paymentMethods = [];
    switch ($paymentMethod) {
        case 'gcash':
            $paymentMethods = ['EWALLET'];
            $invoiceData['payment_methods'] = ['GCASH'];
            break;
        case 'paymaya':
            $paymentMethods = ['EWALLET'];
            $invoiceData['payment_methods'] = ['PAYMAYA'];
            break;
        case 'bank_transfer':
            $paymentMethods = ['BANK_TRANSFER'];
            break;
        case 'card':
            $paymentMethods = ['CREDIT_CARD', 'DEBIT_CARD'];
            break;
        default:
            $paymentMethods = ['GCASH', 'PAYMAYA', 'BANK_TRANSFER'];
    }

    if (!empty($paymentMethods)) {
        $invoiceData['available_banks'] = $paymentMethods;
    }

    // Create Xendit invoice
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $xenditBaseUrl . '/v2/invoices');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($invoiceData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Basic ' . base64_encode($xenditSecretKey . ':'),
        'Content-Type: application/json',
    ]);

    $xenditResponse = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    error_log("Job Payment Xendit Response Code: $httpCode");
    error_log("Job Payment Xendit Response: $xenditResponse");

    if ($httpCode !== 200 && $httpCode !== 201) {
        throw new Exception("Failed to create payment invoice. HTTP Code: $httpCode, Response: $xenditResponse");
    }

    $invoiceResponse = json_decode($xenditResponse, true);
    if (!$invoiceResponse || !isset($invoiceResponse['id'])) {
        error_log("Job Payment Invalid Xendit Response: $xenditResponse");
        throw new Exception("Invalid response from payment provider.");
    }

    // Record job payment transaction in database
    $stmt = $conn->prepare("
        INSERT INTO job_payment_transactions (
            user_id, application_id, doer_id, doer_fee, transaction_fee, total_amount,
            status, payment_method, description, xendit_invoice_id, xendit_external_id,
            created_at
        ) VALUES (?, ?, ?, ?, ?, ?, 'pending', ?, ?, ?, ?, CURRENT_TIMESTAMP)
    ");

    if ($stmt === false) {
        throw new Exception("Failed to prepare job payment transaction insert statement.");
    }

    $description = "Job Payment: {$listingTitle}";

    $stmt->bind_param(
        "iiidddssss",
        $listerId,
        $applicationId,
        $doerId,
        $doerFee,
        $transactionFee,
        $totalAmount,
        $paymentMethod,
        $description,
        $invoiceResponse['id'],
        $externalId
    );

    if (!$stmt->execute()) {
        $stmt->close();
        error_log("Job Payment Database Error: " . $stmt->error);
        throw new Exception("Failed to record job payment transaction.");
    }

    $transactionId = $conn->insert_id;
    $stmt->close();

    error_log("Job payment transaction created: ID $transactionId, Application $applicationId, Total ₱$totalAmount");

    // Return success response
    echo json_encode([
        "success" => true,
        "message" => "Job payment invoice created successfully",
        "payment_details" => [
            "invoice_id" => $invoiceResponse['id'],
            "external_id" => $externalId,
            "invoice_url" => $invoiceResponse['invoice_url'],
            "amount" => $totalAmount,
            "doer_fee" => $doerFee,
            "transaction_fee" => $transactionFee,
            "expires_at" => $invoiceResponse['expiry_date'],
            "transaction_id" => $transactionId
        ]
    ]);

} catch (Exception $e) {
    error_log("Job Payment Error: " . $e->getMessage());
    echo json_encode([
        "success" => false,
        "message" => $e->getMessage()
    ]);
}

$conn->close();
?>
