# Enhanced App Lifecycle Service for Online Status Management

## Overview
The AppLifecycleService has been enhanced to provide automatic online/offline status management based on app lifecycle events, replacing the previous periodic timer approach with immediate, responsive status changes.

## Key Improvements

### 🚀 **Immediate Response to App Lifecycle Events**
- **App Paused/Hidden**: Automatically saves current online status and marks app as in background
- **App Resumed**: Restores online status if user was online before going to background
- **App Detached**: Sets user offline when app is terminated
- **No Periodic Timers**: Removed inefficient periodic status checking

### 📱 **Smart Background Detection**
- Tracks when app goes to background (`_wasInBackground` flag)
- Only restores online status when returning from background, not from brief interruptions
- <PERSON>les both `paused` and `hidden` states for cross-platform compatibility

### ⚡ **Performance Benefits**
- **Reduced API Calls**: No more periodic status checks every 2 minutes
- **Battery Efficient**: Only updates status when app state actually changes
- **Real-time Response**: Immediate status changes based on user behavior

## How It Works

### 1. **App Goes to Background** (Paused/Hidden)
```dart
// Save current online status (user's intended status)
await _saveWasOnlineBeforeBackground(_isOnline);

// Only set offline if user was actually online (not manually set offline)
if (_isOnline) {
  await _setOnlineStatus(false);
} else {
  // User was already offline, no database update needed
}

// Mark app as in background
_wasInBackground = true;

// Update last active time
await _updateLastActiveTime();
```

### 2. **App Returns to Foreground** (Resumed)
```dart
// Check if app was in background
if (_wasInBackground && _currentUser?.role == 'doer') {
  // Restore the user's intended status (what they had before going to background)
  final wasOnline = await _getWasOnlineBeforeBackground();
  
  // Restore the user's intended status
  if (wasOnline != _isOnline) {
    await _setOnlineStatus(wasOnline);
  }
  _wasInBackground = false;
}
```

### 3. **App Terminated** (Detached)
```dart
// Set user offline immediately
if (_currentUser?.role == 'doer' && _isOnline) {
  await _setOnlineStatus(false);
}
```

## Lifecycle Event Flow

```
App Open → Initialize → Set Doer Online (if applicable)
    ↓
User Uses App → Online Status Active
    ↓
App Paused/Hidden → Save Status → Set Offline in DB → Mark Background
    ↓
App Resumed → Check Background Flag → Restore Online Status in DB
    ↓
App Detached → Set Offline → Clean Up
```

## API Integration

### Status Update API Call
```dart
Future<void> _setOnlineStatus(bool isOnline) async {
  final response = await AuthService().updateAvailabilityStatus(
    userId: _currentUser!.id!,
    isAvailable: isOnline,
  );
  
  if (response['success']) {
    _isOnline = isOnline;
    await _updateUserLocalState(isOnline);
  }
}
```

### Error Handling
- Handles API endpoint configuration errors
- Provides detailed logging for debugging
- Maintains local state consistency

## User Experience Benefits

### For Doers
- **Automatic Online**: Set online when app opens (if not manually set offline)
- **Smart Background Handling**: Only set offline if user was actually online
- **Status Preservation**: Restore user's intended status when app resumes
- **Manual Control**: Respects user's manual online/offline choices

### For Listers
- **Accurate Availability**: See real-time doer availability
- **Reliable Status**: Status reflects actual app usage
- **Better Matching**: More accurate doer recommendations

## Configuration

### Inactivity Threshold
```dart
static const Duration _inactivityThreshold = Duration(minutes: 5);
```
- Currently set to 5 minutes (unused in new implementation)
- Can be adjusted based on business requirements

### Debounce Timer
```dart
Timer? _debounceTimer;
```
- Prevents rapid successive API calls
- 300ms debounce for manual status toggles

## Testing

### Unit Tests
- `test_app_lifecycle_online_status.dart` - Comprehensive test suite
- Tests all lifecycle events
- Verifies background state management
- Tests error handling and cleanup

### Manual Testing Scenarios
1. **Open App**: Verify doer goes online
2. **Background App**: Verify status is saved
3. **Resume App**: Verify status is restored
4. **Close App**: Verify doer goes offline
5. **Switch Apps**: Verify no status change for brief interruptions

## Integration Points

### Main App
```dart
// In main.dart
AppLifecycleService.instance.handleAppLifecycleState(state);
```

### Settings Screen
```dart
// Manual status toggle
AppLifecycleService.instance.toggleOnlineStatus(isOnline);
```

### User Service
```dart
// Status synchronization
await AppLifecycleService.instance.forceRefreshStatus();
```

## Monitoring and Debugging

### Log Messages
- `AppLifecycleService: App resumed`
- `AppLifecycleService: App paused`
- `AppLifecycleService: App hidden`
- `AppLifecycleService: App detached`
- `AppLifecycleService: Saved online status before background: true`
- `AppLifecycleService: Restoring online status after app resume`

### Debug Properties
- `wasInBackground`: Check if app was in background
- `lastActiveTime`: Last user activity timestamp
- `isOnline`: Current online status
- `isDoer`: Whether user is a doer

## Future Enhancements

### Potential Improvements
1. **Network State Integration**: Consider network connectivity
2. **Battery Optimization**: Respect system battery saver settings
3. **Geofencing**: Location-based availability
4. **Schedule Integration**: Automatic status based on work hours
5. **Push Notification Integration**: Status updates via notifications

### Analytics
- Track status change frequency
- Monitor API call efficiency
- Measure user engagement patterns

## Files Modified
- `lib/services/app_lifecycle_service.dart` - Enhanced service
- `test_app_lifecycle_online_status.dart` - New test suite
- `ENHANCED_APP_LIFECYCLE_SERVICE.md` - This documentation

## Benefits Summary
✅ **Immediate Response**: No delays in status updates
✅ **Battery Efficient**: Reduced background processing
✅ **User Friendly**: Automatic status management
✅ **Reliable**: Consistent with actual app usage
✅ **Scalable**: Efficient API usage
✅ **Testable**: Comprehensive test coverage 