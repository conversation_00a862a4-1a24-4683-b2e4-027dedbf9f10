<?php
// log_login_history.php
// Function to log user login history - can be included in any login script

function logLoginHistory($conn, $user_id, $location = null, $device_info = null, $ip_address = null, $latitude = null, $longitude = null) {
    try {
        // If location is not provided, try to get it from GPS coordinates first, then fallback to IP
        if ($location === null) {
            // Try GPS-based geolocation first with enhanced accuracy
            if ($latitude !== null && $longitude !== null) {
                $location = getRobustLocationFromCoordinates($latitude, $longitude);
            }
            
            
            // Fallback to IP-based geolocation if GPS failed
            if ($location === 'Unknown' || $location === null) {
                $ip = $ip_address ?? $_SERVER['REMOTE_ADDR'] ?? '';
                if (!empty($ip) && filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    try {
                        // Use cURL instead of file_get_contents for better error handling
                        $ch = curl_init();
                        curl_setopt($ch, CURLOPT_URL, "http://ip-api.com/json/$ip");
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_TIMEOUT, 5); // 5 second timeout
                        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
                        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json']);
                        
                        $geo_response = curl_exec($ch);
                        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                        curl_close($ch);
                        
                        if ($geo_response && $http_code === 200) {
                            $geo = json_decode($geo_response);
                            if ($geo && $geo->status === 'success') {
                                $location = $geo->city . ', ' . $geo->country;
                            } else {
                                $location = 'Unknown';
                            }
                        } else {
                            $location = 'Unknown';
                        }
                    } catch (Exception $geo_e) {
                        error_log("IP Geolocation API error: " . $geo_e->getMessage());
                        $location = 'Unknown';
                    }
                } else {
                    $location = 'Unknown';
                }
            }
        }
        
        // If device info is not provided, get it from user agent
        if ($device_info === null) {
            $device_info = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
        }
        
        // If IP address is not provided, get it from server
        if ($ip_address === null) {
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
        }
        
        // Insert into login_history table
        $stmt = $conn->prepare("INSERT INTO login_history (user_id, login_timestamp, location, device_info, ip_address) VALUES (?, NOW(), ?, ?, ?)");
        if ($stmt) {
            $stmt->bind_param("isss", $user_id, $location, $device_info, $ip_address);
            $insert_result = $stmt->execute();
            
            if ($insert_result) {
                error_log("log_login_history.php: Login history inserted successfully for user ID: $user_id");
                return true;
            } else {
                error_log("log_login_history.php: Login history insert failed for user ID: $user_id. Error: " . $stmt->error);
                return false;
            }
            $stmt->close();
        } else {
            error_log("log_login_history.php: Failed to prepare login history statement. Error: " . $conn->error);
            return false;
        }
    } catch (Exception $e) {
        error_log("log_login_history.php: Exception during login history insert: " . $e->getMessage());
        return false;
    }
}

// Alternative function for PDO connections
function logLoginHistoryPDO($pdo, $user_id, $location = null, $device_info = null, $ip_address = null, $latitude = null, $longitude = null) {
    try {
        // If location is not provided, try to get it from GPS coordinates first, then fallback to IP
        if ($location === null) {
            // Try GPS-based geolocation first with enhanced accuracy
            if ($latitude !== null && $longitude !== null) {
                $location = getRobustLocationFromCoordinates($latitude, $longitude);
            }
            
            // Fallback to IP-based geolocation if GPS failed
            if ($location === 'Unknown' || $location === null) {
                $ip = $ip_address ?? $_SERVER['REMOTE_ADDR'] ?? '';
                if (!empty($ip) && filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    try {
                        // Use cURL instead of file_get_contents for better error handling
                        $ch = curl_init();
                        curl_setopt($ch, CURLOPT_URL, "http://ip-api.com/json/$ip");
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_TIMEOUT, 5); // 5 second timeout
                        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
                        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json']);
                        
                        $geo_response = curl_exec($ch);
                        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                        curl_close($ch);
                        
                        if ($geo_response && $http_code === 200) {
                            $geo = json_decode($geo_response);
                            if ($geo && $geo->status === 'success') {
                                $location = $geo->city . ', ' . $geo->country;
                            } else {
                                $location = 'Unknown';
                            }
                        } else {
                            $location = 'Unknown';
                        }
                    } catch (Exception $geo_e) {
                        error_log("IP Geolocation API error: " . $geo_e->getMessage());
                        $location = 'Unknown';
                    }
                } else {
                    $location = 'Unknown';
                }
            }
        }
        
        // If device info is not provided, get it from user agent
        if ($device_info === null) {
            $device_info = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
        }
        
        // If IP address is not provided, get it from server
        if ($ip_address === null) {
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
        }
        
        // Insert into login_history table using PDO
        $stmt = $pdo->prepare("INSERT INTO login_history (user_id, login_timestamp, location, device_info, ip_address) VALUES (?, NOW(), ?, ?, ?)");
        if ($stmt) {
            $insert_result = $stmt->execute([$user_id, $location, $device_info, $ip_address]);
            
            if ($insert_result) {
                error_log("log_login_history.php: Login history inserted successfully for user ID: $user_id (PDO)");
                return true;
            } else {
                error_log("log_login_history.php: Login history insert failed for user ID: $user_id (PDO)");
                return false;
            }
        } else {
            error_log("log_login_history.php: Failed to prepare login history statement (PDO)");
            return false;
        }
    } catch (Exception $e) {
        error_log("log_login_history.php: Exception during login history insert (PDO): " . $e->getMessage());
        return false;
    }
}

// Enhanced location resolution function with multiple strategies
function getRobustLocationFromCoordinates($latitude, $longitude) {
    // Strategy 1: Google Geocoding API with comprehensive address parsing
    $location = getLocationFromGoogleAPI($latitude, $longitude);
    if ($location !== 'Unknown') {
        return $location;
    }
    
    // Strategy 2: Alternative geocoding service (OpenStreetMap Nominatim)
    $location = getLocationFromNominatim($latitude, $longitude);
    if ($location !== 'Unknown') {
        return $location;
    }
    
    // Strategy 3: Coordinate-based fallback
    return "Coordinates: {$latitude}, {$longitude}";
}

// Google Geocoding API with enhanced address component parsing
function getLocationFromGoogleAPI($latitude, $longitude) {
    try {
        $api_key = 'AIzaSyBjsINSWCpbr6wFTZkNJKoQz2gg_Hl_8Aw';
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://maps.googleapis.com/maps/api/geocode/json?latlng={$latitude},{$longitude}&key={$api_key}&result_type=street_address|sublocality|locality|administrative_area_level_2");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 8);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json']);
        
        $geo_response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($geo_response && $http_code === 200) {
            $geo_data = json_decode($geo_response, true);
            if ($geo_data && $geo_data['status'] === 'OK' && !empty($geo_data['results'])) {
                // Try multiple results for best match
                foreach ($geo_data['results'] as $result) {
                    $address_components = $result['address_components'];
                    
                    $street = '';
                    $sublocality = '';
                    $locality = '';
                    $admin_area_2 = '';
                    $admin_area_1 = '';
                    $country = '';
                    
                    foreach ($address_components as $component) {
                        $types = $component['types'];
                        if (in_array('street_number', $types) || in_array('route', $types)) {
                            $street = $component['long_name'];
                        } elseif (in_array('sublocality_level_1', $types) || in_array('sublocality', $types)) {
                            $sublocality = $component['long_name'];
                        } elseif (in_array('administrative_area_level_2', $types)) {
                            $admin_area_2 = $component['long_name'];
                        } elseif (in_array('locality', $types)) {
                            $locality = $component['long_name'];
                        } elseif (in_array('administrative_area_level_1', $types)) {
                            $admin_area_1 = $component['long_name'];
                        } elseif (in_array('country', $types)) {
                            $country = $component['long_name'];
                        }
                    }
                    
                    // Enhanced priority system: sublocality > admin_area_2 > locality
                    $primary_location = $sublocality ?: ($admin_area_2 ?: $locality);
                    
                    if (!empty($primary_location)) {
                        $location_parts = array_filter([$primary_location, $admin_area_1, $country]);
                        $location = implode(', ', $location_parts);
                        
                        // Validate location is in Philippines for accuracy
                        if (stripos($country, 'philippines') !== false || stripos($admin_area_1, 'philippines') !== false) {
                            error_log("Google API Location found: {$location} for coordinates {$latitude}, {$longitude}");
                            return $location;
                        }
                    }
                }
            }
        }
    } catch (Exception $e) {
        error_log("Google Geocoding API error: " . $e->getMessage());
    }
    
    return 'Unknown';
}

// Alternative geocoding using OpenStreetMap Nominatim
function getLocationFromNominatim($latitude, $longitude) {
    try {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://nominatim.openstreetmap.org/reverse?format=json&lat={$latitude}&lon={$longitude}&zoom=18&addressdetails=1");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_setopt($ch, CURLOPT_USERAGENT, 'HanApp Location Service');
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json']);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($response && $http_code === 200) {
            $data = json_decode($response, true);
            if ($data && isset($data['address'])) {
                $address = $data['address'];
                
                $city = $address['city'] ?? $address['town'] ?? $address['village'] ?? $address['municipality'] ?? '';
                $state = $address['state'] ?? $address['province'] ?? '';
                $country = $address['country'] ?? '';
                
                if (!empty($city) && stripos($country, 'philippines') !== false) {
                    $location_parts = array_filter([$city, $state, $country]);
                    $location = implode(', ', $location_parts);
                    error_log("Nominatim Location found: {$location} for coordinates {$latitude}, {$longitude}");
                    return $location;
                }
            }
        }
    } catch (Exception $e) {
        error_log("Nominatim Geocoding error: " . $e->getMessage());
    }
    
    return 'Unknown';
}

// Handle direct API calls from Flutter app
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['user_id'])) {
    require_once 'db_connect.php';
    
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit();
    }
    
    try {
        $input = file_get_contents("php://input");
        $data = json_decode($input, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo json_encode(['success' => false, 'message' => 'Invalid JSON payload']);
            exit();
        }
        
        $userId = $data['user_id'] ?? null;
        $deviceInfo = $data['device_info'] ?? null;
        $latitude = $data['latitude'] ?? null;
        $longitude = $data['longitude'] ?? null;
        
        if (!$userId) {
            echo json_encode(['success' => false, 'message' => 'Missing user_id']);
            exit();
        }
        
        // Log the login history with GPS coordinates and device information from Flutter
        $result = logLoginHistory($conn, $userId, null, $deviceInfo, null, $latitude, $longitude);
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'Login history logged successfully',
                'device_info' => $deviceInfo
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Failed to log login history'
            ]);
        }
        
    } catch (Exception $e) {
        error_log("log_login_history.php API: Error: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => 'An error occurred: ' . $e->getMessage()
        ]);
    } finally {
        if (isset($conn)) {
            $conn->close();
        }
    }
}
?>