import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geocoding/geocoding.dart' as geocoding;
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geocoding/geocoding.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'dart:math' as math;

// Import both listing services with aliases to avoid naming conflicts
import 'package:hanapp/utils/asap_listing_service.dart';
import 'package:hanapp/utils/public_listing_service.dart' as PublicListingService;
import 'package:hanapp/models/asap_listing.dart';
import 'package:hanapp/models/public_listing.dart';
import 'package:hanapp/utils/location_service.dart';
import 'package:geolocator/geolocator.dart';

import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/utils/word_filter_service.dart';
import 'package:hanapp/widgets/banned_words_dialog.dart';
import 'package:hanapp/services/image_upload_service.dart';
import 'package:hanapp/screens/components/custom_button.dart';
import 'package:hanapp/utils/api_config.dart';

class CombinedListingFormScreen extends StatefulWidget {
  final int? listingId; // Optional: for editing existing listing
  final String? listingType; // Optional: 'ASAP' or 'Public' for editing

  const CombinedListingFormScreen({super.key, this.listingId, this.listingType});

  @override
  State<CombinedListingFormScreen> createState() => _CombinedListingFormScreenState();
}

class _CombinedListingFormScreenState extends State<CombinedListingFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _locationSearchController = TextEditingController();
  final TextEditingController _locationAddressController = TextEditingController();
  final TextEditingController _tagsController = TextEditingController(); // NEW: Tags controller for Public listings



  GoogleMapController? _mapController;
  LatLng _selectedLocation = const LatLng(14.5995, 120.9842); // Center of Philippines (Manila)
  Marker? _locationMarker;
  double? _listingLatitude;
  double? _listingLongitude;

  // Philippines geographical bounds
  static final LatLngBounds _philippinesBounds = LatLngBounds(
    southwest: const LatLng(4.0, 114.0), // Southwest corner of Philippines
    northeast: const LatLng(21.0, 127.0), // Northeast corner of Philippines
  );

  String? _selectedListingType; // 'ASAP' or 'Public' - determines form behavior
  String? _selectedCategory; // 'Onsite', 'Hybrid', 'Remote' - only for Public listings
  String? _preferredDoerGender; // 'Male', 'Female', 'Any'
  List<File> _selectedImages = []; // For new local image files to be uploaded
  List<String> _existingImageUrls = []; // For existing images from database
  final ImagePicker _picker = ImagePicker();



  bool _isLoading = false; // General loading indicator
  bool _isGettingLocation = false; // Add this variable
  final LocationService _locationService = LocationService(); // Add this variable
  bool _isEditing = false; // Flag to indicate if we are editing an existing listing

  // Store loaded listing data for editing
  AsapListing? _currentAsapListing;
  PublicListing? _currentPublicListing;



  // Price change tracking for edit mode
  double? _originalPrice; // Store original price when editing
  bool get _priceChanged => _isEditing && _originalPrice != null &&
      (double.tryParse(_priceController.text) ?? 0.0) != _originalPrice;

  // Custom input formatter for Philippine peso amounts
  static final RegExp _pesoRegex = RegExp(r'^\d*\.?\d{0,2}$');

  List<TextInputFormatter> get _pesoInputFormatters => [
    FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
    TextInputFormatter.withFunction((oldValue, newValue) {
      // Allow empty string
      if (newValue.text.isEmpty) {
        return newValue;
      }

      // Check if the new value matches our peso format
      if (_pesoRegex.hasMatch(newValue.text)) {
        // Ensure only one decimal point
        if (newValue.text.split('.').length <= 2) {
          return newValue;
        }
      }

      // If invalid, keep the old value
      return oldValue;
    }),
  ];

  @override
  void initState() {
    super.initState();
    // Initialize map and location defaults
    _updateLocationMarker(_selectedLocation);
    _reverseGeocodeLocation(_selectedLocation);


    // Check if we are in edit mode
    if (widget.listingId != null && widget.listingType != null) {
      _isEditing = true;
      _selectedListingType = widget.listingType; // Set the listing type based on what's being edited
      _loadListingForEdit(widget.listingId!, widget.listingType!);
    } else {
      // For new listing creation, default to ASAP and show its warning
      _selectedListingType = 'ASAP';
      _showAsapWarningDialog();
    }
  }

  // Check if a location is within Philippines bounds
  bool _isLocationInPhilippines(LatLng location) {
    return location.latitude >= _philippinesBounds.southwest.latitude &&
        location.latitude <= _philippinesBounds.northeast.latitude &&
        location.longitude >= _philippinesBounds.southwest.longitude &&
        location.longitude <= _philippinesBounds.northeast.longitude;
  }

  // Check if an address contains Philippines-related keywords
  bool _isAddressInPhilippines(String address) {
    final philippinesKeywords = [
      'philippines', 'filipino', 'pinoy', 'pinay', 'metro manila', 'manila',
      'quezon city', 'caloocan', 'las piñas', 'makati', 'malabon', 'mandaluyong',
      'marikina', 'muntinlupa', 'navotas', 'parañaque', 'pasay', 'pasig',
      'san juan', 'taguig', 'valenzuela', 'pateros'
    ];

    final lowerAddress = address.toLowerCase();
    return philippinesKeywords.any((keyword) => lowerAddress.contains(keyword));
  }

  // Validate location using reverse geocoding (same as sign-up logic)
  Future<bool> _validateLocationByPhilippines(LatLng location) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(
        location.latitude,
        location.longitude,
      );

      if (placemarks.isNotEmpty) {
        final placemark = placemarks[0];
        // Check if location is in Philippines
        final country = placemark.country?.toLowerCase() ?? '';
        final province = placemark.administrativeArea?.toLowerCase() ?? '';

        bool isQualified = (country == 'philippines') ||
            (province.contains('philippines'));

        print('🔍 Location validation: Country = ${placemark.country}, Province = ${placemark.administrativeArea}, Qualified = $isQualified');
        return isQualified;
      }

      return false;
    } catch (e) {
      print('❌ Error validating location: $e');
      return false;
    }
  }

  // NEW: Method to load existing listing data for editing
  Future<void> _loadListingForEdit(int id, String type) async {
    setState(() {
      _isLoading = true; // Show loading indicator while fetching data
    });

    dynamic response;
    if (type == 'ASAP') {
      response = await AsapListingService().getAsapListingDetails(id);
    } else { // Public
      response = await PublicListingService.ListingService().getListingDetails(id);
    }

    setState(() {
      _isLoading = false; // Hide loading indicator after fetch
    });

    if (response['success']) {
      if (type == 'ASAP') {
        _selectedListingType = 'ASAP'; // Set the listing type for editing
        _currentAsapListing = response['listing'];
        AsapListing listing = _currentAsapListing!;
        print('🔧 DEBUG - Loading ASAP listing for edit, set _selectedListingType to: $_selectedListingType');
        _titleController.text = listing.title;
        _descriptionController.text = listing.description ?? '';
        _priceController.text = listing.price.toString();
        _originalPrice = listing.price; // Store original price for comparison
        _listingLatitude = listing.latitude;
        _listingLongitude = listing.longitude;
        _locationAddressController.text = listing.locationAddress ?? '';
        _preferredDoerGender = listing.preferredDoerGender;


        // Load existing images for editing
        _selectedImages.clear(); // Clear any new images
        _existingImageUrls = List<String>.from(listing.picturesUrls ?? []);
        // Filter out placeholder images
        _existingImageUrls.removeWhere((url) => url.contains('placehold.co') || url.contains('example.com'));
        print('🖼️ Loaded ${_existingImageUrls.length} existing images for editing');
        print('🖼️ Image URLs: $_existingImageUrls');

      } else { // Public
        _selectedListingType = 'Public'; // Set the listing type for editing
        _currentPublicListing = response['listing'];
        PublicListing listing = _currentPublicListing!;
        print('🔧 DEBUG - Loading PUBLIC listing for edit, set _selectedListingType to: $_selectedListingType');
        _titleController.text = listing.title;
        _descriptionController.text = listing.description ?? '';
        _priceController.text = listing.price?.toString() ?? '';
        _originalPrice = listing.price; // Store original price for comparison
        _selectedCategory = listing.category;
        _listingLatitude = listing.latitude;
        _listingLongitude = listing.longitude;
        _locationAddressController.text = listing.locationAddress ?? '';
        _preferredDoerGender = listing.preferredDoerGender;
        _tagsController.text = listing.tags ?? ''; // NEW: Populate tags field


        // Load existing images for editing
        _selectedImages.clear(); // Clear any new images
        _existingImageUrls = List<String>.from(listing.picturesUrls ?? []);
        // Filter out placeholder images
        _existingImageUrls.removeWhere((url) => url.contains('placehold.co') || url.contains('example.com'));
        print('🖼️ Loaded ${_existingImageUrls.length} existing images for editing');
        print('🖼️ Image URLs: $_existingImageUrls');
      }

      // Update map and fees after data is loaded
      if (_listingLatitude != null && _listingLongitude != null) {
        _selectedLocation = LatLng(_listingLatitude!, _listingLongitude!);
        _updateLocationMarker(_selectedLocation);
        _mapController?.animateCamera(CameraUpdate.newLatLngZoom(_selectedLocation, 15.0));
      }

    } else {
      _showSnackBar('Failed to load listing for editing: ${response['message']}', isError: true);
      if (mounted) {
        Navigator.of(context).pop(); // Go back if loading fails
      }
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _priceController.dispose();
    _descriptionController.dispose();
    _locationSearchController.dispose();
    _locationAddressController.dispose();
    _tagsController.dispose(); // Dispose tags controller
    _mapController?.dispose();
    super.dispose();
  }

  void _showSnackBar(String message, {bool isError = false}) {
    // Clear any existing snackbars first
    ScaffoldMessenger.of(context).clearSnackBars();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isError ? Icons.error : Icons.check_circle,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: isError ? Colors.red.shade600 : Colors.green.shade600,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 6,
      ),
    );
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    _mapController?.animateCamera(CameraUpdate.newLatLngZoom(_selectedLocation, 15.0));
  }

  void _onMapTap(LatLng latLng) async {
    // First check basic coordinate bounds
    if (!_isLocationInPhilippines(latLng)) {
      _showSnackBar("Please select a location within Philippines.", isError: true);
      return;
    }

    // Then validate using reverse geocoding (same as sign-up logic)
    bool isValidPhilippinesLocation = await _validateLocationByPhilippines(latLng);
    if (!isValidPhilippinesLocation) {
      _showSnackBar("Please select a location within Philippines.", isError: true);
      return;
    }

    setState(() {
      _selectedLocation = latLng;
      _updateLocationMarker(latLng);
      _listingLatitude = latLng.latitude;
      _listingLongitude = latLng.longitude;
    });
    _reverseGeocodeLocation(latLng);
    _showSnackBar('📍 Location selected successfully!');
    print('📍 Location selected in Philippines: Lat: ${latLng.latitude}, Lng: ${latLng.longitude}');
  }

  void _updateLocationMarker(LatLng latLng) {
    setState(() {
      _locationMarker = Marker(
        markerId: const MarkerId('listing_location'),
        position: latLng,
        infoWindow: const InfoWindow(title: 'Listing Location'),
      );
    });
  }

  Future<void> _geocodeAddress() async {
    final address = _locationSearchController.text.trim();
    if (address.isEmpty) {
      _showSnackBar('Please enter an address to search.', isError: true);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Use location-biased geocoding with Philippines region for better accuracy
      List<Location> locations = await geocoding.locationFromAddress(address);

      if (locations.isNotEmpty) {
        // If multiple results, show the most accurate one first
        // Sort by relevance (closer to Philippines center if available)
        final philippinesCenter = const LatLng(14.5995, 120.9842); // Manila
        locations.sort((a, b) {
          final distA = _calculateDistance(
              a.latitude, a.longitude,
              philippinesCenter.latitude, philippinesCenter.longitude
          );
          final distB = _calculateDistance(
              b.latitude, b.longitude,
              philippinesCenter.latitude, philippinesCenter.longitude
          );
          return distA.compareTo(distB);
        });

        final latLng = LatLng(locations.first.latitude, locations.first.longitude);
        setState(() {
          _selectedLocation = latLng;
          _updateLocationMarker(latLng);
          _listingLatitude = latLng.latitude;
          _listingLongitude = latLng.longitude;
        });
        _mapController?.animateCamera(CameraUpdate.newLatLngZoom(latLng, 18.0)); // Higher zoom for accuracy
        _showSnackBar('Location found! Tap the map to fine-tune if needed.');
        _reverseGeocodeLocation(latLng);
      } else {
        _showSnackBar('Address not found. Please try a more specific address or landmark.', isError: true);
      }
    } catch (e) {
      _showSnackBar('Error geocoding address: $e', isError: true);
      debugPrint('Geocoding error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Helper function to calculate distance between two points
  double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // Earth's radius in kilometers
    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);
    final double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_degreesToRadians(lat1)) * math.cos(_degreesToRadians(lat2)) *
            math.sin(dLon / 2) * math.sin(dLon / 2);
    final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (math.pi / 180);
  }

  Future<void> _getCurrentLocationAndFillFields() async {
    setState(() {
      _isGettingLocation = true;
    });

    try {
      // Get current position
      Position? position = await _locationService.getCurrentLocation();

      if (position != null) {
        // Update map location
        final LatLng currentLatLng = LatLng(position.latitude, position.longitude);

        // Validate if location is in Philippines (for ASAP listings)
        if (_selectedListingType == 'ASAP') {
          bool isValidLocation = await _validateLocationByPhilippines(currentLatLng);
          if (!isValidLocation) {
            _showSnackBar('Current location is outside Philippines. Please select a location within Philippines.', isError: true);
            return;
          }
        }

        _listingLatitude = position.latitude;
        _listingLongitude = position.longitude;

        setState(() {
          _selectedLocation = currentLatLng;
          _updateLocationMarker(currentLatLng);
        });

        // Move map camera to current location
        _mapController?.animateCamera(CameraUpdate.newLatLngZoom(currentLatLng, 15.0));

        // Fill address field using reverse geocoding
        await _reverseGeocodeLocation(currentLatLng);

        _showSnackBar('Current location detected and set successfully!', isError: false);
      }
    } catch (e) {
      _showSnackBar('Failed to get current location: $e', isError: true);
    } finally {
      setState(() {
        _isGettingLocation = false;
      });
    }
  }

  Future<void> _reverseGeocodeLocation(LatLng latLng) async {
    try {
      List<Placemark> placemarks = await geocoding.placemarkFromCoordinates(
        latLng.latitude,
        latLng.longitude,
      );

      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;

        // Create a more detailed and accurate address format
        List<String> addressComponents = [];

        // Add street name if available
        if (placemark.street != null && placemark.street!.isNotEmpty) {
          addressComponents.add(placemark.street!);
        }

        // Add sublocality (barangay/district) if available
        if (placemark.subLocality != null && placemark.subLocality!.isNotEmpty) {
          addressComponents.add(placemark.subLocality!);
        }

        // Add locality (city/municipality) if available
        if (placemark.locality != null && placemark.locality!.isNotEmpty) {
          addressComponents.add(placemark.locality!);
        }

        // Add administrative area (province) if available
        if (placemark.administrativeArea != null && placemark.administrativeArea!.isNotEmpty) {
          addressComponents.add(placemark.administrativeArea!);
        }

        // Add postal code if available
        if (placemark.postalCode != null && placemark.postalCode!.isNotEmpty) {
          addressComponents.add(placemark.postalCode!);
        }

        // Add country if not Philippines (should be Philippines for local addresses)
        if (placemark.country != null && placemark.country!.isNotEmpty && placemark.country != 'Philippines') {
          addressComponents.add(placemark.country!);
        }

        // Join all components with commas
        String address = addressComponents.join(', ');

        // If we have a very detailed address, use it; otherwise, fall back to the original search
        if (addressComponents.length >= 3) {
          _locationAddressController.text = address;
        } else {
          // Fall back to the original search term if reverse geocoding is too generic
          _locationAddressController.text = _locationSearchController.text.trim();
        }

        debugPrint('Reverse geocoded address: $address');
      } else {
        _locationAddressController.text = _locationSearchController.text.trim();
      }
    } catch (e) {
      debugPrint('Reverse geocoding error: $e');
      _locationAddressController.text = _locationSearchController.text.trim();
    }
  }

  Future<void> _pickImage() async {
    // Check total image count (existing + new)
    int totalImages = _existingImageUrls.length + _selectedImages.length;
    if (totalImages >= 5) {
      _showSnackBar('Maximum 5 images allowed.', isError: true);
      return;
    }

    final XFile? pickedFile = await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _selectedImages.add(File(pickedFile.path));
      });
    }
  }

  void _removeNewImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  void _removeExistingImage(int index) {
    setState(() {
      _existingImageUrls.removeAt(index);
    });
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }





  // --- Warning Dialogs ---
  Future<void> _showAsapWarningDialog() async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
          title: const Text(
            'WARNING!',
            style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold, fontSize: 22),
            textAlign: TextAlign.center,
          ),
          content: const SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text(
                  'ASAP listings are for onsite work only. Any task that can be done online is prohibited.',
                  textAlign: TextAlign.center, style: TextStyle(fontSize: 15, color: Colors.black87),
                ),
                SizedBox(height: 10),
                Text(
                  'The task you are posting should also be something urgent or something you need immediate help with.',
                  textAlign: TextAlign.center, style: TextStyle(fontSize: 15, color: Colors.black87),
                ),
                SizedBox(height: 15),
                Text(
                  'If it\'s not urgent, please post it under the Public Listing instead.',
                  textAlign: TextAlign.center, style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15, color: Colors.black87),
                ),
                SizedBox(height: 15),
                Text(
                  'Doers may report you if they see you posting something inappropriate for ASAP. This may cause you to be recommended less — and worse, you could get banned from the platform.',
                  textAlign: TextAlign.center, style: TextStyle(fontSize: 15, color: Colors.black87),
                ),
              ],
            ),
          ),
          actions: <Widget>[
            Center(
              child: CustomButton(
                text: 'Understood',
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                },
                color: Constants.primaryColor,
                textColor: Colors.white,
                borderRadius: 10.0, height: 45.0, width: 150,
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _showPublicListingWarningDialog() async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
          title: const Text(
            'WARNING!',
            style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold, fontSize: 22),
            textAlign: TextAlign.center,
          ),
          content: const SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text(
                  'Requests like... "Please help me sell", "Sell my...", "Commission for selling." or anything that asks other Doers to sell your items are not allowed on this platform.',
                  textAlign: TextAlign.center, style: TextStyle(fontSize: 15, color: Colors.black87),
                ),
                SizedBox(height: 15),
                Text(
                  'Doing so may trigger the AI and the system, causing you to be recommended less and worse, you could get banned from the platform.',
                  textAlign: TextAlign.center, style: TextStyle(fontSize: 15, color: Colors.black87),
                ),
              ],
            ),
          ),
          actions: <Widget>[
            Center(
              child: CustomButton(
                text: 'Understood',
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                },
                color: Constants.primaryColor,
                textColor: Colors.white,
                borderRadius: 10.0, height: 45.0, width: 150,
              ),
            ),
          ],
        );
      },
    );
  }
  // --- End Warning Dialogs ---

  // NEW: _submitListing now handles both create and update based on _isEditing
  Future<void> _submitListing() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Validation specific to location based on category for Public listings
    if (_selectedListingType == 'Public' && (_selectedCategory == 'Onsite' || _selectedCategory == 'Hybrid')) {
      if (_listingLatitude == null || _listingLongitude == null || _locationAddressController.text.isEmpty) {
        _showSnackBar('Location is required for Onsite/Hybrid Public listings. Please select on map or search.', isError: true);
        return;
      }
    }

    // Price validation for both ASAP and Public listings
    final price = double.tryParse(_priceController.text);
    if (_priceController.text.isEmpty || price == null || price <= 0) {
      _showSnackBar('❌ Price is required. Please enter a price.', isError: true);
      return;
    }
    if (price < 20) {
      _showSnackBar('❌ Price must be at least Php 20. Please check the Price field.', isError: true);
      return;
    }




    setState(() {
      _isLoading = true;
    });

    // Check for banned words in title and description
    print('CombinedListingForm: Starting word filter check...');
    print('CombinedListingForm: Title: "${_titleController.text.trim()}"');
    print('CombinedListingForm: Description: "${_descriptionController.text.trim()}"');

    try {
      final wordFilterService = WordFilterService();
      final fieldsToCheck = {
        'title': _titleController.text.trim(),
        'description': _descriptionController.text.trim(),
      };

      // Add tags to check if it's a Public listing
      if (_selectedListingType == 'Public' && _tagsController.text.trim().isNotEmpty) {
        fieldsToCheck['tags'] = _tagsController.text.trim();
      }

      final bannedWordsByField = await wordFilterService.checkMultipleFields(fieldsToCheck);

      print('CombinedListingForm: Banned words result: $bannedWordsByField');

      if (bannedWordsByField.isNotEmpty) {
        setState(() {
          _isLoading = false;
        });

        print('CombinedListingForm: Showing banned words dialog');
        // Show popup dialog with banned words
        await BannedWordsDialog.show(context, bannedWordsByField);
        return;
      } else {
        print('CombinedListingForm: No banned words found, proceeding with creation');
      }
    } catch (e) {
      print('CombinedListingForm: Error checking banned words: $e');
      // Continue with creation if word filter fails
    }

    // Handle images: combine existing and new images
    List<String> imageUrls = [];

    // Start with existing images (for edit mode)
    imageUrls.addAll(_existingImageUrls);
    print('🖼️ Starting with ${_existingImageUrls.length} existing images: $_existingImageUrls');

    // Upload new images if any are selected
    if (_selectedImages.isNotEmpty) {
      print('🖼️ Uploading ${_selectedImages.length} new images...');
      final uploadResult = await ImageUploadService().uploadListingImages(_selectedImages);

      if (uploadResult['success'] == true) {
        List<String> newImageUrls = List<String>.from(uploadResult['image_urls'] ?? []);
        imageUrls.addAll(newImageUrls);
        print('✅ New images uploaded successfully: $newImageUrls');
        print('✅ Total images after adding new ones: ${imageUrls.length} - $imageUrls');
      } else {
        print('❌ Image upload failed: ${uploadResult['message']}');
        _showSnackBar('Failed to upload images: ${uploadResult['message']}', isError: true);
        setState(() {
          _isLoading = false;
        });
        return;
      }
    } else {
      print('🖼️ No new images to upload');
    }

    // No placeholder images - let the detail screens handle empty image lists

    print('🖼️ Final image URLs: $imageUrls');

    dynamic response;
    print('🔍 DEBUG - Selected listing type: $_selectedListingType');
    print('🔍 DEBUG - Is editing: $_isEditing');
    print('🔍 DEBUG - Widget listing ID: ${widget.listingId}');

    if (_selectedListingType == 'ASAP') {
      print('✅ Processing ASAP listing update/create');
      if (_isEditing && widget.listingId != null) {
        // Construct AsapListing object for update
        print('🔄 Creating ASAP listing update with ${imageUrls.length} images: $imageUrls');
        print('🔄 Existing images count: ${_existingImageUrls.length}');
        print('🔄 New images count: ${_selectedImages.length}');

        AsapListing updatedListing = AsapListing(
          id: widget.listingId!,
          listerId: 0, // listerId is not updated via this form, backend handles it
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim(),
          price: double.parse(_priceController.text),
          latitude: _listingLatitude!,
          longitude: _listingLongitude!,
          locationAddress: _locationAddressController.text.trim(),
          preferredDoerGender: _preferredDoerGender ?? 'Any',
          picturesUrls: imageUrls,

          status: 'pending', // Status is typically managed by backend workflow
          isActive: true, // is_active is managed by backend or separate toggle
          createdAt: DateTime.now(), // Dummy, not used for update
          updatedAt: DateTime.now(), listingType: 'ASAP', // Dummy, not used for update
        );
        print('🔄 ASAP listing object created with pictures_urls: ${updatedListing.picturesUrls}');
        print('🔄 Calling ASAP update service...');
        response = await AsapListingService().updateAsapListing(updatedListing);
      } else {
        // Create new ASAP listing
        response = await AsapListingService().createAsapListing(
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim(),
          price: double.parse(_priceController.text),
          latitude: _listingLatitude!,
          longitude: _listingLongitude!,
          locationAddress: _locationAddressController.text.trim(),
          preferredDoerGender: _preferredDoerGender ?? 'Any',
          picturesUrls: imageUrls,
        );
      }
    } else if (_selectedListingType == 'Public') {
      print('✅ Processing PUBLIC listing update/create');
      if (_isEditing && widget.listingId != null && _currentPublicListing != null) {
        print('🔄 Updating existing PUBLIC listing');
        // Create updated PublicListing object
        PublicListing updatedListing = PublicListing(
          id: _currentPublicListing!.id,
          listerId: _currentPublicListing!.listerId,
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim(),
          price: double.tryParse(_priceController.text),
          category: _selectedCategory!,
          latitude: (_selectedCategory == 'Onsite' || _selectedCategory == 'Hybrid') ? _listingLatitude : null,
          longitude: (_selectedCategory == 'Onsite' || _selectedCategory == 'Hybrid') ? _listingLongitude : null,
          locationAddress: (_selectedCategory == 'Onsite' || _selectedCategory == 'Hybrid') ? _locationAddressController.text.trim() : null,
          preferredDoerGender: _preferredDoerGender,
          picturesUrls: imageUrls, // Use combined existing and new images
          tags: _tagsController.text.trim().isEmpty ? null : _tagsController.text.trim(),
          doerFee: _currentPublicListing!.doerFee,
          transactionFee: _currentPublicListing!.transactionFee,
          totalAmount: _currentPublicListing!.totalAmount,

          status: _currentPublicListing!.status,
          isActive: _currentPublicListing!.isActive,
          createdAt: _currentPublicListing!.createdAt,
          updatedAt: DateTime.now(),
          listingType: 'Public',
        );

        // Debug: Print the updated listing object before sending
        print('🔧 DEBUG - About to update listing:');
        print('🆔 Listing ID: ${updatedListing.id}');
        print('📝 Title: ${updatedListing.title}');
        print('💰 Price: ${updatedListing.price}');
        print('📍 Category: ${updatedListing.category}');
        print('🌍 Latitude: ${updatedListing.latitude}');
        print('🌍 Longitude: ${updatedListing.longitude}');
        print('📍 Location: ${updatedListing.locationAddress}');
        print('👤 Gender Pref: ${updatedListing.preferredDoerGender}');
        print('🏷️ Tags: ${updatedListing.tags}');
        print('💳 Payment Method: ${updatedListing.paymentMethod}');
        print('✅ Is Active: ${updatedListing.isActive}');

        // Update existing Public listing
        print('🚀 Calling updateListing...');
        response = await PublicListingService.ListingService().updateListing(
            updatedListing
        );
        print('📨 Update response received: $response');
      } else {
        // Create new Public listing
        response = await PublicListingService.ListingService().createListing(
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim(),
          price: double.tryParse(_priceController.text),
          category: _selectedCategory!,
          latitude: (_selectedCategory == 'Onsite' || _selectedCategory == 'Hybrid') ? _listingLatitude : null,
          longitude: (_selectedCategory == 'Onsite' || _selectedCategory == 'Hybrid') ? _listingLongitude : null,
          locationAddress: (_selectedCategory == 'Onsite' || _selectedCategory == 'Hybrid') ? _locationAddressController.text.trim() : null,
          preferredDoerGender: _preferredDoerGender ?? 'Any',
          picturesUrls: imageUrls, // Use uploaded images

          isActive: true,
          tags: _tagsController.text.trim().isEmpty ? null : _tagsController.text.trim(),
        );
      }
    }

    setState(() {
      _isLoading = false;
    });

    // Debug: Print final response analysis
    print('🔍 DEBUG - Final response analysis:');
    print('📦 Response is null: ${response == null}');
    print('📦 Response: $response');
    if (response != null) {
      print('✅ Success field: ${response['success']}');
      print('💬 Message field: ${response['message']}');
      print('🆔 Listing ID field: ${response['listing_id']}');
    }

    if (response != null && response['success']) {
      print('✅ SUCCESS - Showing success message and navigating');

      // Clean success message without extra details
      String message = response['message'] ?? 'Operation completed successfully!';
      List<dynamic> changes = response['changes_detected'] ?? [];
      int pictureCount = response['pictures_count'] ?? 0;

      // Log details for debugging but don't show in UI
      if (changes.isNotEmpty) {
        print('✅ Changes detected: $changes');
        print('✅ Pictures count: $pictureCount');
      }

      _showSnackBar(message);
      if (mounted) {
        // After successful creation/update, navigate back or to details screen
        if (_selectedListingType == 'ASAP') {
          Navigator.of(context).pushReplacementNamed(
            '/asap_listing_details',
            arguments: {'listing_id': response['listing_id'] ?? widget.listingId}, // Use new ID or existing
          );
        } else { // Public Listing
          Navigator.of(context).pushReplacementNamed(
            '/public_listing_details',
            arguments: {'listing_id': response['listing_id'] ?? widget.listingId}, // Use new ID or existing
          );
        }
      }
    } else {
      print('❌ FAILURE - Showing error message');
      print('❌ Error details: ${response?['message'] ?? 'Unknown error'}');
      _showSnackBar('Failed to ${_isEditing ? 'update' : 'create'} listing: ${response?['message'] ?? 'Unknown error'}', isError: true);
    }
  }

  // Helper to reset location-related fields
  void _resetLocationFields() {
    _listingLatitude = null;
    _listingLongitude = null;
    _locationAddressController.clear();
    _locationSearchController.clear();
    _locationMarker = null;
    _mapController?.animateCamera(CameraUpdate.newLatLngZoom(const LatLng(14.5995, 120.9842), 10.0)); // Reset map view
  }

  @override
  Widget build(BuildContext context) {
    bool isAsap = _selectedListingType == 'ASAP';
    // Show location fields if it's ASAP, or if it's Public and category is Onsite/Hybrid
    bool showLocationFields = isAsap || (_selectedCategory == 'Onsite' || _selectedCategory == 'Hybrid');
    // Show payment section: always for new listings, only when price changed for edit mode
    bool showPaymentSection = !_isEditing || _priceChanged;

    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Listing' : 'Enter Listing Details'), // Title changes based on mode
        backgroundColor: Constants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: _isLoading && _isEditing && _titleController.text.isEmpty // Show loading indicator while fetching data for edit
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Listing Type Selector (ASAP vs Public) - Disabled in Edit Mode
              const Text(
                'Listing Type',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Constants.textColor),
              ),
              const SizedBox(height: 8),
              Container(
                decoration: BoxDecoration(
                  color: _isEditing ? Colors.grey.shade200 : Colors.white, // Grey out in edit mode
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: DropdownButtonFormField<String>(
                  value: _selectedListingType,
                  decoration: const InputDecoration(
                    contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    border: InputBorder.none, // Remove default border
                    prefixIcon: Icon(Icons.list_alt),
                  ),
                  items: <String>['ASAP', 'Public']
                      .map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                  onChanged: _isEditing ? null : (String? newValue) { // Disable type change in edit mode
                    setState(() {
                      _selectedListingType = newValue;
                      if (newValue == 'ASAP') {
                        _selectedCategory = null; // Clear category for ASAP
                        _tagsController.clear(); // Clear tags for ASAP
                        _showAsapWarningDialog();
                      } else { // Switched to Public
                        _showPublicListingWarningDialog();
                      }
                      _resetLocationFields(); // Reset map and address on type change
                    });
                  },
                ),
              ),
              const SizedBox(height: 24),

              // Category Selection (only for Public listings)
              if (!isAsap) ...[
                const Text(
                  'Category',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Constants.textColor),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  hint: const Text('Select Category'),
                  decoration: InputDecoration(
                    labelText: 'Category',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                    prefixIcon: const Icon(Icons.category),
                  ),
                  items: <String>['Onsite', 'Hybrid', 'Remote']
                      .map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    setState(() {
                      _selectedCategory = newValue;
                      if (newValue == 'Remote') {
                        _resetLocationFields();
                      } else {
                        // If switching back to Onsite/Hybrid, ensure map is reset to default or current location
                        _updateLocationMarker(_selectedLocation);
                        _reverseGeocodeLocation(_selectedLocation);
                      }
                    });
                  },
                  validator: (value) {
                    if (!isAsap && (value == null || value.isEmpty)) {
                      return 'Please select a category';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
              ],

              // Title
              TextFormField(
                controller: _titleController,
                decoration: InputDecoration(
                  labelText: 'Title',
                  hintText: 'Type title here',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a title';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Price
              TextFormField(
                controller: _priceController,
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                inputFormatters: _pesoInputFormatters,
                decoration: InputDecoration(
                  labelText: 'Price *', // Always required now
                  hintText: 'e.g., 500.00',
                  helperText: 'Minimum: Php 500.00',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  prefixText: 'Php ',
                  prefixStyle: const TextStyle(
                    color: Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                validator: (value) {
                  // Price is now required for both ASAP and Public listings
                  if (value == null || value.isEmpty) {
                    return isAsap ? 'Please enter a price for ASAP listing' : 'Please enter a price for Public listing';
                  }

                  // Check if it matches our peso format
                  if (!_pesoRegex.hasMatch(value)) {
                    return 'Invalid format. Use numbers and decimal point only (e.g., 20.00)';
                  }

                  final price = double.tryParse(value);
                  if (price == null) {
                    return 'Please enter a valid number';
                  }

                  if (price <= 0) {
                    return 'Price must be greater than zero';
                  }

                  if (price < 20) {
                    return 'Minimum price is Php 20.00';
                  }

                  if (price > 999999.99) {
                    return 'Maximum price is Php 999,999.99';
                  }

                  return null;
                },
                onChanged: (_) {
                  setState(() {}); // Trigger rebuild to check price change
                },
              ),
              const SizedBox(height: 16),

              // Description
              TextFormField(
                controller: _descriptionController,
                maxLines: 3,
                decoration: InputDecoration(
                  labelText: 'Description',
                  hintText: 'Type your details here',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                ),
              ),
              const SizedBox(height: 24),

              // Tags (only for Public listings)
              if (!isAsap) ...[
                TextFormField(
                  controller: _tagsController,
                  decoration: InputDecoration(
                    labelText: 'Tags (Optional)',
                    hintText: 'ex. palaba, pahugas, pabuhat, etc.', // Matches image
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                    prefixIcon: const Icon(Icons.tag),
                  ),
                ),
                const SizedBox(height: 24),
              ],

              // Location Section (Conditional based on category for Public, always for ASAP)
              if (showLocationFields) ...[
                const Text(
                  'Location',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Constants.textColor),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _locationSearchController,
                  decoration: InputDecoration(
                    labelText: 'Search Location',
                    hintText: 'Enter address or landmark',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _isLoading
                        ? const Padding(
                      padding: EdgeInsets.all(8.0),
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                        : IconButton(
                      icon: const Icon(Icons.send),
                      onPressed: _geocodeAddress,
                    ),
                  ),
                  onFieldSubmitted: (value) => _geocodeAddress(),
                ),
                const SizedBox(height: 16),

                // Use Current Location Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _isGettingLocation ? null : _getCurrentLocationAndFillFields,
                    icon: _isGettingLocation
                        ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                    )
                        : const Icon(Icons.my_location),
                    label: Text(_isGettingLocation ? 'Getting Location...' : 'Use My Current Location'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Constants.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  height: 200,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey.shade300, width: 1),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: GoogleMap(
                      onMapCreated: _onMapCreated,
                      initialCameraPosition: CameraPosition(
                        target: _selectedLocation,
                        zoom: 15.0,
                      ),
                      markers: _locationMarker != null ? {_locationMarker!} : {},
                      onTap: _onMapTap,
                      myLocationButtonEnabled: true,
                      myLocationEnabled: true,
                      zoomControlsEnabled: true,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _locationAddressController,
                  readOnly: true,
                  decoration: InputDecoration(
                    labelText: 'Selected Location *',
                    hintText: _locationAddressController.text.isEmpty
                        ? 'Tap on the map or search to select location'
                        : null,
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                    prefixIcon: const Icon(Icons.location_on),
                    suffixIcon: _locationAddressController.text.isNotEmpty
                        ? IconButton(
                      icon: const Icon(Icons.clear, color: Colors.grey),
                      onPressed: () {
                        setState(() {
                          _locationAddressController.clear();
                          _listingLatitude = null;
                          _listingLongitude = null;
                          _locationMarker = null;
                        });
                      },
                    )
                        : null,
                  ),
                  validator: (value) {
                    // Only validate if location is required for the selected category
                    if (_selectedListingType == 'ASAP' ||
                        (_selectedListingType == 'Public' &&
                            (_selectedCategory == 'Onsite' || _selectedCategory == 'Hybrid'))) {
                      if (value == null || value.isEmpty) {
                        return 'Please select a location on the map';
                      }
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),
              ],

              // Preferred Doer
              DropdownButtonFormField<String>(
                value: _preferredDoerGender,
                hint: const Text('Preferred Doer (Optional)'),
                decoration: InputDecoration(
                  labelText: 'Preferred Doer (Optional)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  prefixIcon: const Icon(Icons.person_search),
                ),
                items: <String>['Any', 'Male', 'Female']
                    .map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    _preferredDoerGender = newValue;
                  });
                },
              ),
              const SizedBox(height: 24),

              // Pictures
              const Text(
                'Pictures',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Constants.textColor),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 10,
                runSpacing: 10,
                children: [
                  // Display existing images from database
                  ..._existingImageUrls.asMap().entries.map((entry) {
                    int idx = entry.key;
                    String imageUrl = entry.value;
                    return Stack(
                      children: [
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              imageUrl.startsWith('http') 
                                  ? imageUrl 
                                  : imageUrl.startsWith('api/') 
                                      ? '${ApiConfig.baseUrl.replaceAll('/api', '')}/$imageUrl'
                                      : '${ApiConfig.baseUrl}/$imageUrl',
                              width: 80,
                              height: 80,
                              fit: BoxFit.contain, // Show full image without cropping
                              errorBuilder: (context, error, stackTrace) {
                                print('❌ Failed to load image: ${ApiConfig.baseUrl}/$imageUrl');
                                print('❌ Error: $error');
                                return Container(
                                  width: 80,
                                  height: 80,
                                  color: Colors.grey.shade200,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(Icons.broken_image, color: Colors.grey.shade400, size: 20),
                                      Text('Failed', style: TextStyle(fontSize: 8, color: Colors.grey.shade600)),
                                    ],
                                  ),
                                );
                              },
                              loadingBuilder: (context, child, loadingProgress) {
                                if (loadingProgress == null) {
                                  print('✅ Image loaded successfully: ${ApiConfig.baseUrl}/$imageUrl');
                                  return child;
                                }
                                return Container(
                                  width: 80,
                                  height: 80,
                                  color: Colors.grey.shade100,
                                  child: Center(
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      value: loadingProgress.expectedTotalBytes != null
                                          ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                                          : null,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                        Positioned(
                          right: 0,
                          top: 0,
                          child: GestureDetector(
                            onTap: () => _removeExistingImage(idx),
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: const Icon(
                                Icons.close,
                                color: Colors.white,
                                size: 18,
                              ),
                            ),
                          ),
                        ),
                        // Badge to indicate existing image
                        Positioned(
                          left: 0,
                          bottom: 0,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.blue,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: const Text(
                              'Saved',
                              style: TextStyle(color: Colors.white, fontSize: 10),
                            ),
                          ),
                        ),
                      ],
                    );
                  }).toList(),
                  // Display new images selected from gallery
                  ..._selectedImages.asMap().entries.map((entry) {
                    int idx = entry.key;
                    File image = entry.value;
                    return Stack(
                      children: [
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.file(
                              image,
                              width: 80,
                              height: 80,
                              fit: BoxFit.contain, // Show full image without cropping
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  width: 80,
                                  height: 80,
                                  color: Colors.grey.shade200,
                                  child: Icon(Icons.broken_image, color: Colors.grey.shade400),
                                );
                              },
                            ),
                          ),
                        ),
                        Positioned(
                          right: 0,
                          top: 0,
                          child: GestureDetector(
                            onTap: () => _removeNewImage(idx),
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: const Icon(
                                Icons.close,
                                color: Colors.white,
                                size: 18,
                              ),
                            ),
                          ),
                        ),
                        // Badge to indicate new image
                        Positioned(
                          left: 0,
                          bottom: 0,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.green,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: const Text(
                              'New',
                              style: TextStyle(color: Colors.white, fontSize: 10),
                            ),
                          ),
                        ),
                      ],
                    );
                  }).toList(),
                  // Add image button (only show if less than 5 total images)
                  if ((_existingImageUrls.length + _selectedImages.length) < 5)
                    GestureDetector(
                      onTap: _pickImage,
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey.shade400),
                        ),
                        child: Icon(Icons.add_a_photo, color: Colors.grey.shade600),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 24),

              const SizedBox(height: 32),

              // Submit Button
              _isLoading && !_isEditing // Show loading only for initial fetch in edit mode, or for submission
                  ? const Center(child: CircularProgressIndicator())
                  : CustomButton(
                text: _isEditing ? 'Update Listing' : 'Save', // Button text changes based on mode
                onPressed: _submitListing,
                color: Constants.primaryColor,
                textColor: Colors.white,
                borderRadius: 25.0,
                height: 50.0,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeeRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 18 : 16,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? Constants.textColor : Colors.grey.shade800,
            ),
          ),
          Text(
            'Php ${amount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: isTotal ? 18 : 16,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? Constants.primaryColor : Colors.grey.shade800,
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildDivider() {
    return Divider(
      height: 1,
      thickness: 1,
      color: Colors.grey.shade200,
      indent: 20,
      endIndent: 20,
    );
  }





  // Show "Coming Soon" snackbar
  void _showComingSoonSnackbar(String paymentMethod) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$paymentMethod - Coming Soon'),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  // Payment option with coming soon functionality
  Widget _buildPaymentOptionWithComingSoon(String title, String value, {String? feeText}) {
    return InkWell(
      onTap: () {
        _showComingSoonSnackbar(title);
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.grey.shade400,
                  width: 2,
                ),
                color: Colors.transparent,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.normal,
                  color: Colors.grey.shade600,
                ),
              ),
            ),
            if (feeText != null)
              Text(
                feeText,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade500,
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Card option with coming soon functionality
  Widget _buildCardOptionWithComingSoon() {
    return InkWell(
      onTap: () {
        _showComingSoonSnackbar('Credit/Debit Card');
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.grey.shade400,
                  width: 2,
                ),
                color: Colors.transparent,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                'Credit/Debit Card',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.normal,
                  color: Colors.grey.shade600,
                ),
              ),
            ),
            Text(
              'Service fee: 3.2% + ₱30',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Bank transfer dropdown with coming soon functionality
  Widget _buildBankTransferDropdownWithComingSoon() {
    return InkWell(
      onTap: () {
        _showComingSoonSnackbar('Bank Transfer');
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.grey.shade400,
                  width: 2,
                ),
                color: Colors.transparent,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                'Bank Transfer',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.normal,
                  color: Colors.grey.shade600,
                ),
              ),
            ),
            Text(
              'Service fee: 1% + ₱20',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}