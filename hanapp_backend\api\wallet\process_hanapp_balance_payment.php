<?php
// hanapp_backend/api/wallet/process_hanapp_balance_payment.php
// Production-level HanApp Balance payment processing for job payments
// Deducts from lister's balance and updates doer's total_profit for withdrawal

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

require_once '../../config/db_connect.php';

// Ensure clean output buffer
ob_clean();

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(["success" => false, "message" => "Method not allowed"]);
    exit();
}

if (!isset($conn) || $conn->connect_error) {
    error_log("process_hanapp_balance_payment.php: Database connection failed: " . ($conn->connect_error ?? 'Unknown error'));
    http_response_code(500);
    echo json_encode(["success" => false, "message" => "Database connection failed"]);
    exit();
}

try {
    // Log the start of the request
    error_log("PRODUCTION: HanApp Balance payment request started at " . date('Y-m-d H:i:s'));

    // Get JSON input
    $rawInput = file_get_contents('php://input');

    if (empty($rawInput)) {
        throw new Exception("No input data received");
    }

    $input = json_decode($rawInput, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("Invalid JSON input: " . json_last_error_msg());
    }

    if (!$input) {
        throw new Exception("Empty input data");
    }

    error_log("PRODUCTION: HanApp Balance payment processing for application: " . ($input['application_id'] ?? 'unknown'));

    // Extract and validate required fields
    $listerId = $input['lister_id'] ?? null;
    $applicationId = $input['application_id'] ?? null;
    $doerFee = $input['doer_fee'] ?? null;
    $transactionFee = $input['transaction_fee'] ?? 25.0; // Default ₱25 platform fee
    $totalAmount = $input['total_amount'] ?? null;

    // Strict validation for production
    if (!$listerId || !is_numeric($listerId) || $listerId <= 0) {
        throw new Exception("Valid lister ID is required");
    }

    if (!$applicationId || !is_numeric($applicationId) || $applicationId <= 0) {
        throw new Exception("Valid application ID is required");
    }

    if (!$doerFee || !is_numeric($doerFee) || $doerFee <= 0) {
        throw new Exception("Valid doer fee is required");
    }

    if (!$totalAmount || !is_numeric($totalAmount) || $totalAmount <= 0) {
        throw new Exception("Valid total amount is required");
    }

    // Convert to proper types
    $listerId = intval($listerId);
    $applicationId = intval($applicationId);
    $doerFee = floatval($doerFee);
    $transactionFee = floatval($transactionFee);
    $totalAmount = floatval($totalAmount);

    // Validate calculation
    $expectedTotal = $doerFee + $transactionFee;
    if (abs($totalAmount - $expectedTotal) > 0.01) {
        throw new Exception("Amount mismatch: Expected ₱" . number_format($expectedTotal, 2) . ", got ₱" . number_format($totalAmount, 2));
    }

    error_log("PRODUCTION: Processing payment - Lister: $listerId, Application: $applicationId, Doer Fee: ₱$doerFee, Platform Fee: ₱$transactionFee, Total: ₱$totalAmount");

    // Start atomic transaction
    $conn->autocommit(false);

    try {
        // 1. Get and validate lister's current balance
        $stmt = $conn->prepare("SELECT id, balance, role, full_name FROM users WHERE id = ? FOR UPDATE");
        if ($stmt === false) {
            throw new Exception("Failed to prepare lister query: " . $conn->error);
        }

        $stmt->bind_param("i", $listerId);
        if (!$stmt->execute()) {
            throw new Exception("Failed to execute lister query: " . $stmt->error);
        }

        $result = $stmt->get_result();
        if ($result->num_rows === 0) {
            $stmt->close();
            throw new Exception("Lister not found with ID: $listerId");
        }

        $lister = $result->fetch_assoc();
        $stmt->close();

        $currentBalance = floatval($lister['balance']);
        $listerName = $lister['full_name'];

        error_log("PRODUCTION: Lister $listerName (ID: $listerId) current balance: ₱" . number_format($currentBalance, 2));

        // Check sufficient balance
        if ($currentBalance < $totalAmount) {
            throw new Exception("Insufficient HanApp Balance. Required: ₱" . number_format($totalAmount, 2) . ", Available: ₱" . number_format($currentBalance, 2));
        }

        // 2. Get and validate application details with doer info
        $stmt = $conn->prepare("
            SELECT a.id, a.doer_id, a.lister_id, a.listing_id, a.listing_type, a.status,
                   COALESCE(pl.title, al.title) as listing_title,
                   COALESCE(pl.price, al.price) as listing_price,
                   u.full_name as doer_name, u.email as doer_email, u.total_profit as current_doer_profit
            FROM applicationsv2 a
            LEFT JOIN listingsv2 pl ON a.listing_id = pl.id AND a.listing_type = 'PUBLIC'
            LEFT JOIN asap_listings al ON a.listing_id = al.id AND a.listing_type = 'ASAP'
            JOIN users u ON a.doer_id = u.id
            WHERE a.id = ? AND a.lister_id = ? AND a.status = 'in_progress'
            FOR UPDATE
        ");

        if ($stmt === false) {
            throw new Exception("Failed to prepare application query: " . $conn->error);
        }

        $stmt->bind_param("ii", $applicationId, $listerId);
        if (!$stmt->execute()) {
            throw new Exception("Failed to execute application query: " . $stmt->error);
        }

        $result = $stmt->get_result();
        if ($result->num_rows === 0) {
            $stmt->close();
            throw new Exception("Application not found, not owned by lister, or not in progress status");
        }

        $application = $result->fetch_assoc();
        $stmt->close();

        $doerId = intval($application['doer_id']);
        $listingTitle = $application['listing_title'];
        $doerName = $application['doer_name'];
        $currentDoerProfit = floatval($application['current_doer_profit']);

        error_log("PRODUCTION: Application found - Doer: $doerName (ID: $doerId), Job: '$listingTitle', Current doer profit: ₱" . number_format($currentDoerProfit, 2));

        // 3. Deduct total amount from lister's HanApp Balance
        $newListerBalance = $currentBalance - $totalAmount;
        $stmt = $conn->prepare("
            UPDATE users
            SET balance = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ? AND balance >= ?
        ");

        if ($stmt === false) {
            throw new Exception("Failed to prepare lister balance update: " . $conn->error);
        }

        $stmt->bind_param("did", $newListerBalance, $listerId, $totalAmount);

        if (!$stmt->execute()) {
            throw new Exception("Failed to deduct from lister balance: " . $stmt->error);
        }

        if ($stmt->affected_rows === 0) {
            throw new Exception("Failed to deduct balance - insufficient funds or concurrent modification");
        }
        $stmt->close();

        error_log("PRODUCTION: Deducted ₱" . number_format($totalAmount, 2) . " from lister balance. New balance: ₱" . number_format($newListerBalance, 2));

        // 4. Add doer fee to doer's total_profit (for withdrawal)
        $newDoerProfit = $currentDoerProfit + $doerFee;
        $stmt = $conn->prepare("
            UPDATE users
            SET total_profit = total_profit + ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ? AND role = 'doer'
        ");

        if ($stmt === false) {
            throw new Exception("Failed to prepare doer profit update: " . $conn->error);
        }

        $stmt->bind_param("di", $doerFee, $doerId);

        if (!$stmt->execute()) {
            throw new Exception("Failed to update doer profit: " . $stmt->error);
        }

        if ($stmt->affected_rows === 0) {
            throw new Exception("Failed to update doer profit - doer not found or invalid role");
        }
        $stmt->close();

        error_log("PRODUCTION: Added ₱" . number_format($doerFee, 2) . " to doer profit. New total profit: ₱" . number_format($newDoerProfit, 2));

        // 5. Record platform revenue (transaction fee)
        $stmt = $conn->prepare("
            INSERT INTO platform_revenue (
                transaction_type, reference_id, amount, source,
                description, created_at
            ) VALUES (
                'hanapp_balance_job_payment_fee', ?, ?, 'hanapp_balance_payment',
                ?, CURRENT_TIMESTAMP
            )
        ");

        if ($stmt === false) {
            error_log("Warning: Failed to prepare platform revenue statement: " . $conn->error);
        } else {
            $description = "Platform fee from HanApp Balance job payment - Application ID: $applicationId, Job: '$listingTitle'";
            $stmt->bind_param("ids", $applicationId, $transactionFee, $description);

            if (!$stmt->execute()) {
                error_log("Warning: Failed to record platform revenue: " . $stmt->error);
            } else {
                error_log("PRODUCTION: Recorded platform revenue: ₱" . number_format($transactionFee, 2));
            }
            $stmt->close();
        }

        // 6. Record the HanApp Balance payment transaction - Optional
        $transactionId = 0;
        try {
            $stmt = $conn->prepare("
                INSERT INTO job_payment_transactions (
                    user_id, application_id, doer_id, doer_fee, transaction_fee, total_amount,
                    status, payment_method, description, paid_at, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, 'completed', 'hanapp_balance', ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ");

            if ($stmt !== false) {
                $description = "TAPP Balance Payment: {$listingTitle}";
                $paidAt = date('Y-m-d H:i:s');

                $stmt->bind_param(
                    "iiidddss",
                    $listerId,
                    $applicationId,
                    $doerId,
                    $doerFee,
                    $transactionFee,
                    $totalAmount,
                    $description,
                    $paidAt
                );

                if ($stmt->execute()) {
                    $transactionId = $conn->insert_id;
                } else {
                    error_log("Failed to record job payment transaction: " . $stmt->error);
                    $transactionId = time(); // Use timestamp as fallback ID
                }
                $stmt->close();
            }
        } catch (Exception $e) {
            // Continue without transaction recording if table doesn't exist
            error_log("Optional job payment transaction recording failed (table may not exist): " . $e->getMessage());
            $transactionId = time(); // Use timestamp as fallback ID
        }

        // 7. Update application status to completed with payment confirmation
        $stmt = $conn->prepare("
            UPDATE applicationsv2
            SET status = 'completed',
                payment_confirmed = 1,
                payment_confirmed_at = CURRENT_TIMESTAMP,
                project_end_date = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ? AND lister_id = ? AND status = 'in_progress'
        ");

        if ($stmt === false) {
            throw new Exception("Failed to prepare application status update: " . $conn->error);
        }

        $stmt->bind_param("ii", $applicationId, $listerId);

        if (!$stmt->execute()) {
            throw new Exception("Failed to update application status: " . $stmt->error);
        }

        if ($stmt->affected_rows === 0) {
            throw new Exception("Failed to update application - not found or status changed");
        }
        $stmt->close();

        error_log("PRODUCTION: Application $applicationId marked as completed with payment confirmation");

        // 8. Create notification for doer (payment received)
        $stmt = $conn->prepare("
            INSERT INTO doer_notifications (
                user_id, type, title, content, associated_id, is_read, created_at
            ) VALUES (?, 'payment_received', 'Payment Received', ?, ?, 0, CURRENT_TIMESTAMP)
        ");

        if ($stmt === false) {
            error_log("Warning: Failed to prepare doer notification: " . $conn->error);
        } else {
            $notificationContent = "You have received ₱" . number_format($doerFee, 2) . " for completing '$listingTitle'. Payment has been added to your total profit and is available for withdrawal.";
            $stmt->bind_param("isi", $doerId, $notificationContent, $applicationId);

            if (!$stmt->execute()) {
                error_log("Warning: Failed to create doer notification: " . $stmt->error);
            } else {
                error_log("PRODUCTION: Created payment notification for doer $doerName");
            }
            $stmt->close();
        }

        // 9. Record transaction for audit trail
        $stmt = $conn->prepare("
            INSERT INTO hanapp_balance_transactions (
                user_id, transaction_type, amount, description,
                reference_id, created_at
            ) VALUES (?, 'job_payment_debit', ?, ?, ?, CURRENT_TIMESTAMP)
        ");

        if ($stmt === false) {
            error_log("Warning: Failed to prepare transaction record: " . $conn->error);
        } else {
            $transactionDescription = "Job payment for '$listingTitle' - Doer: $doerName (₱" . number_format($doerFee, 2) . " + ₱" . number_format($transactionFee, 2) . " fee)";
            $stmt->bind_param("idsi", $listerId, $totalAmount, $transactionDescription, $applicationId);

            if (!$stmt->execute()) {
                error_log("Warning: Failed to record transaction: " . $stmt->error);
            } else {
                error_log("PRODUCTION: Recorded transaction for audit trail");
            }
            $stmt->close();
        }

        // Commit all changes atomically
        $conn->commit();
        $conn->autocommit(true);

        // Generate transaction ID for tracking
        $transactionId = 'hanapp_' . $applicationId . '_' . time() . '_' . $listerId;

        error_log("PRODUCTION: HanApp Balance payment completed successfully");
        error_log("PRODUCTION: Lister $listerName paid ₱" . number_format($totalAmount, 2) . ", Doer $doerName received ₱" . number_format($doerFee, 2) . " profit");

        // Clean output buffer and return success response
        ob_clean();
        echo json_encode([
            "success" => true,
            "message" => "TAPP Balance payment processed successfully",
            "transaction_id" => $transactionId,
            "application_id" => $applicationId,
            "lister_id" => $listerId,
            "doer_id" => $doerId,
            "doer_fee" => $doerFee,
            "platform_fee" => $transactionFee,
            "total_amount" => $totalAmount,
            "payment_method" => "tapp_balance",
            "new_lister_balance" => $newListerBalance,
            "new_doer_profit" => $newDoerProfit,
            "job_title" => $listingTitle,
            "status" => "completed"
        ]);
        exit();

    } catch (Exception $e) {
        // Rollback transaction on any error
        $conn->rollback();
        $conn->autocommit(true);
        throw $e;
    }

} catch (Exception $e) {
    error_log("PRODUCTION: HanApp Balance Payment Error: " . $e->getMessage());

    // Clean output buffer and return error response
    ob_clean();
    http_response_code(400);
    echo json_encode([
        "success" => false,
        "message" => $e->getMessage(),
        "error_type" => "payment_processing_error"
    ]);
}

if (isset($conn)) {
    $conn->close();
}
?>
