import 'package:flutter/material.dart';
import 'package:hanapp/models/message.dart';
import 'package:hanapp/models/conversation.dart';
import 'package:hanapp/models/user.dart';
import 'package:hanapp/screens/view_profile_screen.dart';
import 'package:hanapp/services/chat_service.dart';
import 'package:hanapp/utils/auth_service.dart';
import 'package:hanapp/utils/constants.dart' as Constants;
import 'dart:async';
import 'package:hanapp/screens/payment_screen.dart';
import 'package:hanapp/screens/payment_summary_screen.dart';
//import 'package:hanapp/utils/application_service.dart'; // For updating application status
import 'package:geolocator/geolocator.dart'; // Import geolocator for location services
import 'package:url_launcher/url_launcher.dart'; // For opening maps
import 'package:hanapp/screens/review_screen.dart';
import 'components/custom_button.dart'; // NEW: Import ActionService
import 'package:hanapp/services/notification_popup_service.dart';
import 'package:hanapp/models/notification_model.dart';
import 'package:hanapp/services/listing_service.dart';
import 'package:hanapp/screens/review_screen.dart';
import 'package:hanapp/services/action_service.dart';
import 'package:hanapp/services/application_service.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/foundation.dart';

import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:hanapp/utils/api_config.dart';
import 'package:hanapp/utils/word_filter_service.dart';
import 'package:flutter_polyline_points/flutter_polyline_points.dart';
import 'package:hanapp/services/location_sync_service.dart';
import 'map_screen.dart';
import 'package:hanapp/screens/map_screen.dart';
import 'package:hanapp/widgets/banned_words_dialog.dart';
import 'package:hanapp/screens/because_screen.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:path/path.dart' as path;

import 'package:flutter_sound/flutter_sound.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:mime/mime.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:hanapp/utils/image_utils.dart';
import 'package:file_selector/file_selector.dart';
import 'package:hanapp/widgets/inline_video_player.dart';
import 'package:hanapp/models/video.dart';
import 'package:hanapp/services/location_sync_service.dart'; // Import LocationSyncService


// Dummy ViewProfileScreen for the IconButton in AppBar actions.
// Replace with your actual ViewProfileScreen if it exists.

class ChatScreen extends StatefulWidget {
  final int conversationId;
  final int otherUserId; // The ID of the person the current user is chatting with
  final String listingTitle;
  final int applicationId; // Can be null if chat is not linked to an application
  final bool isLister; // True if the current logged-in user is the lister of the associated listing
  // NEW: Add listerId and doerId of the conversation for notification context


  const ChatScreen({
    super.key,
    required this.conversationId,
    required this.otherUserId,
    required this.listingTitle,
    required this.applicationId,
    required this.isLister,

  });

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> with TickerProviderStateMixin, WidgetsBindingObserver {
  final TextEditingController _messageController = TextEditingController();
  final ChatService _chatService = ChatService();
  final ApplicationService _applicationService = ApplicationService();
  final ScrollController _scrollController = ScrollController();
  final ActionService _actionService = ActionService(); // NEW: Instantiate ActionService
  FlutterSoundRecorder? _audioRecorder;
  FlutterSoundPlayer? _audioPlayer;
  AudioPlayer? _messageAudioPlayer; // Add separate audio player for message playback

  // Animation controller for "Mark as Done" button wiggle
  late AnimationController _wiggleAnimationController;
  late Animation<double> _wiggleAnimation;
  late AnimationController _animationController;
  bool _isPlayingAudio = false;
  String? _currentPlayingUrl;
  AudioPlayer? _audioPlayerForMessages; // Separate player for message playback
  late AnimationController _playAnimationController;

  List<Message> _messages = [];
  User? _currentUser;
  Conversation? _conversationDetails; // Stores detailed conversation info
  bool _isLoading = true; // Overall loading state for the screen
  String? _errorMessage;
  Timer? _messageFetchTimer; // Timer for periodic message fetching
  Timer? _statusCheckTimer; // Timer for checking payment status changes
  int _lastMessageId = 0; // To fetch new messages incrementally
  bool _isBlockedByCurrentUser = false; // NEW: Track if other user is blocked by current user
  User? _otherUser;
  bool _isConfirmingCompletion = false; // Separate loading state for confirm button
  bool _hasReview = true; // default to true to hide button until checked
  double? _listingPrice; // Add this variable for storing listing price
  String? _lastKnownStatus; // Track the last known application status
  bool _isRecording = false;
  bool _isPlaying = false;
  String? _recordingPath;

  @override
  void initState() {
    super.initState();
    
    // Initialize audio recorder and player
    _audioRecorder = FlutterSoundRecorder();
    _audioPlayer = FlutterSoundPlayer();
    
    // Initialize wiggle animation
    _wiggleAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _wiggleAnimation = Tween<double>(begin: -0.05, end: 0.05).animate(
      CurvedAnimation(
        parent: _wiggleAnimationController,
        curve: Curves.easeInOut,
      ),
    );
    
    // Initialize recording animation
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    // Initialize play animation controller
    _playAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _initializeChat();
    _checkIfHasReview();
    _initializeAudio();
    _startStatusMonitoring();
  }

  Future<void> _initializeAudio() async {
    await _audioRecorder!.openRecorder();
    await _audioPlayer!.openPlayer();
    _audioPlayerForMessages = AudioPlayer(); // Initialize audio player for messages
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _wiggleAnimationController.dispose(); // Dispose animation controller
    _animationController.dispose(); // Dispose recording animation controller
    _playAnimationController.dispose(); // Dispose play animation controller
    _messageFetchTimer?.cancel(); // Cancel the timer to prevent memory leaks
    _statusCheckTimer?.cancel(); // Cancel the status check timer
    _audioRecorder?.closeRecorder();
    _audioPlayer?.closePlayer();
    _audioPlayerForMessages?.dispose(); // Dispose audio player for messages
    super.dispose();
  }

  Future<void> _initializeChat() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      _currentUser = await AuthService.getUser();
      if (_currentUser == null) {
        throw Exception("Current user not logged in or user ID is null.");
      }
      
      // Update location when chat screen opens to ensure current location for message bubbles
      try {
        await LocationSyncService.instance.getCurrentLocationAndUpdate();
        debugPrint('Location updated successfully when opening chat');
      } catch (e) {
        debugPrint('Failed to update location when opening chat: $e');
      }
      
      await _checkBlockedStatus();
      await _fetchConversationDetails();
      if (_conversationDetails != null) {
        await _fetchListingPrice(); // Add this line
        await _fetchMessages();
        if (widget.isLister &&
            _conversationDetails?.applicationStatus == 'in_progress' &&
            _messages.any((m) => m.type == 'doer_marked_complete_request')) {
          _wiggleAnimationController.repeat(reverse: true);
        }
        _startMessagePolling();
        
        // Start wiggle animation for "Mark as Done" button if user is doer and job is in progress
        if (!widget.isLister && _conversationDetails!.applicationStatus == 'in_progress') {
          Future.delayed(const Duration(milliseconds: 1000), () {
            if (mounted) {
              _startWiggleAnimation();
            }
          });
        }
      } else {
        throw Exception(_errorMessage ?? "Failed to load conversation details, cannot fetch messages.");
      }
    } catch (e) {
      debugPrint('ChatScreen _initializeChat error: $e');
      setState(() {
        _errorMessage = "Failed to load chat: $e";
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _checkIfHasReview() async {
    final applicationId = widget.applicationId;
    print('DEBUG: ENTERED _checkIfHasReview for applicationId: $applicationId');
    if (applicationId != null) {
      print('DEBUG: Checking if review exists for application $applicationId');
      final url = Uri.parse('${ApiConfig.baseUrl}/reviews/has_review.php?application_id=$applicationId');
      print('DEBUG: Requesting URL: $url');
      // Set a User-Agent header to mimic a browser
      final response = await http.get(url, headers: {'User-Agent': 'Mozilla/5.0'});
      print('DEBUG: hasReview raw response: \\${response.body}');
      try {
        final data = jsonDecode(response.body);
        print('DEBUG: Decoded data: $data');
        print('DEBUG: has_review type: \\${data['has_review']?.runtimeType}');
        final hasReview = data['has_review'] == true || data['has_review'] == 'true';
        print('DEBUG: Parsed hasReview: \\${data['has_review']} -> $hasReview');
        setState(() {
          _hasReview = hasReview;
        });
      } catch (e) {
        print('DEBUG: Error parsing hasReview response: $e');
        setState(() {
          _hasReview = false;
        });
      }
    } else {
      print('DEBUG: No applicationId provided, cannot check for review');
    }
  }

  // NEW: Method to check blocked status
  Future<void> _checkBlockedStatus() async {
    if (_currentUser == null) return;
    final response = await _actionService.getBlockedStatus(
      currentUserId: _currentUser!.id!,
      targetUserId: widget.otherUserId,
    );
    if (response['success']) {
      setState(() {
        _isBlockedByCurrentUser = response['is_blocked'];
      });
    } else {
      _showSnackBar(response['message'] ?? 'Failed to check block status.', isError: true);
    }
  }

  void _startMessagePolling() {
    _messageFetchTimer?.cancel();
    _messageFetchTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _fetchMessages(isPolling: true);
    });
  }

  Future<void> _fetchConversationDetails() async {
    // Validate conversation ID
    if (widget.conversationId <= 0) {
      setState(() {
        _errorMessage = 'Invalid conversation ID. This chat may no longer exist.';
        _isLoading = false;
      });
      return;
    }

    try {
      final response = await _chatService.getConversationDetails(widget.conversationId);
      
      if (response['success'] == true && response['details'] != null) {
        setState(() {
          _conversationDetails = Conversation.fromJson(response['details'], _currentUser!.id!);
          _errorMessage = null;

          // Initialize last known status for monitoring
          if (_lastKnownStatus == null) {
            _lastKnownStatus = _conversationDetails?.applicationStatus;
          }
        });

        // Fetch listing price
        await _fetchListingPrice();
      } else {
        setState(() {
          _errorMessage = response['message'] ?? 'Failed to load conversation details';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading conversation details: $e';
        _isLoading = false;
      });
      debugPrint('Error fetching conversation details: $e');
    }
  }

  Future<void> _fetchListingPrice() async {
    try {
      if (_conversationDetails?.listingId != null) {
        final response = await http.get(
          Uri.parse('${ApiConfig.baseUrl}/get_listing_payment_details.php?listing_id=${_conversationDetails!.listingId}'),
          headers: {'Content-Type': 'application/json'},
        );
        
        if (response.statusCode == 200) {
          final data = json.decode(response.body);
          if (data['success'] == true && data['listing'] != null) {
            setState(() {
              _listingPrice = double.tryParse(data['listing']['price']?.toString() ?? '0') ?? 0.0;
            });
          }
        }
      }
    } catch (e) {
      debugPrint('Error fetching listing price: $e');
      // Set default price if fetch fails
      setState(() {
        _listingPrice = 0.0;
      });
    }
  }

  Future<void> _fetchMessages({bool isPolling = false}) async {
    if (_currentUser == null || _currentUser!.id == null || _conversationDetails == null) {
      debugPrint('ChatScreen: Cannot fetch messages. Current user or conversation details missing.');
      return;
    }

    debugPrint('ChatScreen: Fetching messages for conversation ${widget.conversationId} from lastMessageId: $_lastMessageId (isPolling: $isPolling)');
    final response = await _chatService.getMessages(
      conversationId: widget.conversationId,
      lastMessageId: _lastMessageId,
    );

    if (response['success'] && response['messages'] != null) {
      List<Message> newMessages = (response['messages'] as List)
          .map((msgJson) => Message.fromJson(msgJson as Map<String, dynamic>))
          .toList();

      if (newMessages.isNotEmpty) {
        setState(() {
          final existingIds = _messages.map((m) => m.id).toSet();
          final uniqueNewMessages = newMessages.where((m) => !existingIds.contains(m.id)).toList();
          _messages.addAll(uniqueNewMessages);
          _messages.sort((a, b) => a.sentAt.compareTo(b.sentAt));
          if (_messages.isNotEmpty) {
            _lastMessageId = _messages.last.id;
          }
        });
        if (!isPolling || (_scrollController.hasClients && _scrollController.position.pixels == _scrollController.position.maxScrollExtent)) {
          _scrollToBottom();
        }
        debugPrint('ChatScreen: Fetched ${newMessages.length} new messages. Total: ${_messages.length}');
      } else {
        debugPrint('ChatScreen: No new messages fetched.');
      }
    } else {
      if (!isPolling) {
        _showSnackBar(response['message'] ?? 'Failed to fetch messages.', isError: true);
      }
      debugPrint('ChatScreen: Failed to fetch messages during polling: ${response['message']}');
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  // Method to start wiggle animation for "Mark as Done" button
  void _startWiggleAnimation() {
    _wiggleAnimationController.reset();
    _wiggleAnimationController.repeat(reverse: true, period: const Duration(milliseconds: 100));
    
    // Stop the animation after a few cycles
    Timer(const Duration(milliseconds: 600), () {
      if (mounted) {
        _wiggleAnimationController.stop();
        _wiggleAnimationController.reset();
      }
    });
  }

  // Method to stop wiggle animation
  void _stopWiggleAnimation() {
    _wiggleAnimationController.stop();
  }

  Future<void> _sendMessage({String messageType = 'text', String messageContent = '', Map<String, dynamic>? extraData}) async {
    if (messageType == 'text' && _messageController.text.trim().isEmpty) return;

    if (_currentUser == null || _isBlockedByCurrentUser) {
      if (_isBlockedByCurrentUser) {
        _showSnackBar('You have blocked this user. Unblock them to send messages.', isError: true);
      }
      return;
    }

    final messageContentToSend = messageType == 'text' ? _messageController.text.trim() : messageContent;
    
    // Check for banned words in the message content
    if (messageType == 'text' && messageContentToSend.isNotEmpty) {
      print('ChatScreen: Checking message for banned words: "$messageContentToSend"');
      final bannedWords = await WordFilterService().findBannedWords(messageContentToSend);
      
      if (bannedWords.isNotEmpty) {
        print('ChatScreen: Banned words detected in message');
        // Show banned words dialog
        await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return BannedWordsDialog(
              bannedWordsByField: {'message': bannedWords['text'] ?? []},
            );
          },
        );
        return; // Don't send the message
      }
    }
    
    _messageController.clear();

    await _chatService.sendMessage(
      conversationId: widget.conversationId,
      senderId: _currentUser!.id!,
      receiverId: widget.otherUserId,
      messageContent: messageContentToSend,
      messageType: messageType,
      // extraData: extraData,
      // listingId: widget.applicationId,
    );
  }
  // Future<void> _sendMessage() async {
  //   if (_messageController.text.trim().isEmpty || _currentUser == null || _currentUser!.id == null || _conversationDetails == null || _isBlockedByCurrentUser) {
  //     return;
  //   }
  //
  //   final String messageContent = _messageController.text.trim();
  //   _messageController.clear();
  //
  //   final optimisticMessage = Message(
  //     id: -(_messages.length + 1),
  //     conversationId: widget.conversationId,
  //     senderId: _currentUser!.id!,
  //     receiverId: widget.otherUserId,
  //     content: messageContent,
  //     sentAt: DateTime.now(),
  //     type: 'text',
  //   );
  //
  //   setState(() {
  //     _messages.add(optimisticMessage);
  //     _messages.sort((a, b) => a.sentAt.compareTo(b.sentAt));
  //   });
  //   _scrollToBottom();
  //
  //   final response = await _chatService.sendMessage(
  //     conversationId: widget.conversationId,
  //     senderId: _currentUser!.id!,
  //     receiverId: widget.otherUserId,
  //     messageContent: messageContent,
  //     messageType: 'text',
  //   );
  //
  //   if (!response['success']) {
  //     _showSnackBar('Failed to send message: ${response['message']}', isError: true);
  //     _fetchMessages();
  //   } else {
  //     _fetchMessages();
  //   }
  // }

  Future<void> _handleStartProject() async {
    if (_currentUser == null || _currentUser!.id == null || _conversationDetails == null) {
      _showSnackBar('Error: Missing user or conversation data to start project.', isError: true);
      return;
    }
    if (!widget.isLister) {
      _showSnackBar('Only the Lister can start the project.', isError: true);
      return;
    }
    
    // For ASAP listings without application, we'll create one
    if (_conversationDetails!.listingType == 'ASAP' && widget.applicationId == null) {
      await _handleStartAsapProject();
      return;
    }
    
    // For regular listings with applications
    if (widget.applicationId == null) {
      _showSnackBar('Error: Application data missing to start project.', isError: true);
      return;
    }
    
    if (_conversationDetails!.applicationStatus != 'pending' && _conversationDetails!.applicationStatus != 'accepted') {
      _showSnackBar('Project can only be started if application status is "pending" or "accepted". Current status: ${_conversationDetails!.applicationStatus}.', isError: true);
      return;
    }

    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Confirm Start Project'),
          content: const Text('Are you sure you want to proceed to payment? The project will start after payment is completed.'),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(dialogContext).pop(false);
              },
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(dialogContext).pop(true);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Constants.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Start'),
            ),
          ],
        );
      },
    );

    if (confirm == true) {
      setState(() { _isLoading = true; });
      try {
        // Don't change status yet - just redirect to payment
        // Status will change to 'in_progress' when payment is completed

        // Send a system message to inform both parties
        await _chatService.sendMessage(
          conversationId: widget.conversationId,
          senderId: _currentUser!.id!,
          receiverId: widget.otherUserId,
          messageContent: "${_currentUser!.fullName} initiated payment for the project.",
          messageType: 'system',
        );
        await _fetchMessages();

        // Redirect to payment summary screen
        if (mounted) {
          final paymentResult = await _navigateToPaymentSummary();
          // If payment was initiated, refresh the conversation details
          if (paymentResult == true) {
            await _fetchConversationDetails();
            await _fetchMessages(); // Refresh messages

            // Note: "Project started" message will be sent automatically by the webhook
            // when payment is actually completed and status changes to 'in_progress'
            // _showSnackBar('Payment initiated. You will be notified when payment is confirmed.', isError: false);
          }
        }
      } catch (e) {
        _showSnackBar('Error starting project: $e', isError: true);
        debugPrint('Error starting project: $e');
      } finally {
        setState(() { _isLoading = false; });
      }
    }
  }

  // NEW: Handle starting ASAP projects by creating an application first
  Future<void> _handleStartAsapProject() async {
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Confirm Start ASAP Project'),
          content: const Text('Are you sure you want to proceed to payment? This will create an application and the project will start after payment is completed.'),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(dialogContext).pop(false);
              },
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(dialogContext).pop(true);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Constants.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Start'),
            ),
          ],
        );
      },
    );

    if (confirm == true) {
      setState(() { _isLoading = true; });
      try {
        // Create an application for the ASAP listing
        final createResponse = await _applicationService.createApplication(
          listingId: _conversationDetails!.listingId,
          listingType: 'ASAP',
          listerId: _conversationDetails!.listerId,
          doerId: _conversationDetails!.doerId,
          message: 'ASAP project started by lister',
          listingTitle: _conversationDetails!.listingTitle ?? 'ASAP Project',
        );

        if (createResponse['success']) {
          // Get the application ID from the response
          final int? newApplicationId = createResponse['application_id'];

          if (newApplicationId != null) {
            // Don't change status yet - just redirect to payment
            // Status will change to 'in_progress' when payment is completed

            await _fetchConversationDetails();

            // Send a system message to inform both parties
            await _chatService.sendMessage(
              conversationId: widget.conversationId,
              senderId: _currentUser!.id!,
              receiverId: widget.otherUserId,
              messageContent: "${_currentUser!.fullName} initiated payment for the ASAP project.",
              messageType: 'system',
            );
            await _fetchMessages();

            // Redirect to payment summary screen for ASAP projects
            if (mounted) {
              final paymentResult = await _navigateToPaymentSummary();
              // If payment was initiated, refresh the conversation details
              if (paymentResult == true) {
                await _fetchConversationDetails();
                await _fetchMessages(); // Refresh messages

                // Note: "Project started" message will be sent automatically by the webhook
                // when payment is actually completed and status changes to 'in_progress'
                // _showSnackBar('Payment initiated. You will be notified when payment is confirmed.', isError: false);
              }
            }
          } else {
            _showSnackBar('Failed to get application ID after creation.', isError: true);
          }
        } else {
          _showSnackBar('Failed to create application: ${createResponse['message']}', isError: true);
        }
      } catch (e) {
        _showSnackBar('Error starting ASAP project: $e', isError: true);
        debugPrint('Error starting ASAP project: $e');
      } finally {
        setState(() { _isLoading = false; });
      }
    }
  }

// NEW: Method to handle reporting a user
  Future<void> _reportUser() async {
    if (_currentUser == null) return;

    final detailsController = TextEditingController();
    File? selectedImage;
    String? selectedReason;
    
    // Predefined report reasons
    final List<String> reportReasons = [
      'Inappropriate behavior',
      'Spam or scam',
      'Harassment',
      'Fake profile',
      'Inappropriate content',
      'No-show/Unreliable',
      'Payment issues',
      'Other'
    ];

    // Function to show image source dialog
    Future<void> pickImage() async {
      final ImagePicker picker = ImagePicker();
      await showDialog<ImageSource>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Select Image Source'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.camera_alt),
                  title: const Text('Camera'),
                  onTap: () => Navigator.pop(context, ImageSource.camera),
                ),
                ListTile(
                  leading: const Icon(Icons.photo_library),
                  title: const Text('Gallery'),
                  onTap: () => Navigator.pop(context, ImageSource.gallery),
                ),
              ],
            ),
          );
        },
      ).then((source) async {
        if (source != null) {
          final pickedFile = await picker.pickImage(source: source);
          if (pickedFile != null) {
            selectedImage = File(pickedFile.path);
          }
        }
      });
    }

    await showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Report User'),
              content: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Reason for reporting:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: selectedReason,
                      decoration: InputDecoration(
                        hintText: 'Select a reason',
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                      ),
                      items: reportReasons.map((String reason) {
                        return DropdownMenuItem<String>(
                          value: reason,
                          child: Text(reason),
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        setState(() {
                          selectedReason = newValue;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    const Text('Additional details:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    TextField(
                      controller: detailsController,
                      decoration: InputDecoration(
                        hintText: 'Provide more details (optional)',
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 16),
                    const Text('Attach image (optional):', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    ElevatedButton.icon(
                      onPressed: () async {
                        await pickImage();
                        setState(() {}); // Refresh the dialog to show selected image
                      },
                      icon: const Icon(Icons.image),
                      label: const Text('Select Image'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blueGrey,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                      ),
                    ),
                    if (selectedImage != null) Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text('Selected: ${path.basename(selectedImage!.path)}', style: const TextStyle(color: Colors.green)),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(dialogContext).pop(),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    if (selectedReason == null || selectedReason!.isEmpty) {
                      _showSnackBar('Please select a reason for reporting.', isError: true);
                      return;
                    }
                    Navigator.of(dialogContext).pop();
                    final response = await _actionService.reportUser(
                      reporterUserId: _currentUser!.id!,
                      reportedUserId: widget.otherUserId,
                      listingId: _conversationDetails?.listingId,
                      applicationId: widget.applicationId,
                      reportReason: selectedReason!,
                      reportDetails: detailsController.text.trim(),
                      reportImage: selectedImage,
                    );
                    _showSnackBar(response['success'] ? 'Report submitted successfully.' : response['message'] ?? 'Failed to submit report.', isError: !response['success']);
                  },
                  child: const Text('Submit'),
                ),
              ],
            );
          },
        );
      },
    );
  }
// NEW: Method to handle blocking/unblocking a user
  Future<void> _toggleBlockUser() async {
    if (_currentUser == null) return;

    String actionMessage = _isBlockedByCurrentUser ? 'unblock' : 'block';
    String confirmMessage = _isBlockedByCurrentUser
        ? 'Are you sure you want to unblock ${_otherUser?.fullName ?? 'this user'}? You will be able to send and receive messages again.'
        : 'Are you sure you want to block ${_otherUser?.fullName ?? 'this user'}? You will no longer be able to send or receive messages from them.';

    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text('Confirm $actionMessage'),
          content: Text(confirmMessage),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(dialogContext).pop(false);
              },
            ),
            CustomButton( // Using CustomButton
              text: actionMessage == 'block' ? 'Block' : 'Unblock',
              onPressed: () {
                Navigator.of(dialogContext).pop(true);
              },
              color: actionMessage == 'block' ? Colors.red : Constants.primaryColor,
              textColor: Colors.white,
              borderRadius: 8.0,
              height: 40.0,
              width: actionMessage == 'block' ? 80 : 100,
            ),
          ],
        );
      },
    );

    if (confirm == true) {
      Map<String, dynamic> response;
      if (_isBlockedByCurrentUser) {
        response = await _actionService.unblockUser(
          userId: _currentUser!.id!,
          blockedUserId: widget.otherUserId,
        );
      } else {
        response = await _actionService.blockUser(
          userId: _currentUser!.id!,
          blockedUserId: widget.otherUserId,
        );
      }

      if (response['success']) {
        _showSnackBar(response['message'] ?? 'Action successful!');
        await _checkBlockedStatus(); // Re-check status to update UI
      } else {
        _showSnackBar('Failed to $actionMessage user: ${response['message']}', isError: true);
      }
    }
  }
  Future<void> _handleRejectApplication() async {
    if (_currentUser == null || _currentUser!.id == null || _conversationDetails == null || widget.applicationId == null) {
      _showSnackBar('Error: Missing user or conversation data to reject application.', isError: true);
      return;
    }
    if (!widget.isLister) {
      _showSnackBar('Only the Lister can reject an application.', isError: true);
      return;
    }
    if (_conversationDetails!.applicationStatus == 'rejected' || _conversationDetails!.applicationStatus == 'completed') {
      _showSnackBar('Application is already rejected or completed.', isError: true);
      return;
    }

    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Confirm Reject Application'),
          content: const Text('Are you sure you want to reject this application? This cannot be easily undone.'),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(dialogContext).pop(false);
              },
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(dialogContext).pop(true);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Reject'),
            ),
          ],
        );
      },
    );

    if (confirm == true) {
      setState(() { _isLoading = true; });
      try {
        final response = await _applicationService.updateApplicationStatus(
          applicationId: widget.applicationId!,
          newStatus: 'rejected',
          currentUserId: _currentUser!.id!,
        );

        if (response['success']) {
          // Soft delete the conversation
          final conversationResponse = await _chatService.softDeleteConversation(
            widget.conversationId,
          );
          
          if (conversationResponse['success']) {
            debugPrint('Conversation soft deleted successfully');
          } else {
            debugPrint('Failed to soft delete conversation: ${conversationResponse['message']}');
          }

          // Send system message before archiving
          await _chatService.sendMessage(
            conversationId: widget.conversationId,
            senderId: _currentUser!.id!,
            receiverId: widget.otherUserId,
            messageContent: "This application has been rejected. The conversation will no longer be visible.",
            messageType: 'system',
          );

          // Create and show popup notification for the doer
          if (_otherUser != null) {
            final notification = NotificationModel(
              id: 0, // This will be set by the backend
              userId: widget.otherUserId, // Doer's ID
              senderId: _currentUser!.id, // Lister's ID
              type: 'application_rejected',
              title: 'Application Rejected',
              content: "Your application for '${_conversationDetails?.listingTitle ?? widget.listingTitle}' has been rejected by the lister.",
              createdAt: DateTime.now(),
              isRead: false,
              associatedId: widget.applicationId,
              relatedListingTitle: _conversationDetails?.listingTitle ?? widget.listingTitle,
            );
            
            // Show popup notification for the doer
            NotificationPopupService().showNotification(context, notification);
          }

          // Navigate back and show success message
        if (mounted) {
          // Check if this is an ASAP listing and navigate accordingly
          if (_conversationDetails?.listingType == 'ASAP') {
            // For ASAP listings, navigate back to the ASAP doer search screen with correct argument keys
            Navigator.of(context).pushReplacementNamed(
              '/asap_doer_search',
              arguments: {
                'listingId': _conversationDetails!.listingId,
                'listing_data': {
                  'id': _conversationDetails!.listingId,
                  'title': _conversationDetails?.listingTitle ?? widget.listingTitle,
                  'lister_id': _conversationDetails!.listerId,
                  'location_address': _conversationDetails!.listingLocationAddress,
                  'lister_full_name': _conversationDetails!.listerFullName,
                  'lister_profile_picture_url': _conversationDetails!.listerProfilePictureUrl,
                  'lister_address_details': _conversationDetails!.listerAddressDetails,
                  'project_start_date': _conversationDetails!.projectStartDate?.toIso8601String(),
                },
                'listing_latitude': _conversationDetails!.listingLatitude ?? 0.0,
                'listing_longitude': _conversationDetails!.listingLongitude ?? 0.0,
                'preferred_doer_gender': _conversationDetails!.preferredDoerGender ?? 'Any',
                'max_distance': _conversationDetails!.maxDistance ?? 10.0,
                // Additional context data
                'rejection_reason': 'doer_rejected',
                'is_lister': widget.isLister,
              },
            );
          } else {
            // For regular listings, just pop back
            Navigator.of(context).pop();
          }
          
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Application rejected and conversation archived'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        } else {
          _showSnackBar('Failed to reject application: ${response['message']}', isError: true);
        }
      } catch (e) {
        _showSnackBar('Error rejecting application: $e', isError: true);
        debugPrint('Error rejecting application: $e');
      } finally {
        setState(() { _isLoading = false; });
      }
    }
  }

  Future<void> _checkLocationPermissions() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      _showSnackBar('Location services are disabled. Please enable them.', isError: true);
      return;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        _showSnackBar('Location permissions are denied. Cannot share location.', isError: true);
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      _showSnackBar('Location permissions are permanently denied. Please enable them from app settings.', isError: true);
      return;
    }
    return;
  }

  Future<void> _shareCurrentLocation() async {
    if (_currentUser == null || _currentUser!.id == null || _conversationDetails == null) {
      _showSnackBar('Error: User or conversation data missing.', isError: true);
      return;
    }

    setState(() { _isLoading = true; });
    try {
      await _checkLocationPermissions();

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      final String locationMessage = "My current location: latitude ${position.latitude}, longitude ${position.longitude}";
      final String mapLink = "https://www.google.com/maps/search/?api=1&query=${position.latitude},${position.longitude}";

      final response = await _chatService.sendMessage(
        conversationId: widget.conversationId,
        senderId: _currentUser!.id!,
        receiverId: widget.otherUserId,
        messageContent: locationMessage,
        messageType: 'location_share',
        locationData: {'latitude': position.latitude, 'longitude': position.longitude, 'mapLink': mapLink},
      );

      if (response['success']) {
        _showSnackBar('Location shared successfully!');
        _fetchMessages();
      } else {
        _showSnackBar('Failed to share location: ${response['message']}', isError: true);
      }
    } catch (e) {
      _showSnackBar('Error getting/sharing location: $e', isError: true);
      debugPrint('Error getting/sharing location: $e');
    } finally {
      setState(() { _isLoading = false; });
    }
  }

  Future<void> _viewCurrentLocation(String locationString) async {
    final latLngRegex = RegExp(r'latitude\s*(-?\d+\.?\d*),\s*longitude\s*(-?\d+\.?\d*)');
    final match = latLngRegex.firstMatch(locationString);

    if (match != null) {
      final latitude = double.parse(match.group(1)!);
      final longitude = double.parse(match.group(2)!);
      
      // Navigate to MapScreen with both doer and lister locations
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => MapScreen(
            latitude: latitude,
            longitude: longitude,
            title: 'Doer\'s Location',
            listerLatitude: _conversationDetails?.listerLatitude,
            listerLongitude: _conversationDetails?.listerLongitude,
          ),
        ),
      );
    } else {
      _showSnackBar('Could not parse location string: $locationString', isError: true);
    }
  }

  Future<void> _handleMarkAsDone() async {
    if (_currentUser == null || _currentUser!.id == null || _conversationDetails == null || widget.applicationId == null) {
      _showSnackBar('Error: Missing user or conversation data to mark as done.', isError: true);
      return;
    }
    if (widget.isLister) {
      _showSnackBar('Only the Doer can mark a project as done.', isError: true);
      return;
    }
    if (_conversationDetails!.applicationStatus != 'in_progress') {
      _showSnackBar('Project must be "In Progress" to be marked as done. Current status: ${_conversationDetails!.applicationStatus}.', isError: true);
      return;
    }

    // Stop the wiggle animation when user clicks the button
    _stopWiggleAnimation();

    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Confirm Mark as Done'),
          content: const Text('Are you sure you want to mark this project as "Done"? The Lister will need to confirm completion.'),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(dialogContext).pop(false);
              },
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(dialogContext).pop(true);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Constants.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Mark Done'),
            ),
          ],
        );
      },
    );

    if (confirm == true) {
      setState(() { _isLoading = true; });
      try {
        // Send a system message to notify the Lister that the Doer marked it as done
        final response = await _chatService.sendMessage(
          conversationId: widget.conversationId,
          senderId: _currentUser!.id!,
          receiverId: widget.otherUserId,
          messageContent: "${_currentUser!.fullName} marked the project as done. Please confirm.",
          messageType: 'system', // New message type
        );

        if (response['success']) {
          _showSnackBar(response['message'] ?? 'Project marked as done. Waiting for Lister confirmation.');
          await _fetchMessages(); // Refresh messages to show the new system message
          // The application status remains 'in_progress' until the Lister confirms.
        } else {
          _showSnackBar('Failed to mark as done: ${response['message']}', isError: true);
        }
      } catch (e) {
        _showSnackBar('Error marking as done: $e', isError: true);
        debugPrint('Error marking as done: $e');
      } finally {
        setState(() { _isLoading = false; });
      }
    }
  }
// FIXED: Handle Lister's confirmation of project completion
  Future<void> _handleConfirmCompletion() async {
    // Check for necessary data more robustly
    if (_currentUser == null || _currentUser!.id == null || _currentUser!.id == 0 ||
        widget.applicationId == 0 || // Assuming 0 is an invalid default for applicationId
        widget.otherUserId == 0 || // Assuming 0 is an invalid default for otherUserId
        (_conversationDetails?.listingTitle ?? widget.listingTitle).isEmpty) {
      _showSnackBar('Error: Missing user or application data to confirm completion.', isError: true);
      // Log the specific missing values for debugging
      debugPrint('CONFIRM COMPLETION ERROR: _currentUser: ${_currentUser?.id}, '
          'applicationId: ${widget.applicationId}, '
          'otherUserId: ${widget.otherUserId}, '
          'listingTitle: "${_conversationDetails?.listingTitle ?? widget.listingTitle}"');
      return;
    }

    if (!widget.isLister) {
      _showSnackBar('Only the Lister can confirm project completion.', isError: true);
      return;
    }

    if (_isConfirmingCompletion) return; // Prevent multiple taps

    setState(() {
      _isConfirmingCompletion = true;
    });

    final bool? confirm = await showDialog<bool>(
  context: context,
  builder: (BuildContext context) {
    return AlertDialog(
      contentPadding: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      content: Container(
        decoration: BoxDecoration(
          color: Constants.primaryColor, // Primary color background (blue)
          borderRadius: BorderRadius.circular(20),
        ),
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '(${_otherUser?.fullName ?? "name"}) marked as done. Please confirm',
              style: const TextStyle(color: Colors.white, fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            AnimatedBuilder(
              animation: _wiggleAnimation,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(_wiggleAnimation.value * 10, 0),
                  child: SizedBox(
                    width: 120,
                    child: ElevatedButton(
                      onPressed: () {
                        _startWiggleAnimation();
                        Navigator.of(context).pop(true);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.yellow, // Yellow button
                        foregroundColor: Colors.black, // Black text
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                        elevation: 3,
                        shadowColor: Colors.black26,
                      ),
                      child: const Text('Confirm'),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  },
);

    if (confirm == true) {
      try {
        final response = await _applicationService.updateApplicationStatus(
          applicationId: widget.applicationId!, // Now guaranteed to be non-null/non-zero
          newStatus: 'completed',
          currentUserId: _currentUser!.id!,
        );

        if (response['success']) {
          _showSnackBar(response['message'] ?? 'Project confirmed as completed.');

          // Create and show popup notification for the doer
          if (_otherUser != null) {
            final notification = NotificationModel(
              id: 0, // This will be set by the backend
              userId: widget.otherUserId, // Doer's ID
              senderId: _currentUser!.id, // Lister's ID
              type: 'job_completed_by_lister',
              title: 'Job Completed by Lister',
              content: "Congratulations! The lister has marked the job '${_conversationDetails?.listingTitle ?? widget.listingTitle}' as completed.",
              createdAt: DateTime.now(),
              isRead: false,
              associatedId: widget.applicationId,
              listingId: _conversationDetails?.listingId,
              senderFullName: _currentUser!.fullName,
              conversationIdForChat: widget.conversationId,
              listerIdForChat: _currentUser!.id,
              doerIdForChat: widget.otherUserId,
              relatedListingTitle: _conversationDetails?.listingTitle ?? widget.listingTitle,
            );
            
            // Show popup notification for the doer
            NotificationPopupService().showNotification(context, notification);
          }

          // Send a system message to indicate project completion
          await _sendMessage(
            messageType: 'system',
            messageContent: "${_currentUser!.fullName} has confirmed the project completion. You can now leave a review.",
            extraData: {
              'action': 'doer_marked_completed_request', // Example action for a specific UI/backend trigger
            },
          );

          // After successful completion and sending system message, navigate to review screen
          if (!mounted) return;
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => ReviewScreen(
                reviewerId: _currentUser!.id!,
                reviewedUserId: widget.otherUserId,
                listingId: widget.applicationId,
                listingTitle: _conversationDetails?.listingTitle ?? widget.listingTitle,
              ),
            ),
          ).then((_) => _checkIfHasReview());

        } else {
          _showSnackBar('Failed to confirm completion: ${response['message']}', isError: true);
        }
      } catch (e) {
        _showSnackBar('Error confirming completion: ${e.toString()}', isError: true);
        debugPrint('Error confirming completion: $e');
      }
    }

    setState(() {
      _isConfirmingCompletion = false; // Reset loading state
    });
  }

  Future<void> _handleAcceptApplication() async {
    if (_currentUser == null || _currentUser!.id == null || _conversationDetails == null || widget.applicationId == null) {
      _showSnackBar('Error: Missing user or conversation data to accept application.', isError: true);
      return;
    }
    if (!widget.isLister) {
      _showSnackBar('Only the Lister can accept an application.', isError: true);
      return;
    }
    if (_conversationDetails!.applicationStatus == 'accepted' || _conversationDetails!.applicationStatus == 'completed') {
      _showSnackBar('Application is already accepted or completed.', isError: true);
      return;
    }

    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Confirm Accept Application'),
          content: const Text('Are you sure you want to accept this application?'),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(dialogContext).pop(false);
              },
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(dialogContext).pop(true);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('Accept'),
            ),
          ],
        );
      },
    );

    if (confirm == true) {
      setState(() { _isLoading = true; });
      try {
        final response = await _applicationService.updateApplicationStatus(
          applicationId: widget.applicationId!,
          newStatus: 'accepted',
          currentUserId: _currentUser!.id!,
        );

        if (response['success']) {
          _showSnackBar(response['message'] ?? 'Application accepted.');
          
          // Create and show popup notification for the doer
          if (_otherUser != null) {
            final notification = NotificationModel(
              id: 0, // This will be set by the backend
              userId: widget.otherUserId, // Doer's ID
              senderId: _currentUser!.id, // Lister's ID
              type: 'application_accepted',
              title: 'Application Accepted',
              content: "Congratulations! Your application for '${_conversationDetails?.listingTitle ?? widget.listingTitle}' has been accepted by the lister.",
              createdAt: DateTime.now(),
              isRead: false,
              associatedId: widget.applicationId,
              relatedListingTitle: _conversationDetails?.listingTitle ?? widget.listingTitle,
            );
            
            // Show popup notification for the doer
            NotificationPopupService().showNotification(context, notification);
          }
          
          await _fetchConversationDetails();
          await _chatService.sendMessage(
            conversationId: widget.conversationId,
            senderId: _currentUser!.id!,
            receiverId: widget.otherUserId,
            messageContent: "Your application has been marked as 'Accepted' by the Lister.",
            messageType: 'system',
          );
          _fetchMessages();
        } else {
          _showSnackBar('Failed to accept application: ${response['message']}', isError: true);
        }
      } catch (e) {
        _showSnackBar('Error accepting application: $e', isError: true);
        debugPrint('Error accepting application: $e');
      } finally {
        setState(() { _isLoading = false; });
      }
    }
  }



  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isError ? Icons.error_outline : Icons.check_circle_outline,
              color: Colors.white,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: isError ? Colors.red : Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(10),
      ),
    );
  }







  // Helper method to determine file type based on extension
  String _getFileType(String fileName) {
    final extension = fileName.toLowerCase().split('.').last;
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension)) {
      return 'image';
    } else if (['mp4', 'mov', 'avi', 'mkv', 'wmv', 'flv', 'webm'].contains(extension)) {
      return 'video';
    } else if (['pdf'].contains(extension)) {
      return 'pdf';
    } else {
      return 'other';
    }
  }

  // Helper method to check file size limit based on type
  bool _isFileSizeValid(int fileSizeInBytes, String fileType) {
    double fileSizeInMB = fileSizeInBytes / (1024 * 1024);
    
    switch (fileType) {
      case 'video':
        return fileSizeInMB <= 10; // 10MB limit for videos
      case 'image':
      case 'pdf':
      case 'other':
      default:
        return fileSizeInMB <= 10; // 10MB limit for other files
    }
  }

  Future<void> _pickFile() async {
    try {
      const XTypeGroup typeGroup = XTypeGroup(
        label: 'files',
        extensions: ['*'], // For Android: allow all files
        uniformTypeIdentifiers: ['public.data'], // For iOS: allow all files
      );
      
      final XFile? file = await openFile(
        acceptedTypeGroups: [typeGroup],
      );

      if (file != null) {
        File selectedFile = File(file.path);
        
        // Check file size based on type
        int fileSizeInBytes = await selectedFile.length();
        String fileType = _getFileType(file.name);
        
        if (!_isFileSizeValid(fileSizeInBytes, fileType)) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('File size must be less than 10MB for ${fileType}s')),
          );
          return;
        }
        
        // Show confirmation dialog
        bool? shouldSend = await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Send File'),
              content: Text(
                'Send "${file.name}" (${_formatFileSize(fileSizeInBytes)})?',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('Send'),
                ),
              ],
            );
          },
        );
        
        if (shouldSend == true) {
          await _sendFileMessage(selectedFile, file.name);
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error picking file: $e')),
      );
    }
  }

  void _pickAudio() async {
    try {
      if (_isRecording) {
        // Stop recording
        await _stopRecording();
      } else {
        // Start recording
        await _startRecording();
      }
    } catch (e) {
      _showSnackBar('Error with audio recording: $e', isError: true);
    }
  }

  Future<void> _startRecording() async {
    // Request microphone permission
    final permission = await Permission.microphone.request();
    if (permission != PermissionStatus.granted) {
      _showSnackBar('Microphone permission is required for audio recording', isError: true);
      return;
    }
    
    try {
      final directory = await getTemporaryDirectory();
      final filePath = '${directory.path}/audio_${DateTime.now().millisecondsSinceEpoch}.aac';
      
      await _audioRecorder!.startRecorder(
        toFile: filePath,
        codec: Codec.aacADTS,
      );
      
      setState(() {
        _isRecording = true;
        _recordingPath = filePath;
      });
      
      // Start pulsing animation
      _animationController.repeat(reverse: true);
      
    } catch (e) {
      _showSnackBar('Failed to start recording: $e', isError: true);
    }
  }

  Future<void> _stopRecording() async {
    try {
      final path = await _audioRecorder!.stopRecorder();
      
      setState(() {
        _isRecording = false;
      });
      
      // Stop pulsing animation
      _animationController.stop();
      _animationController.reset();
      
      if (path != null && File(path).existsSync()) {
        // Show confirmation dialog
        final bool? shouldSend = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Send Audio'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Audio recorded successfully!'),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton.icon(
                      onPressed: () async {
                        await _playAudio(path);
                      },
                      icon: Icon(_isPlaying ? Icons.stop : Icons.play_arrow),
                      label: Text(_isPlaying ? 'Stop' : 'Play'),
                    ),
                  ],
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  File(path).deleteSync(); // Delete the file
                  Navigator.of(context).pop(false);
                },
                child: const Text('Delete'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Send'),
              ),
            ],
          ),
        );
        
        if (shouldSend == true) {
          await _sendAudioMessage(File(path));
        } else {
          // Delete the file if not sending
          File(path).deleteSync();
        }
      }
    } catch (e) {
      setState(() {
        _isRecording = false;
      });
      
      // Stop pulsing animation on error
      _animationController.stop();
      _animationController.reset();
      
      _showSnackBar('Failed to stop recording: $e', isError: true);
    }
  }

  Future<void> _playAudio(String path) async {
    try {
      if (_isPlaying) {
        await _audioPlayer!.stopPlayer();
        setState(() {
          _isPlaying = false;
        });
      } else {
        await _audioPlayer!.startPlayer(
          fromURI: path,
          whenFinished: () {
            setState(() {
              _isPlaying = false;
            });
          },
        );
        setState(() {
          _isPlaying = true;
        });
      }
    } catch (e) {
      _showSnackBar('Error playing audio: $e', isError: true);
    }
  }

  Future<void> _sendFileMessage(File file, String fileName) async {
    try {
      // Show loading indicator
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Uploading file...')),
      );
      
      // First upload the file
      final uploadResponse = await _chatService.uploadFile(
        conversationId: widget.conversationId,
        senderId: _currentUser!.id!,
        file: file,
        fileType: 'file',
      );
      
      if (!uploadResponse['success']) {
        _showSnackBar('Failed to upload file: ${uploadResponse['message']}', isError: true);
        return;
      }
      
      // Then send the file message with metadata
      final response = await _chatService.sendFileMessage(
        conversationId: widget.conversationId,
        senderId: _currentUser!.id!,
        receiverId: widget.otherUserId,
        fileUrl: uploadResponse['file_url'],
        fileMetadata: uploadResponse['file_metadata'],
        messageType: 'file',
        listingTitle: _conversationDetails?.listingTitle ?? widget.listingTitle,
        conversationListerId: _conversationDetails?.listerId,
        conversationDoerId: _conversationDetails?.doerId,
        applicationId: widget.applicationId,
      );
      
      if (response['success']) {
        _showSnackBar('File sent successfully!', isError: false);
        await _fetchMessages(); // Refresh messages
      } else {
        _showSnackBar('Failed to send file: ${response['message']}', isError: true);
      }
    } catch (e) {
      _showSnackBar('Error sending file: $e', isError: true);
    }
  }

  Future<void> _sendAudioMessage(File audioFile) async {
    try {
      // Show loading indicator
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Uploading audio...')),
      );
      
      // First upload the audio file
      final uploadResponse = await _chatService.uploadFile(
        conversationId: widget.conversationId,
        senderId: _currentUser!.id!,
        file: audioFile,
        fileType: 'audio',
      );
      
      if (!uploadResponse['success']) {
        _showSnackBar('Failed to upload audio: ${uploadResponse['message']}', isError: true);
        return;
      }
      
      // Then send the audio message with metadata
      final response = await _chatService.sendFileMessage(
        conversationId: widget.conversationId,
        senderId: _currentUser!.id!,
        receiverId: widget.otherUserId,
        fileUrl: uploadResponse['file_url'],
        fileMetadata: uploadResponse['file_metadata'],
        messageType: 'audio',
        listingTitle: _conversationDetails?.listingTitle ?? widget.listingTitle,
        conversationListerId: _conversationDetails?.listerId,
        conversationDoerId: _conversationDetails?.doerId,
        applicationId: widget.applicationId,
      );
      
      if (response['success']) {
        _showSnackBar('Audio sent successfully!', isError: false);
        await _fetchMessages(); // Refresh messages
      } else {
        _showSnackBar('Failed to send audio: ${response['message']}', isError: true);
      }
    } catch (e) {
      _showSnackBar('Error sending audio: $e', isError: true);
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  // Add method to handle audio playback toggle for messages
  Future<void> _toggleAudioPlayback(String fileUrl) async {
    try {
      if (_isPlayingAudio && _currentPlayingUrl == fileUrl) {
        // Stop current audio
        await _audioPlayerForMessages?.stop();
        setState(() {
          _isPlayingAudio = false;
          _currentPlayingUrl = null;
        });
      } else {
        // Stop any currently playing audio first
        if (_isPlayingAudio) {
          await _audioPlayerForMessages?.stop();
        }
        
        // Start playing new audio
        String fullUrl;
        if (fileUrl.startsWith('http://') || fileUrl.startsWith('https://')) {
          fullUrl = fileUrl;
        } else if (fileUrl.startsWith('uploads/')) {
          fullUrl = '${ApiConfig.baseUrl}/$fileUrl';
        } else {
          fullUrl = '${ApiConfig.baseUrl}/$fileUrl';
        }
        
        await _audioPlayerForMessages?.play(UrlSource(fullUrl));
        
        setState(() {
          _isPlayingAudio = true;
          _currentPlayingUrl = fileUrl;
        });
        
        // Listen for completion
        _audioPlayerForMessages?.onPlayerComplete.listen((_) {
          if (mounted) {
            setState(() {
              _isPlayingAudio = false;
              _currentPlayingUrl = null;
            });
          }
        });
      }
    } catch (e) {
      _showSnackBar('Error playing audio: $e', isError: true);
      setState(() {
        _isPlayingAudio = false;
        _currentPlayingUrl = null;
      });
    }
  }

@override
Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: Constants.primaryColor,
          foregroundColor: Colors.white,
          title: const Text('Loading Chat...'),
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_errorMessage != null) {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: Constants.primaryColor,
          foregroundColor: Colors.white,
          title: const Text('Chat Error'),
        ),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.red, fontSize: 16),
            ),
          ),
        ),
      );
    }

    final String otherUserName = _conversationDetails?.getOtherUserFullName(_currentUser?.id ?? 0) ?? 'Unknown User';
    final String? otherUserLocation = _conversationDetails?.getOtherUserAddress(_currentUser?.id ?? 0);
    final String? otherUserProfilePic = _conversationDetails?.getOtherUserProfilePictureUrl(_currentUser?.id ?? 0);
    final String? currentUserProfilePic = _currentUser?.profilePictureUrl;


    String? currentApplicationStatus = _conversationDetails?.applicationStatus;
    String statusDisplay = currentApplicationStatus?.toUpperCase().replaceAll('_', ' ') ?? 'N/A';
    Color statusColor = Colors.grey;

    if (currentApplicationStatus == 'pending') {
      statusColor = Colors.orange;
    } else if (currentApplicationStatus == 'accepted') {
      statusColor = Colors.blue;
    } else if (currentApplicationStatus == 'in_progress') {
      statusDisplay = 'ONGOING';
      statusColor = Colors.green;
    } else if (currentApplicationStatus == 'completed') {
      statusColor = Colors.teal;
    } else if (currentApplicationStatus == 'rejected' || currentApplicationStatus == 'cancelled') {
      statusColor = Colors.red;
    }

    print('Current application status: [32m${_conversationDetails?.applicationStatus}[0m');
    print('hasReview: [32m$_hasReview[0m, currentUser: [32m${_currentUser?.id}[0m, listerId: [32m${_conversationDetails?.listerId}[0m');
    print('DEBUG: applicationId: ${widget.applicationId}');

    // Debug prints for Leave a Review button logic
    print('DEBUG: Checking Leave a Review button conditions:');
    print('  - _hasReview: $_hasReview');
    print('  - _currentUser?.id: ${_currentUser?.id}');
    print('  - _conversationDetails?.listerId: ${_conversationDetails?.listerId}');
    print('  - currentApplicationStatus: $currentApplicationStatus');
    print('  - Should show button: ${!_hasReview && _currentUser?.id == _conversationDetails?.listerId}');

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Constants.primaryColor,
        foregroundColor: Colors.white,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    otherUserName,
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 4),
                // Display status in app bar
                if (currentApplicationStatus != null && currentApplicationStatus.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                    decoration: BoxDecoration(
                      color: statusColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      statusDisplay,
                      style: const TextStyle(fontSize: 10, color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                  ),
              ],
            ),
            if (otherUserLocation != null && otherUserLocation.isNotEmpty)
              Text(
                otherUserLocation,
                style: const TextStyle(fontSize: 12, color: Colors.white70),
                overflow: TextOverflow.ellipsis,
              ),
          ],
        ),
        actions: [
          // Refresh button
          IconButton(
            icon: _isLoading
                ? const SizedBox(
                    width: 18,
                    height: 18,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.refresh, color: Colors.white, size: 20),
            onPressed: _isLoading ? null : () async {
              setState(() { _isLoading = true; });
              try {
                await _fetchConversationDetails();
                await _fetchMessages();
              } catch (e) {
                _showSnackBar('Failed to refresh: $e', isError: true);
              } finally {
                setState(() { _isLoading = false; });
              }
            },
            tooltip: 'Refresh Chat',
            padding: const EdgeInsets.all(8),
            constraints: const BoxConstraints(minWidth: 36, minHeight: 36),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'report') {
                _reportUser();
              } else if (value == 'block') {
                _toggleBlockUser();
              }
            },
            itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
              const PopupMenuItem<String>(
                value: 'report',
                child: Text('Report User'),
              ),
              PopupMenuItem<String>(
                value: 'block',
                child: Text(_isBlockedByCurrentUser ? 'Unblock User' : 'Block User'), // Dynamic text
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _conversationDetails?.listingTitle ?? widget.listingTitle,
                  style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Constants.textColor),
                ),
                const SizedBox(height: 10),
                // Conditional buttons for Lister and Doer
                // Lister's initial buttons (Reject/Start) - visible for pending/accepted or ASAP listings
                if (widget.isLister &&
                    ((widget.applicationId != null && (currentApplicationStatus == 'pending' || currentApplicationStatus == 'accepted')) ||
                     (_conversationDetails?.listingType == 'ASAP' && widget.applicationId == null)) &&
                    !(currentApplicationStatus == 'completed' && _hasReview))
                  Row(
                    children: [
                      // Only show Reject button if there's an application
                      if (widget.applicationId != null)
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _handleRejectApplication,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                            child: _isLoading
                                ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2))
                                : const Text('Reject', style: TextStyle(fontSize: 16)),
                          ),
                        ),
                      if (widget.applicationId != null)
                        const SizedBox(width: 10),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _handleStartProject,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Constants.primaryColor,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                          child: _isLoading
                              ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2))
                              : const Text('Start', style: TextStyle(fontSize: 16)),
                        ),
                      ),
                    ],
                  ),
                // Lister's "View Current Location" button (when in_progress and not yet marked done by doer)
                // This button should *not* show if there's a pending 'doer_marked_complete_request'
                if (widget.applicationId != null && widget.isLister &&
                    currentApplicationStatus == 'in_progress' &&
                    !_messages.any((msg) => msg.type == 'doer_marked_complete_request' && msg.senderId == widget.otherUserId))
                  Align(
                    alignment: Alignment.center,
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : () {
                        final lastLocationMessage = _messages.lastWhere(
                              (msg) => msg.type == 'location_share' && msg.senderId == widget.otherUserId,
                          orElse: () => Message(id: -1, conversationId: -1, senderId: -1, receiverId: -1, content: '', sentAt: DateTime.now(), type: 'none'),
                        );
                        if (lastLocationMessage.id != -1 && lastLocationMessage.content.isNotEmpty) {
                          _viewCurrentLocation(lastLocationMessage.content);
                        } else {
                          _showSnackBar('No location shared by Doer yet.', isError: true);
                        }
                      },
                      icon: const Icon(Icons.location_on),
                      label: const Text('View Current Location', style: TextStyle(fontSize: 16)),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blueAccent,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
                      ),
                    ),
                  ),

                // Doer's "Share Location" and "Mark as Done" buttons (when in_progress)
                // This button should *not* show if the doer has already sent a 'doer_marked_complete_request'
                if (widget.applicationId != null && !widget.isLister &&
                    currentApplicationStatus == 'in_progress' &&
                    !_messages.any((msg) => msg.type == 'doer_marked_complete_request' && msg.senderId == _currentUser!.id))
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _isLoading ? null : _shareCurrentLocation,
                          icon: const Icon(Icons.share_location),
                          label: const Text('Share Location', style: TextStyle(fontSize: 16)),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Constants.primaryColor,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: AnimatedBuilder(
                          animation: _wiggleAnimation,
                          builder: (context, child) {
                            return Transform.translate(
                              offset: Offset(_wiggleAnimation.value * 10.0, 0), // 10 pixels left-right wiggle
                              child: ElevatedButton.icon(
                                onPressed: _isLoading ? null : _handleMarkAsDone,
                                icon: const Icon(Icons.check_circle),
                                label: const Text('Mark as Done', style: TextStyle(fontSize: 16)),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.teal,
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                // Display final status text if no active buttons are shown
                if (currentApplicationStatus != null && (currentApplicationStatus == 'completed' || currentApplicationStatus == 'rejected' || currentApplicationStatus == 'cancelled'))
                  Align(
                    alignment: Alignment.center,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 10.0),
                      child: Text(
                        'Project ${statusDisplay}',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: statusColor),
                      ),
                    ),
                  ),
                

                // Review button for completed jobs (simplified - no payment dependency)
                if (!_hasReview &&
                    _currentUser?.id == _conversationDetails?.listerId &&
                    currentApplicationStatus == 'completed' &&
                    widget.isLister) ...[
                  Center(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).push(MaterialPageRoute(
                            builder: (context) => ReviewScreen(
                              reviewerId: _currentUser!.id!,
                              reviewedUserId: _conversationDetails!.doerId,
                              listingId: widget.applicationId,
                              listingTitle: _conversationDetails!.listingTitle,
                            ),
                          )).then((_) => _checkIfHasReview());
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Constants.primaryColor,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                          padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 12),
                        ),
                        child: const Text('Leave a Review', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
          const Divider(height: 1, color: Colors.grey),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                try {
                  await _fetchConversationDetails();
                  await _fetchMessages();
                } catch (e) {
                  _showSnackBar('Failed to refresh: $e', isError: true);
                }
              },
              color: Constants.primaryColor,
              child: ListView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                itemCount: _messages.length,
                itemBuilder: (context, index) {
                  final message = _messages[index];
                  final bool isMe = message.senderId == _currentUser?.id;
                  final bool isSystemMessage = message.type == 'system';
                  final bool isLocationMessage = message.type == 'location_share';
                  final bool isDoerMarkedCompleteRequest = message.type == 'doer_marked_complete_request';

                  if (isSystemMessage) {
                    // Check if this is a "marked as done" system message
                    final bool isDoerMarkedDoneMessage = message.content.contains("marked the project as done");
                    
                    if (isDoerMarkedDoneMessage && widget.isLister && currentApplicationStatus == 'in_progress' && !_hasReview) {
                      // Display system message with confirmation button for lister
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Column(
                          children: [
                            // System message display
                            Center(
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade300,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Text(
                                  message.content,
                                  style: const TextStyle(fontSize: 13, color: Colors.black87),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                            // Confirmation button
                            const SizedBox(height: 8),
                            ElevatedButton(
                              onPressed: _isLoading ? null : _handleConfirmCompletion,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                                foregroundColor: Colors.white,
                              ),
                              child: const Text('Confirm Completion'),
                            ),
                          ],
                        ),
                      );
                    }
                    
                    // Regular system message display
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Center(
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade300,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            message.content,
                            style: const TextStyle(fontSize: 13, color: Colors.black87),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    );
                  } else if (isLocationMessage) {
                    return MessageBubble(
                    message: message,
                    isMe: isMe,
                    otherUserProfilePic: otherUserProfilePic,
                    currentUserProfilePic: currentUserProfilePic,
                    onLocationTap: (latLngString) {
                      _viewCurrentLocation(latLngString);
                    },
                    onAudioToggle: _toggleAudioPlayback,
                    isPlayingAudio: _isPlayingAudio,
                    currentPlayingUrl: _currentPlayingUrl,
                    conversationDetails: _conversationDetails,
                  );
                  } else if (isDoerMarkedCompleteRequest) {
  // NEW: Render special UI for Doer Marked Complete Request on Lister side
  if (widget.isLister &&
      currentApplicationStatus == 'in_progress' &&
      !_hasReview) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Container(
        decoration: BoxDecoration(
          color: Constants.primaryColor, // Primary color background (blue)
          borderRadius: BorderRadius.circular(20),
        ),
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              message.content,
              style: const TextStyle(color: Colors.white, fontSize: 13),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),
            // "Confirm" Button with yellow design and constant wiggle animation
            AnimatedBuilder(
              animation: _wiggleAnimation,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(_wiggleAnimation.value * 10, 0),
                  child: SizedBox(
                    width: 120,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : () async {
                        // Show styled confirmation popup with wiggle on Confirm button
                        final bool? confirmed = await showDialog<bool>(
                          context: context,
                          builder: (BuildContext context) {
                            // Start wiggle animation for popup
                            _wiggleAnimationController.repeat(reverse: true);
                            return AlertDialog(
                              backgroundColor: Constants.primaryColor, // Blue background
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                              title: const Text('Confirm Job Completion', style: TextStyle(color: Colors.white)),
                              content: const Text('Are you sure you want to mark this job as completed?', style: TextStyle(color: Colors.white)),
                              actions: [
                                TextButton(
                                  onPressed: () {
                                    _wiggleAnimationController.stop();
                                    _wiggleAnimationController.reset();
                                    Navigator.of(context).pop(false);
                                  },
                                  child: const Text('Cancel', style: TextStyle(color: Colors.white)),
                                ),
                                AnimatedBuilder(
                                  animation: _wiggleAnimation,
                                  builder: (context, child) {
                                    return Transform.translate(
                                      offset: Offset(_wiggleAnimation.value * 10, 0), // Left-right wiggle
                                      child: ElevatedButton(
                                        onPressed: () {
                                          _wiggleAnimationController.stop();
                                          _wiggleAnimationController.reset();
                                          Navigator.of(context).pop(true);
                                        },
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.yellow, // Yellow button
                                          foregroundColor: Colors.black, // Black text
                                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(25)),
                                        ),
                                        child: const Text('Confirm'),
                                      ),
                                    );
                                  },
                                ),
                              ],
                            );
                          },
                        );
                        if (confirmed != true) {
                          _wiggleAnimationController.repeat(reverse: true);
                        }
                        if (confirmed == true) {
                          setState(() => _isLoading = true);
                          try {
                            // Update application status to completed
                            final response = await _applicationService.updateApplicationStatus(
                              applicationId: widget.applicationId,
                              newStatus: 'completed',
                              currentUserId: _currentUser!.id!,
                            );
                            if (response['success']) {
                              // Send system message
                              await _chatService.sendMessage(
                                conversationId: widget.conversationId,
                                senderId: _currentUser!.id!,
                                receiverId: widget.otherUserId,
                                messageContent: 'Job marked as completed by Lister.',
                                messageType: 'system',
                              );
                              // Refresh messages and status
                              await _fetchMessages();
                              await _fetchConversationDetails();
                            } else {
                              _showSnackBar('Failed to complete job: ${response['message']}', isError: true);
                            }
                          } catch (e) {
                            _showSnackBar('Failed to complete job: $e', isError: true);
                          } finally {
                            setState(() => _isLoading = false);
                          }
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.yellow, // Yellow button
                        foregroundColor: Colors.black, // Black text
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                        elevation: 3,
                        shadowColor: Colors.black26,
                      ),
                      child: _isLoading
                          ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(color: Colors.black, strokeWidth: 2))
                          : const Text('Confirm', style: TextStyle(fontSize: 16)),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

                  return MessageBubble(
                    message: message,
                    isMe: isMe,
                    otherUserProfilePic: otherUserProfilePic,
                    currentUserProfilePic: currentUserProfilePic,
                    onLocationTap: (latLngString) {
                      _viewCurrentLocation(latLngString);
                    },
                    onAudioToggle: _toggleAudioPlayback,
                    isPlayingAudio: _isPlayingAudio,
                    currentPlayingUrl: _currentPlayingUrl,
                    conversationDetails: _conversationDetails,
                  );
                },
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 2,
                  blurRadius: 5,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                children: [
                  IconButton(
                    onPressed: _isBlockedByCurrentUser ? null : _pickFile,
                    icon: Icon(
                      Icons.attach_file,
                      color: _isBlockedByCurrentUser ? Colors.grey : Constants.primaryColor,
                    ),
                    tooltip: 'Attach file',
                  ),
                  IconButton(
                    onPressed: _isBlockedByCurrentUser ? null : _pickAudio,
                    icon: _isRecording 
                        ? AnimatedBuilder(
                            animation: _animationController,
                            builder: (context, child) {
                              return Transform.scale(
                                scale: 1.0 + (_animationController.value * 0.3),
                                child: Icon(
                                  Icons.stop,
                                  color: _isBlockedByCurrentUser 
                                      ? Colors.grey 
                                      : Colors.red,
                                ),
                              );
                            },
                          )
                        : Icon(
                            Icons.mic,
                            color: _isBlockedByCurrentUser 
                                ? Colors.grey 
                                : Constants.primaryColor,
                          ),
                    tooltip: _isRecording ? 'Stop recording' : 'Record audio',
                  ),
                  const SizedBox(width: 8.0),
                  Expanded(
                    child: TextField(
                      controller: _messageController,
                      decoration: InputDecoration(
                        hintText: _isBlockedByCurrentUser ? 'You have blocked this user.' : 'Type a message...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(20.0),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: Colors.grey.shade200,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10.0),
                      ),
                      enabled: !_isBlockedByCurrentUser, // Disable if user is blocked
                    ),
                  ),
                  const SizedBox(width: 8.0),
                  FloatingActionButton(
                    onPressed: _isBlockedByCurrentUser ? null : _sendMessage,
                    backgroundColor: _isBlockedByCurrentUser ? Colors.grey : Constants.primaryColor,
                    foregroundColor: Colors.white,
                    elevation: 0,
                    child: const Icon(Icons.send),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Navigate to payment summary screen with listing payment details
  Future<bool?> _navigateToPaymentSummary() async {
    try {
      // Use the dedicated listing payment details endpoint that handles both ASAP and PUBLIC listings
      final apiEndpoint = '${ApiConfig.baseUrl}/api/job_payment/get_listing_payment_details.php?listing_id=${_conversationDetails?.listingId}';

      print('Payment Summary Debug: Listing Type = ${_conversationDetails?.listingType}');
      print('Payment Summary Debug: Listing ID = ${_conversationDetails?.listingId}');
      print('Payment Summary Debug: API Endpoint = $apiEndpoint');

      final response = await http.get(
        Uri.parse(apiEndpoint),
        headers: {'Content-Type': 'application/json'},
      );

      print('Payment Summary Debug: Response Status = ${response.statusCode}');
      print('Payment Summary Debug: Response Body = ${response.body}');

      if (response.statusCode == 200) {
        final result = jsonDecode(response.body);
        if (result['success'] && result['listing'] != null) {
          final listing = result['listing'];

          // Extract payment details from the listing
          final double doerFee = (listing['doer_fee'] ?? 0.0).toDouble();
          final double totalAmount = (listing['total_amount'] ?? 0.0).toDouble();
          final String paymentMethod = listing['payment_method'] ?? '';
          final String listingTitle = _conversationDetails?.listingTitle ?? widget.listingTitle; // Use the title from conversation details

          // Get application ID for payment processing
          String applicationId = '';
          if (_conversationDetails?.applicationId != null) {
            applicationId = _conversationDetails!.applicationId.toString();
          }

          if (mounted) {
            final result = await Navigator.of(context).push<bool>(
              MaterialPageRoute(
                builder: (context) => PaymentSummaryScreen(
                  doerFee: doerFee,
                  applicationId: applicationId,
                  listingTitle: listingTitle,
                ),
              ),
            );
            return result;
          }
        } else {
          _showSnackBar('Failed to load payment details: ${result['message'] ?? 'Unknown error'}', isError: true);
        }
      } else {
        _showSnackBar('Failed to connect to server', isError: true);
      }
    } catch (e) {
      print('Error navigating to payment summary: $e');
      _showSnackBar('Error loading payment details: $e', isError: true);
    }
    return null;
  }

  /// Start monitoring application status changes for payment completion
  void _startStatusMonitoring() {
    // Only start monitoring if we're the lister and status is not yet in_progress
    if (widget.isLister && _conversationDetails?.applicationStatus != 'in_progress') {
      _statusCheckTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
        _checkForStatusChange();
      });
    }
  }

  /// Check if application status has changed to in_progress
  Future<void> _checkForStatusChange() async {
    try {
      await _fetchConversationDetails();

      final currentStatus = _conversationDetails?.applicationStatus;

      // If status changed from non-in_progress to in_progress, refresh messages
      if (_lastKnownStatus != null &&
          _lastKnownStatus != 'in_progress' &&
          currentStatus == 'in_progress') {

        debugPrint('Status changed to in_progress - refreshing messages');
        await _fetchMessages();

        // Stop monitoring once project has started
        _statusCheckTimer?.cancel();
        _statusCheckTimer = null;
      }

      _lastKnownStatus = currentStatus;

    } catch (e) {
      debugPrint('Error checking status change: $e');
    }
  }
}

// MessageBubble is updated to pass onLocationTap
class MessageBubble extends StatelessWidget {
  final Message message;
  final bool isMe;
  final String? otherUserProfilePic;
  final String? currentUserProfilePic;
  final Function(String)? onLocationTap;
  final Function(String)? onAudioToggle; // New callback for audio playback
  final bool isPlayingAudio; // New parameter for audio state
  final String? currentPlayingUrl; // New parameter for current playing URL
  final Conversation? conversationDetails; // Add conversation details

  const MessageBubble({
    super.key,
    required this.message,
    required this.isMe,
    this.otherUserProfilePic,
    this.currentUserProfilePic,
    this.onLocationTap,
    this.onAudioToggle, // Add this
    this.isPlayingAudio = false, // Add this
    this.currentPlayingUrl, // Add this
    this.conversationDetails, // Add this
  });

  // Add this method to the MessageBubble class
  String _getFileType(String fileName) {
    final extension = fileName.toLowerCase().split('.').last;
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension)) {
      return 'image';
    } else if (['mp4', 'mov', 'avi', 'mkv', 'wmv', 'flv', 'webm'].contains(extension)) {
      return 'video';
    } else if (['pdf'].contains(extension)) {
      return 'pdf';
    } else {
      return 'other';
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  Future<void> _downloadOrOpenFile(BuildContext context, String fileUrl, String fileName, bool isAudio) async {
    try {
      // Add debugging to see what fileUrl contains
      print('DEBUG: fileUrl from database: "$fileUrl"');
      print('DEBUG: ApiConfig.baseUrl: "${ApiConfig.baseUrl}"');
      
      if (fileUrl.isEmpty) {
        print('Error: fileUrl is empty for message ${message.id}');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('File URL is missing. Please contact support.'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
      
      // Smart URL construction
      String fullUrl;
      if (fileUrl.startsWith('http://') || fileUrl.startsWith('https://')) {
        // fileUrl is already a complete URL
        fullUrl = fileUrl;
      } else if (fileUrl.startsWith('uploads/')) {
        // fileUrl is a relative path, construct full URL with /api
        fullUrl = '${ApiConfig.baseUrl}/$fileUrl';
      } else {
        // Fallback for other cases
        fullUrl = '${ApiConfig.baseUrl}/$fileUrl';
      }
      
      print('Final fullUrl: $fullUrl');
      
      if (isAudio) {
        // For audio files, play them
        await _playAudioFromUrl(fullUrl);
      } else {
        // For other files, open them in browser or download
        if (await canLaunchUrl(Uri.parse(fullUrl))) {
          await launchUrl(Uri.parse(fullUrl), mode: LaunchMode.externalApplication);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Cannot open file: $fileName')),
          );
        }
      }
    } catch (e) {
      print('DEBUG: Error in _downloadOrOpenFile: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error opening file: $e')),
      );
    }
  }

  Future<void> _playAudioFromUrl(String fullUrl) async {
    try {
      final player = AudioPlayer();
      await player.play(UrlSource(fullUrl));
    } catch (e) {
      print('Error playing audio: $e');
    }
  }

Widget _buildFileMessage(BuildContext context) {
  Map<String, dynamic>? fileMetadata;
  
  try {
    if (message.extraData != null) {
      // Try to parse extraData if it's a string
      if (message.extraData is String) {
        fileMetadata = json.decode(message.extraData as String);
      } else if (message.extraData is Map<String, dynamic>) {
        fileMetadata = message.extraData as Map<String, dynamic>;
      }
    }
  } catch (e) {
    print('Error parsing file metadata: $e');
  }
  
  // Get filename from metadata or extract from URL as fallback
  String fileName = fileMetadata?['original_name'] ?? 'Unknown file';
  if (fileName == 'Unknown file' && message.fileUrl != null && message.fileUrl!.isNotEmpty) {
    // Extract filename from URL as fallback
    final urlParts = message.fileUrl!.split('/');
    if (urlParts.isNotEmpty) {
      final fileNameWithId = urlParts.last;
      // Try to extract original extension
      final parts = fileNameWithId.split('.');
      if (parts.length > 1) {
        fileName = 'file.${parts.last}'; // Use generic name with correct extension
      }
    }
  }
  
  final fileSize = fileMetadata?['file_size'] ?? 0;
  final fileUrl = message.fileUrl ?? '';
  final isAudio = message.type == 'audio';
  final fileType = _getFileType(fileName);
  
  // Add debug prints
  print('DEBUG: fileName: $fileName');
  print('DEBUG: message.type: ${message.type}');
  print('DEBUG: isAudio: $isAudio');
  print('DEBUG: fileType: $fileType');
  print('DEBUG: fileUrl: $fileUrl');
  print('DEBUG: extraData: ${message.extraData}');
  
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 4.0),
    child: Row(
      mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        if (!isMe) ...[
          CircleAvatar(
            radius: 18,
            backgroundImage: ImageUtils.createProfileImageProvider(otherUserProfilePic) ?? const AssetImage('assets/11.png'),
            child: null,
            backgroundColor: Colors.grey.shade200,
          ),
          const SizedBox(width: 8),
        ],
        Flexible(
          child: Container(
            decoration: BoxDecoration(
              color: isMe ? Constants.primaryColor : Colors.grey.shade300,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(isMe ? 15 : 0),
                topRight: Radius.circular(isMe ? 0 : 15),
                bottomLeft: const Radius.circular(15),
                bottomRight: const Radius.circular(15),
              ),
            ),
            padding: const EdgeInsets.all(12.0),
            margin: EdgeInsets.only(
              left: isMe ? 50.0 : 0,
              right: isMe ? 0 : 50.0,
            ),
            child: Column(
              crossAxisAlignment: isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                if (isAudio) ...[
                  // Audio bubble with note icon and play/pause button
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.music_note,
                        color: isMe ? Colors.white : Colors.black87,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton.icon(
                        onPressed: onAudioToggle != null ? () => onAudioToggle!(fileUrl) : null,
                        icon: AnimatedSwitcher(
                          duration: const Duration(milliseconds: 300),
                          child: Icon(
                            (isPlayingAudio && currentPlayingUrl == fileUrl) 
                                ? Icons.pause 
                                : Icons.play_arrow,
                            key: ValueKey((isPlayingAudio && currentPlayingUrl == fileUrl) 
                                ? 'pause' 
                                : 'play'),
                          ),
                        ),
                        label: Text((isPlayingAudio && currentPlayingUrl == fileUrl) 
                            ? 'Pause' 
                            : 'Play'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: isMe ? Colors.white : Constants.primaryColor,
                          foregroundColor: isMe ? Constants.primaryColor : Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        ),
                      ),
                    ],
                  ),
                ] else if (fileType == 'image') ...[
                  // Display image directly in chat bubble
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      fileUrl.startsWith('http') ? fileUrl : '${ApiConfig.baseUrl}/$fileUrl',
                      width: 200,
                      height: 200,
                      fit: BoxFit.cover,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Container(
                          width: 200,
                          height: 200,
                          color: Colors.grey.shade200,
                          child: const Center(
                            child: CircularProgressIndicator(),
                          ),
                        );
                      },
                      errorBuilder: (context, error, stackTrace) {
                        print('Image load error: $error');
                        return Container(
                          width: 200,
                          height: 200,
                          color: Colors.grey.shade200,
                          child: const Icon(Icons.broken_image, size: 50),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    fileName,
                    style: TextStyle(
                      color: isMe ? Colors.white70 : Colors.black54,
                      fontSize: 12,
                    ),
                  ),
                ] else if (fileType == 'video') ...[
                  // Inline video player
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: InlineVideoPlayer(
                      video: Video(
                        id: 0,
                        title: fileName,
                        description: 'Chat video',
                        videoUrl: fileUrl.startsWith('http') ? fileUrl : '${ApiConfig.baseUrl}/$fileUrl',
                        category: 'chat',
                        isActive: true,
                        createdAt: DateTime.now(),
                        updatedAt: DateTime.now(),
                      ),
                      autoplay: false,
                      height: 200,
                      width: 250,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    fileName,
                    style: TextStyle(
                      color: isMe ? Colors.white70 : Colors.black54,
                      fontSize: 12,
                    ),
                  ),
                ] else if (fileType == 'pdf') ...[
                  // PDF file display - compact format without size
                  Row(
                    children: [
                      Icon(
                        Icons.picture_as_pdf,
                        color: isMe ? Colors.white : Colors.red,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          fileName,
                          style: TextStyle(
                            color: isMe ? Colors.white : Colors.black87,
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      IconButton(
                        onPressed: () => _downloadOrOpenFile(context, fileUrl, fileName, false),
                        icon: Icon(
                          Icons.download,
                          color: isMe ? Colors.white : Constants.primaryColor,
                          size: 20,
                        ),
                        constraints: const BoxConstraints(),
                        padding: EdgeInsets.zero,
                      ),
                    ],
                  ),
                ] else ...[
                  // Other file types - compact format without size
                  Row(
                    children: [
                      Icon(
                        Icons.insert_drive_file,
                        color: isMe ? Colors.white : Colors.black87,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          fileName,
                          style: TextStyle(
                            color: isMe ? Colors.white : Colors.black87,
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      IconButton(
                        onPressed: () => _downloadOrOpenFile(context, fileUrl, fileName, false),
                        icon: Icon(
                          Icons.download,
                          color: isMe ? Colors.white : Constants.primaryColor,
                          size: 20,
                        ),
                        constraints: const BoxConstraints(),
                        padding: EdgeInsets.zero,
                      ),
                    ],
                  ),
                ],
                const SizedBox(height: 4),
                Text(
                  _formatMessageTime(message.sentAt),
                  style: TextStyle(
                    color: isMe ? Colors.white70 : Colors.black54,
                    fontSize: 10.0,
                  ),
                ),
              ],
            ),
          ),
        ),
        if (isMe) ...[
          const SizedBox(width: 8),
          CircleAvatar(
            radius: 18,
            backgroundImage: ImageUtils.createProfileImageProvider(currentUserProfilePic) ?? const AssetImage('assets/11.png'),
            child: null,
            backgroundColor: Colors.grey.shade200,
          ),
        ],
      ],
    ),
  );
}

  @override
  Widget build(BuildContext context) {
    final String? profilePicUrl = isMe ? currentUserProfilePic : otherUserProfilePic;
    final bool isLocationMessage = message.type == 'location_share';
    final bool isFileMessage = message.type == 'file' || message.type == 'audio';

    // Handle file and audio messages
    if (isFileMessage) {
      return _buildFileMessage(context);
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isMe) ...[
            CircleAvatar(
              radius: 18,
              backgroundImage: ImageUtils.createProfileImageProvider(profilePicUrl) ?? const AssetImage('assets/11.png'),
              child: null,
              backgroundColor: Colors.grey.shade200,
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: GestureDetector(
              onTap: isLocationMessage && onLocationTap != null && message.extraData != null
                  ? () => onLocationTap!(message.content)
                  : null,
              child: Container(
                decoration: BoxDecoration(
                  color: isMe ? Constants.primaryColor : Colors.grey.shade300,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(isMe ? 15 : 0),
                    topRight: Radius.circular(isMe ? 0 : 15),
                    bottomLeft: const Radius.circular(15),
                    bottomRight: const Radius.circular(15),
                  ),
                ),
                padding: const EdgeInsets.all(12.0),
                margin: EdgeInsets.only(
                  left: isMe ? 50.0 : 0,
                  right: isMe ? 0 : 50.0,
                ),
                child: Column(
                  crossAxisAlignment: isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                  children: [
                    if (isLocationMessage && message.extraData != null) ...[
  MessageBubbleMap(
    message: message,
    conversationDetails: conversationDetails,
    isMe: isMe,
  ),
] else
  Text(
    message.content,
    style: TextStyle(
      color: isMe ? Colors.white : Colors.black87,
      fontSize: 16.0,
    ),
  ),
const SizedBox(height: 4.0),
Text(
  _formatMessageTime(message.sentAt),
  style: TextStyle(
    color: isMe ? Colors.white70 : Colors.black54,
    fontSize: 10.0,
  ),
),
                  ],
                ),
              ),
            ),
          ),
          if (isMe) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 18,
              backgroundImage: ImageUtils.createProfileImageProvider(profilePicUrl) ?? const AssetImage('assets/11.png'),
              child: null,
              backgroundColor: Colors.grey.shade200,
            ),
          ],
        ],
      ),
    );
  }

  String _formatMessageTime(DateTime sentAt) {
    // Display as-is, since backend sends +08:00
    final philippinesTime = sentAt;

    int hour = philippinesTime.hour;
    String period = 'AM';

    if (hour >= 12) {
      period = 'PM';
      if (hour > 12) {
        hour = hour - 12;
      }
    }
    if (hour == 0) {
      hour = 12;
    }

    return '${hour.toString()}:${philippinesTime.minute.toString().padLeft(2, '0')} $period';
  }

}

// New widget for message bubble map with route calculation
class MessageBubbleMap extends StatefulWidget {
  final Message message;
  final Conversation? conversationDetails;
  final bool isMe;

  const MessageBubbleMap({
    super.key,
    required this.message,
    required this.conversationDetails,
    required this.isMe,
  });

  @override
  State<MessageBubbleMap> createState() => _MessageBubbleMapState();
}

class _MessageBubbleMapState extends State<MessageBubbleMap> {
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {};
  Position? _currentPosition;
  String _travelTime = 'Calculating...';
  
  // Google Maps API Key (same as in MapScreen)
  static const String GOOGLE_MAPS_API_KEY = 'AIzaSyAB3tS0U-SVSJLEHnpKKYa7wtK7cL3S2YM';

  @override
  void initState() {
    super.initState();
    _initializeMapWithRoute();
  }

  Future<void> _initializeMapWithRoute() async {
    _addLocationMarkers();
    await _getCurrentLocation();
    if (_currentPosition != null && widget.conversationDetails?.listerLatitude != null && widget.conversationDetails?.listerLongitude != null) {
      await _getDirectionsAndDrawRoute(
        LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
        LatLng(
          (widget.message.extraData!['latitude'] as num).toDouble(),
          (widget.message.extraData!['longitude'] as num).toDouble(),
        ),
      );
    }
  }

  void _addLocationMarkers() {
    final LatLng doerLocation = LatLng(
      (widget.message.extraData!['latitude'] as num).toDouble(),
      (widget.message.extraData!['longitude'] as num).toDouble(),
    );
    
    setState(() {
      // Add doer's shared location marker (blue)
      _markers.add(
        Marker(
          markerId: const MarkerId('doer_shared_location'),
          position: doerLocation,
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
          infoWindow: const InfoWindow(title: 'Doer Shared Location'),
        ),
      );
      
      // Add lister's location marker (red) if available
      if (widget.conversationDetails?.listerLatitude != null && widget.conversationDetails?.listerLongitude != null) {
        final LatLng listerLocation = LatLng(
          widget.conversationDetails!.listerLatitude!,
          widget.conversationDetails!.listerLongitude!,
        );
        _markers.add(
          Marker(
            markerId: const MarkerId('lister_location'),
            position: listerLocation,
            icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
            infoWindow: const InfoWindow(title: 'Lister Location'),
          ),
        );
      }
    });
  }

  Future<void> _getCurrentLocation() async {
    try {
      _currentPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      
      if (_currentPosition != null) {
        setState(() {
          _markers.add(
            Marker(
              markerId: const MarkerId('current_location'),
              position: LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
              icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure),
              infoWindow: const InfoWindow(title: 'My Location'),
            ),
          );
        });
      }
    } catch (e) {
      debugPrint('Error getting current location: $e');
    }
  }

  Future<void> _getDirectionsAndDrawRoute(LatLng origin, LatLng destination) async {
    String url = 'https://maps.googleapis.com/maps/api/directions/json?'
        'origin=${origin.latitude},${origin.longitude}&'
        'destination=${destination.latitude},${destination.longitude}&'
        'key=$GOOGLE_MAPS_API_KEY';

    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);

        if (data['routes'] != null && data['routes'].isNotEmpty) {
          final route = data['routes'][0];
          final String encodedPolyline = route['overview_polyline']['points'];
          final int durationSeconds = route['legs'][0]['duration']['value'];

          // Decode polyline
          PolylinePoints polylinePoints = PolylinePoints();
          List<PointLatLng> result = polylinePoints.decodePolyline(encodedPolyline);
          List<LatLng> polylineCoordinates = result.map((point) => LatLng(point.latitude, point.longitude)).toList();

          setState(() {
            _polylines.clear();
            _polylines.add(
              Polyline(
                polylineId: const PolylineId('route'),
                points: polylineCoordinates,
                color: Colors.blue,
                width: 3,
              ),
            );
            _travelTime = _formatDuration(durationSeconds);
          });
        } else {
          setState(() {
            _travelTime = 'No route';
          });
        }
      } else {
        setState(() {
          _travelTime = 'Error';
        });
      }
    } catch (e) {
      setState(() {
        _travelTime = 'Error';
      });
      debugPrint('Error fetching directions: $e');
    }
  }

  String _formatDuration(int seconds) {
    int minutes = (seconds / 60).round();
    if (minutes < 60) {
      return '$minutes min';
    } else {
      int hours = (minutes / 60).floor();
      minutes = minutes % 60;
      return '${hours}h ${minutes}m';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 150,
          child: Stack(
            children: [
              GoogleMap(
                onMapCreated: (GoogleMapController controller) {
                  _mapController = controller;
                },
                initialCameraPosition: CameraPosition(
                  target: LatLng(
                    (widget.message.extraData!['latitude'] as num).toDouble(),
                    (widget.message.extraData!['longitude'] as num).toDouble(),
                  ),
                  zoom: 13,
                ),
                markers: _markers,
                polylines: _polylines,
                gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
                  Factory<EagerGestureRecognizer>(() => EagerGestureRecognizer())
                },
                zoomGesturesEnabled: false,
                scrollGesturesEnabled: false,
                tiltGesturesEnabled: false,
                rotateGesturesEnabled: false,
              ),
              // Travel time overlay
              if (_travelTime != 'Calculating...')
                Positioned(
                  top: 8,
                  left: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade600,
                      borderRadius: BorderRadius.circular(6),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 3,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.directions_car, color: Colors.white, size: 14),
                        const SizedBox(width: 4),
                        Text(
                          _travelTime,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        FutureBuilder<Position>(
          future: Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.high),
          builder: (context, snapshot) {
            if (snapshot.hasData && widget.conversationDetails?.listingLatitude != null && widget.conversationDetails?.listingLongitude != null) {
              final double distance = Geolocator.distanceBetween(
                widget.conversationDetails!.listingLatitude!,
                widget.conversationDetails!.listingLongitude!,
                snapshot.data!.latitude,
                snapshot.data!.longitude,
              ) / 1000;
              return Text(
                'Distance from listing: ${distance.toStringAsFixed(1)} km',
                style: TextStyle(color: widget.isMe ? Colors.white : Colors.black87),
              );
            } else if (snapshot.hasError) {
              return Text(
                'Unable to calculate distance',
                style: TextStyle(color: widget.isMe ? Colors.white : Colors.black87),
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ],
    );
  }
}
