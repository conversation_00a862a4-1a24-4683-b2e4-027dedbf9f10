-- Create login_notifications table for tracking login security notifications
-- This table stores information about login attempts that trigger email notifications

CREATE TABLE IF NOT EXISTS login_notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    device_info TEXT,
    ip_address VARCHAR(45),
    location VARCHAR(255),
    login_time DATETIME NOT NULL,
    confirmation_token VARCHAR(64) NOT NULL UNIQUE,
    status ENUM('pending', 'confirmed', 'denied') DEFAULT 'pending',
    confirmed_at DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_confirmation_token (confirmation_token),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at)
);

-- Create security_incidents table for logging security events
CREATE TABLE IF NOT EXISTS security_incidents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    incident_type VARCHAR(50) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_incident_type (incident_type),
    INDEX idx_created_at (created_at)
);

-- Create password_reset_tokens table for secure password resets
CREATE TABLE IF NOT EXISTS password_reset_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(64) NOT NULL UNIQUE,
    expires_at DATETIME NOT NULL,
    used TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_token (token),
    INDEX idx_expires_at (expires_at)
);

-- Add cleanup procedure for expired tokens and notifications
DELIMITER //

CREATE EVENT IF NOT EXISTS cleanup_expired_security_tokens
ON SCHEDULE EVERY 1 HOUR
DO
BEGIN
    -- Clean up expired login notifications (older than 24 hours)
    DELETE FROM login_notifications 
    WHERE expires_at < NOW();
    
    -- Clean up expired password reset tokens
    DELETE FROM password_reset_tokens 
    WHERE expires_at < NOW();
    
    -- Clean up old security incidents (older than 90 days)
    DELETE FROM security_incidents 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
END//

DELIMITER ;

-- Enable event scheduler if not already enabled
SET GLOBAL event_scheduler = ON;