<?php
// Step by step test WITH actual payment processing
// Upload this to public_html/api/api/wallet/ and replace step_by_step_test.php

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set headers first
header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$steps = [];
$error = null;

try {
    $steps[] = "Step 1: PHP started";
    
    // Test if we can handle POST data
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $steps[] = "Step 2: POST request detected";
        
        $rawInput = file_get_contents('php://input');
        $steps[] = "Step 3: Raw input read: " . strlen($rawInput) . " bytes";
        
        if (!empty($rawInput)) {
            $input = json_decode($rawInput, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $steps[] = "Step 4: JSON decoded successfully";
            } else {
                $steps[] = "Step 4: JSON decode failed: " . json_last_error_msg();
            }
        } else {
            $steps[] = "Step 4: No input data received";
        }
    } else {
        $steps[] = "Step 2: GET request - using test data";
        $input = [
            'lister_id' => 87,
            'application_id' => 187,
            'doer_fee' => 500.0,
            'transaction_fee' => 25.0,
            'total_amount' => 525.0
        ];
    }
    
    $steps[] = "Step 5: About to test database connection";
    
    // Test database connection
    $db_path = '../../config/db_connect.php';
    if (file_exists($db_path)) {
        $steps[] = "Step 6: Database config file found";
        
        require_once $db_path;
        $steps[] = "Step 7: Database config included";
        
        if (isset($conn)) {
            $steps[] = "Step 8: Connection variable exists";
            
            if (!$conn->connect_error) {
                $steps[] = "Step 9: Database connected successfully";
                
                // Test a simple query
                $result = $conn->query("SELECT 1 as test");
                if ($result) {
                    $steps[] = "Step 10: Simple query executed successfully";
                    
                    // NOW PROCESS THE ACTUAL PAYMENT
                    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($input['lister_id']) && isset($input['application_id'])) {
                        $steps[] = "Step 11: Starting payment processing";
                        
                        $listerId = intval($input['lister_id']);
                        $applicationId = intval($input['application_id']);
                        $doerFee = floatval($input['doer_fee'] ?? 0);
                        $transactionFee = floatval($input['transaction_fee'] ?? 25.0);
                        $totalAmount = floatval($input['total_amount'] ?? 0);
                        
                        $steps[] = "Step 12: Payment data - Lister: $listerId, App: $applicationId, Total: ₱$totalAmount";
                        
                        // Start transaction
                        $conn->autocommit(false);
                        $steps[] = "Step 13: Database transaction started";
                        
                        try {
                            // 1. Get current lister balance
                            $stmt = $conn->prepare("SELECT balance, full_name FROM users WHERE id = ?");
                            $stmt->bind_param("i", $listerId);
                            $stmt->execute();
                            $result = $stmt->get_result();
                            $lister = $result->fetch_assoc();
                            $stmt->close();
                            
                            if ($lister) {
                                $currentBalance = floatval($lister['balance']);
                                $steps[] = "Step 14: Lister current balance: ₱" . number_format($currentBalance, 2);
                                
                                if ($currentBalance >= $totalAmount) {
                                    // 2. Update lister balance (deduct total amount)
                                    $newBalance = $currentBalance - $totalAmount;
                                    $stmt = $conn->prepare("UPDATE users SET balance = ? WHERE id = ?");
                                    $stmt->bind_param("di", $newBalance, $listerId);
                                    
                                    if ($stmt->execute()) {
                                        $steps[] = "Step 15: Lister balance updated: ₱" . number_format($currentBalance, 2) . " -> ₱" . number_format($newBalance, 2);
                                        $stmt->close();
                                        
                                        // 3. Get doer info and update total_profit
                                        $stmt = $conn->prepare("SELECT doer_id FROM applicationsv2 WHERE id = ?");
                                        $stmt->bind_param("i", $applicationId);
                                        $stmt->execute();
                                        $result = $stmt->get_result();
                                        $app = $result->fetch_assoc();
                                        $stmt->close();
                                        
                                        if ($app) {
                                            $doerId = intval($app['doer_id']);
                                            $steps[] = "Step 16: Found doer ID: $doerId";
                                            
                                            // Get current doer profit
                                            $stmt = $conn->prepare("SELECT total_profit, full_name FROM users WHERE id = ?");
                                            $stmt->bind_param("i", $doerId);
                                            $stmt->execute();
                                            $result = $stmt->get_result();
                                            $doer = $result->fetch_assoc();
                                            $stmt->close();
                                            
                                            if ($doer) {
                                                $currentProfit = floatval($doer['total_profit']);
                                                $newProfit = $currentProfit + $doerFee;
                                                
                                                // Update doer total_profit
                                                $stmt = $conn->prepare("UPDATE users SET total_profit = ? WHERE id = ?");
                                                $stmt->bind_param("di", $newProfit, $doerId);
                                                
                                                if ($stmt->execute()) {
                                                    $steps[] = "Step 17: Doer profit updated: ₱" . number_format($currentProfit, 2) . " -> ₱" . number_format($newProfit, 2);
                                                    $stmt->close();
                                                    
                                                    // 4. Record platform revenue
                                                    $stmt = $conn->prepare("INSERT INTO platform_revenue (transaction_type, reference_id, application_id, user_id, amount, payment_amount, payment_method, source, description) VALUES ('job_payment_fee', ?, ?, ?, ?, ?, 'hanapp_balance', 'hanapp_balance_payment', 'Platform fee from HanApp Balance job payment')");
                                                    $stmt->bind_param("iiidd", $applicationId, $applicationId, $listerId, $transactionFee, $totalAmount);
                                                    
                                                    if ($stmt->execute()) {
                                                        $steps[] = "Step 18: Platform revenue recorded: ₱" . number_format($transactionFee, 2);
                                                        $stmt->close();
                                                        
                                                        // 5. Update application status
                                                        $stmt = $conn->prepare("UPDATE applicationsv2 SET status = 'completed', payment_confirmed = 1, payment_confirmed_at = NOW() WHERE id = ?");
                                                        $stmt->bind_param("i", $applicationId);
                                                        
                                                        if ($stmt->execute()) {
                                                            $steps[] = "Step 19: Application status updated to completed";
                                                            $stmt->close();
                                                            
                                                            // Commit transaction
                                                            $conn->commit();
                                                            $steps[] = "Step 20: Payment transaction committed successfully!";
                                                            
                                                            // Return success response
                                                            echo json_encode([
                                                                "success" => true,
                                                                "message" => "Payment processed successfully!",
                                                                "transaction_details" => [
                                                                    "application_id" => $applicationId,
                                                                    "lister_id" => $listerId,
                                                                    "lister_name" => $lister['full_name'],
                                                                    "doer_id" => $doerId,
                                                                    "doer_name" => $doer['full_name'],
                                                                    "doer_fee" => $doerFee,
                                                                    "transaction_fee" => $transactionFee,
                                                                    "total_amount" => $totalAmount,
                                                                    "previous_lister_balance" => $currentBalance,
                                                                    "new_lister_balance" => $newBalance,
                                                                    "previous_doer_profit" => $currentProfit,
                                                                    "new_doer_profit" => $newProfit
                                                                ],
                                                                "processing_steps" => $steps,
                                                                "timestamp" => date('Y-m-d H:i:s')
                                                            ]);
                                                            
                                                            if (isset($conn)) {
                                                                $conn->close();
                                                            }
                                                            exit();
                                                            
                                                        } else {
                                                            throw new Exception("Failed to update application status");
                                                        }
                                                    } else {
                                                        throw new Exception("Failed to record platform revenue");
                                                    }
                                                } else {
                                                    throw new Exception("Failed to update doer profit");
                                                }
                                            } else {
                                                throw new Exception("Doer not found");
                                            }
                                        } else {
                                            throw new Exception("Application not found");
                                        }
                                    } else {
                                        throw new Exception("Failed to update lister balance");
                                    }
                                } else {
                                    throw new Exception("Insufficient balance. Required: ₱" . number_format($totalAmount, 2) . ", Available: ₱" . number_format($currentBalance, 2));
                                }
                            } else {
                                throw new Exception("Lister not found");
                            }
                            
                        } catch (Exception $paymentError) {
                            $conn->rollback();
                            $steps[] = "Step ERROR: Payment failed - " . $paymentError->getMessage();
                            $error = $paymentError->getMessage();
                        }
                        
                        $conn->autocommit(true);
                    }
                } else {
                    $steps[] = "Step 10: Simple query failed: " . $conn->error;
                }
            } else {
                $steps[] = "Step 9: Database connection error: " . $conn->connect_error;
            }
        } else {
            $steps[] = "Step 8: Connection variable not set";
        }
    } else {
        $steps[] = "Step 6: Database config file not found at: $db_path";
    }
    
} catch (Exception $e) {
    $error = $e->getMessage();
    $steps[] = "ERROR: " . $error;
} catch (Error $e) {
    $error = $e->getMessage();
    $steps[] = "FATAL ERROR: " . $error;
}

// Default response if payment wasn't processed
echo json_encode([
    "success" => $error === null,
    "message" => $error ? "Error occurred: $error" : "Test completed successfully",
    "steps" => $steps,
    "method" => $_SERVER['REQUEST_METHOD'],
    "timestamp" => date('Y-m-d H:i:s')
]);

if (isset($conn)) {
    $conn->close();
}
?>
