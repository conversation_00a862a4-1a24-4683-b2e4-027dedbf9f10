workflows:
  android-workflow:
    name: Android Build
    environment:
      flutter: stable
      android_signing:
        - keystore_reference
      vars:
        PACKAGE_NAME: "com.debcompanylimited.hanapp"
        GRADLE_OPTS: "-Dorg.gradle.jvmargs=-Xmx8G -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError"
    scripts:
      - name: Set up code signing settings
        script: |
          keytool -list -v -keystore $CM_KEYSTORE_PATH -alias $CM_KEY_ALIAS -storepass $CM_KEYSTORE_PASSWORD -keypass $CM_KEY_PASSWORD
      - name: Get Flutter packages
        script: flutter pub get
      - name: Flutter build apk
        script: |
          export GRADLE_OPTS="-Dorg.gradle.jvmargs=-Xmx8G -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError"
          flutter build apk --release
    artifacts:
      - build/**/outputs/**/*.apk
      - build/**/outputs/**/mapping.txt
      - flutter_drive.log
    publishing:
      email:
        recipients:
          - <EMAIL> 