import 'package:flutter/material.dart';
import 'package:hanapp/services/xendit_service.dart';
import 'package:hanapp/utils/auth_service.dart';
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/utils/api_config.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class PaymentSummaryScreen extends StatefulWidget {
  final double doerFee;
  final String applicationId;
  final String listingTitle;

  const PaymentSummaryScreen({
    super.key,
    required this.doerFee,
    required this.applicationId,
    required this.listingTitle,
  });

  @override
  State<PaymentSummaryScreen> createState() => _PaymentSummaryScreenState();
}

class _PaymentSummaryScreenState extends State<PaymentSummaryScreen> {
  bool _isProcessing = false;
  String? _selectedPaymentMethod;
  String? _selectedBank;
  String? _selectedCardType;
  double _serviceFee = 0.0;
  double _totalAmount = 0.0;

  @override
  void initState() {
    super.initState();
    _totalAmount = widget.doerFee; // Initialize with doer fee only
  }

  // Calculate service fee based on selected payment method
  double _calculateServiceFee(double amount) {
    if (_selectedPaymentMethod == null) return 0.0;

    double fee = 0.0;

    switch (_selectedPaymentMethod) {
      case 'GCash':
        // GCash: 2.3% + ₱20
        fee = (amount * 0.023) + 20.0;
        break;
      case 'Credit/Debit Card':
        if (_selectedCardType == 'philippine') {
          // Philippine Cards: 3.2% + ₱30
          fee = (amount * 0.032) + 30.0;
        } else if (_selectedCardType == 'international_php') {
          // International Cards (PHP): 4.2% + ₱30
          fee = (amount * 0.042) + 30.0;
        } else {
          // Default to Philippine rate if card type not selected
          fee = (amount * 0.032) + 30.0;
        }
        break;
      case 'Bank Transfer':
      case 'bpi':
      case 'chinabank':
      case 'rcbc':
      case 'unionbank':
        // Bank Transfer: 1% + ₱20
        fee = (amount * 0.01) + 20.0;
        break;
      case 'TAPP Balance':
        // TAPP Balance: Fixed ₱20 fee
        fee = 20.0;
        break;
      default:
        fee = 0.0;
        break;
    }

    return fee;
  }

  // Calculate fees and update total amount
  void _calculateFees() {
    setState(() {
      _serviceFee = _calculateServiceFee(widget.doerFee);
      _totalAmount = widget.doerFee + _serviceFee;
    });
  }

  // Get the actual payment method for backend processing
  String _getActualPaymentMethod() {
    if (_selectedPaymentMethod == null) return '';

    String finalPaymentMethod = _selectedPaymentMethod!;
    if (_selectedPaymentMethod == 'Bank Transfer' && _selectedBank != null) {
      finalPaymentMethod = _selectedBank!; // Use the specific bank ID
    } else if (_selectedPaymentMethod == 'Credit/Debit Card') {
      // Use specific card type instead of generic 'card'
      if (_selectedCardType == 'philippine') {
        finalPaymentMethod = 'philippine_card';
      } else if (_selectedCardType == 'international_php') {
        finalPaymentMethod = 'international_card';
      } else {
        finalPaymentMethod = 'philippine_card'; // Default to Philippine if no card type selected
      }
    } else if (_selectedPaymentMethod == 'TAPP Balance') {
      finalPaymentMethod = 'tapp_balance'; // Convert to backend format
    }

    return finalPaymentMethod;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text('Payment Summary'),
        backgroundColor: Constants.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Job Title
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Job Payment',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Constants.textColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.listingTitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // Payment Method Selection
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Select Payment Method',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Constants.textColor,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Column(
                    children: [
                      _buildPaymentOption('GCash', 'GCash', feeText: 'Service fee: 2.3% + ₱20'),
                      const SizedBox(height: 12),
                      _buildCardOption(),
                      const SizedBox(height: 12),
                      _buildPaymentOption('TAPP Balance', 'TAPP Balance', feeText: 'Service fee: ₱20.00'),
                      const SizedBox(height: 12),
                      _buildBankTransferDropdown(),
                    ],
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Payment Breakdown
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Payment Details',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Constants.textColor,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // Doer Fee
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Doer Fee',
                        style: TextStyle(fontSize: 14, color: Constants.textColor),
                      ),
                      Text(
                        '₱${widget.doerFee.toStringAsFixed(2)}',
                        style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                      ),
                    ],
                  ),
                  
                  // Service Fee (only show if > 0)
                  if (_serviceFee > 0) ...[
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Service Fee',
                          style: TextStyle(fontSize: 14, color: Constants.textColor),
                        ),
                        Text(
                          '₱${_serviceFee.toStringAsFixed(2)}',
                          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                  ],
                  
                  const SizedBox(height: 12),
                  const Divider(),
                  const SizedBox(height: 8),
                  
                  // Total Amount
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Total Amount',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Constants.textColor,
                        ),
                      ),
                      Text(
                        '₱${_totalAmount.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Constants.primaryColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 40),

            // Pay Now Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: (_isProcessing || _selectedPaymentMethod == null) ? null : _processPayment,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Constants.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isProcessing
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        'Pay Now',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),
            
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentOption(String title, String value, {String? feeText}) {
    final isSelected = _selectedPaymentMethod == value;

    return InkWell(
      onTap: () {
        setState(() {
          _selectedPaymentMethod = value;
          _selectedBank = null; // Clear bank selection when switching payment methods
          _selectedCardType = null; // Clear card type selection when switching payment methods
          _calculateFees(); // Recalculate fees when payment method changes
        });
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isSelected ? Constants.primaryColor.withOpacity(0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              color: isSelected ? Constants.primaryColor : Colors.grey,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: isSelected ? Constants.primaryColor : Constants.textColor,
                    ),
                  ),
                  if (feeText != null)
                    Text(
                      feeText,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardOption() {
    final isSelected = _selectedPaymentMethod == 'Credit/Debit Card';

    return Column(
      children: [
        InkWell(
          onTap: () {
            setState(() {
              _selectedPaymentMethod = 'Credit/Debit Card';
              _selectedBank = null; // Clear bank selection when switching payment methods
              if (_selectedCardType == null) {
                _selectedCardType = 'philippine'; // Default to Philippine card
              }
              _calculateFees(); // Recalculate fees when payment method changes
            });
          },
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: isSelected ? Constants.primaryColor.withOpacity(0.1) : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                  color: isSelected ? Constants.primaryColor : Colors.grey,
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Credit/Debit Card',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Constants.textColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        if (isSelected) ...[
          Container(
            margin: const EdgeInsets.only(left: 40, top: 8),
            child: Column(
              children: [
                // Philippine Card Option
                RadioListTile<String>(
                  title: const Text('Philippine Card'),
                  subtitle: const Text('Service fee: 3.2% + ₱30'),
                  value: 'philippine',
                  groupValue: _selectedCardType,
                  onChanged: (value) {
                    setState(() {
                      _selectedCardType = value;
                      _calculateFees(); // Recalculate fees when card type changes
                    });
                  },
                  activeColor: Constants.primaryColor,
                ),
                // International Card Option
                RadioListTile<String>(
                  title: const Text('International Card (PHP)'),
                  subtitle: const Text('Service fee: 4.2% + ₱30'),
                  value: 'international_php',
                  groupValue: _selectedCardType,
                  onChanged: (value) {
                    setState(() {
                      _selectedCardType = value;
                      _calculateFees(); // Recalculate fees when card type changes
                    });
                  },
                  activeColor: Constants.primaryColor,
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildBankTransferDropdown() {
    final isSelected = _selectedPaymentMethod == 'Bank Transfer';

    // List of working banks
    final workingBanks = [
      {'id': 'bpi', 'name': 'BPI Direct Debit'},
      {'id': 'chinabank', 'name': 'China Bank Direct Debit'},
      {'id': 'rcbc', 'name': 'RCBC Direct Debit'},
      {'id': 'unionbank', 'name': 'UBP Direct Debit'},
    ];

    return Column(
      children: [
        InkWell(
          onTap: () {
            setState(() {
              _selectedPaymentMethod = 'Bank Transfer';
              _selectedCardType = null; // Clear card type selection when switching payment methods
              if (_selectedBank == null && workingBanks.isNotEmpty) {
                _selectedBank = workingBanks.first['id']; // Default to first bank
              }
              _calculateFees(); // Recalculate fees when payment method changes
            });
          },
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: isSelected ? Constants.primaryColor.withOpacity(0.1) : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                  color: isSelected ? Constants.primaryColor : Colors.grey,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Bank Transfer',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: isSelected ? Constants.primaryColor : Constants.textColor,
                        ),
                      ),
                      const Text(
                        'Service fee: 1% + ₱20',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        if (isSelected) ...[
          Container(
            margin: const EdgeInsets.only(left: 40, top: 8),
            child: DropdownButtonFormField<String>(
              value: _selectedBank,
              decoration: const InputDecoration(
                labelText: 'Select Bank',
                border: OutlineInputBorder(),
              ),
              items: workingBanks.map((bank) {
                return DropdownMenuItem<String>(
                  value: bank['id'],
                  child: Text(bank['name']!),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedBank = value;
                  _calculateFees(); // Recalculate fees when bank changes
                });
              },
            ),
          ),
        ],
      ],
    );
  }

  String _getPaymentMethodName(String paymentMethod) {
    switch (paymentMethod) {
      case 'gcash':
        return 'GCash E-Wallet';
      case 'card':
        return 'Credit/Debit Card';
      case 'bpi':
        return 'BPI Direct Debit';
      case 'bdo':
        return 'BDO Online Banking';
      case 'metrobank':
        return 'Metrobank Online';
      case 'unionbank':
        return 'UBP Direct Debit';
      case 'rcbc':
        return 'RCBC Direct Debit';
      case 'chinabank':
        return 'China Bank Direct Debit';
      case 'hanapp_balance':
      case 'tapp_balance':
        return 'TAPP Balance';
      default:
        return paymentMethod;
    }
  }

  Future<void> _processPayment() async {
    if (_selectedPaymentMethod == null) {
      _showSnackBar('Please select a payment method', isError: true);
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      // First, update payment details in the database
      print('💾 Updating payment details in database...');
      await _updatePaymentDetails();

      // Check for HanApp Balance payment (handle both formats)
      final paymentMethodLower = _selectedPaymentMethod!.toLowerCase().replaceAll(' ', '_');
      print('🔍 Payment method: "$_selectedPaymentMethod" -> normalized: "$paymentMethodLower"');

      if (paymentMethodLower == 'tapp_balance') {
        print('💰 Processing TAPP Balance payment...');
        await _processHanAppBalancePayment();
      } else {
        print('💳 Processing Xendit payment...');
        await _processXenditPayment();
      }
    } catch (e) {
      _showSnackBar('Payment processing failed: $e', isError: true);
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  Future<void> _updatePaymentDetails() async {
    print('💾 Starting payment details update...');
    print('📋 Payment Details to Update:');
    print('   Application ID: ${widget.applicationId}');
    print('   Doer Fee: ₱${widget.doerFee}');
    print('   Service Fee (Transaction Fee): ₱$_serviceFee');
    print('   Total Amount: ₱$_totalAmount');

    // Call backend to update payment details in database
    print('🌐 Calling API: ${ApiConfig.baseUrl}/api/job_payment/update_payment_details.php');
    final response = await http.post(
      Uri.parse('${ApiConfig.baseUrl}/api/job_payment/update_payment_details.php'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'application_id': widget.applicationId,
        'doer_fee': widget.doerFee,
        'transaction_fee': _serviceFee,
        'total_amount': _totalAmount,
      }),
    );

    final result = jsonDecode(response.body);

    // Debug logging for Visual Studio terminal
    print('🔍 Payment Details Update Response:');
    print('📄 Response: ${jsonEncode(result)}');

    if (!result['success']) {
      throw Exception(result['message'] ?? 'Failed to update payment details');
    }

    print('✅ Payment details updated successfully in database');
    print('   Listing ID: ${result['listing_id']}');
    print('   Listing Type: ${result['listing_type']}');
    print('   Listing Title: ${result['listing_title']}');
  }

  Future<void> _processHanAppBalancePayment() async {
    // Process HanApp Balance payment - deduct from lister's balance and add to doer's profit
    print('💰 Starting Tapp Balance Payment Process...');
    print('📋 Payment Details:');
    print('   Application ID: ${widget.applicationId}');
    print('   Doer Fee: ₱${widget.doerFee}');
    print('   Total Amount: ₱$_totalAmount');
    print('   Payment Method: $_selectedPaymentMethod');

    final currentUser = await AuthService.getUser();
    if (currentUser == null) {
      throw Exception('User not found');
    }

    print('👤 Lister ID: ${currentUser.id}');

    // Call backend to process TAPP Balance payment
    print('🌐 Calling API: ${ApiConfig.baseUrl}/api/job_payment/process_hanapp_balance_payment.php');
    final response = await http.post(
      Uri.parse('${ApiConfig.baseUrl}/api/job_payment/process_hanapp_balance_payment.php'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'application_id': widget.applicationId,
        'lister_id': currentUser.id,
        'doer_fee': widget.doerFee,
        'total_amount': _totalAmount,
      }),
    );

    final result = jsonDecode(response.body);

    // Debug logging for Visual Studio terminal
    print('🔍 Tapp Balance Payment Response:');
    print('📄 Response: ${jsonEncode(result)}');

    if (result['success']) {
      // Log debug information if available
      if (result['debug_info'] != null) {
        final debug = result['debug_info'];
        print('💰 Payment Debug Information:');
        print('   📊 Lister Balance: ${debug['lister_balance_before']} → ${debug['lister_balance_after']}');
        print('   💼 Doer Profit: ${debug['doer_profit_before']} → ${debug['doer_profit_after']}');
        print('   💵 Doer Fee Added: ${debug['doer_fee_added']}');
        print('   💸 Total Deducted: ${debug['total_amount_deducted']}');
        print('   👤 Doer: ID=${debug['doer_id']}, Name=${debug['doer_name']}');
        print('   ✅ Affected Rows: ${debug['affected_rows']}');
        print('   🎯 Profit Update Success: ${debug['profit_update_success']}');
      }

      if (mounted) {
        _showSnackBar('Payment completed! Project will start automatically.', isError: false);
        Navigator.of(context).pop(true); // Return success
      }
    } else {
      // Log error debug information if available
      if (result['debug_info'] != null) {
        print('❌ Payment Error Debug: ${jsonEncode(result['debug_info'])}');
      }
      throw Exception(result['message'] ?? 'Payment failed');
    }
  }

  Future<void> _processXenditPayment() async {
    // Process Xendit payment - create invoice and show success/failed HTML
    final currentUser = await AuthService.getUser();
    if (currentUser == null) {
      throw Exception('User not found');
    }

    // Get the actual payment method for backend processing
    String actualPaymentMethod = _getActualPaymentMethod();
    print('🔍 Selected payment method: "$_selectedPaymentMethod" -> Actual: "$actualPaymentMethod"');

    // Create Xendit invoice for job payment
    final response = await http.post(
      Uri.parse('${ApiConfig.baseUrl}/api/job_payment/create_job_payment_invoice.php'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'application_id': widget.applicationId,
        'lister_id': currentUser.id,
        'doer_fee': widget.doerFee,
        'total_amount': _totalAmount,
        'payment_method': actualPaymentMethod,
        'user_email': currentUser.email,
        'user_full_name': currentUser.fullName,
      }),
    );

    final result = jsonDecode(response.body);
    if (result['success'] && result['invoice_url'] != null) {
      // Launch payment URL
      final success = await XenditService.instance.launchPayment(result['invoice_url']);
      if (success && mounted) {
        // _showSnackBar('Payment page opened. You will be notified when payment is confirmed.', isError: false);
        Navigator.of(context).pop(true); // Return success
      } else {
        throw Exception('Failed to open payment page');
      }
    } else {
      throw Exception(result['message'] ?? 'Failed to create payment invoice');
    }
  }

  void _showSnackBar(String message, {required bool isError}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: Duration(seconds: isError ? 4 : 2),
      ),
    );
  }
}
