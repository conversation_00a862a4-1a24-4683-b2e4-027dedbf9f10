import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/models/user.dart';
import 'package:hanapp/utils/auth_service.dart';
import 'package:hanapp/utils/verification_storage.dart';

import '../utils/api_config.dart';

class VerificationService {
  final String _baseUrl = ApiConfig.baseUrl;

  /// Submits front and back ID photos for verification.
  Future<Map<String, dynamic>> submitIdVerification({
    required int userId,
    required String idType,
    required String idPhotoFrontPath,
    required String idPhotoBackPath,
    required String brgyClearancePhotoPath, // NEW PARAMETER
    required bool confirmation,
  }) async {
    final url = Uri.parse('$_baseUrl/verification/submit_id_verification.php');
    print('VerificationService: Submitting ID photos to URL: $url');

    try {
      var request = http.MultipartRequest('POST', url)
        ..fields['user_id'] = userId.toString()
        ..fields['id_type'] = idType
        ..fields['confirmation'] = confirmation.toString();

      request.files.add(await http.MultipartFile.fromPath(
        'id_photo_front',
        idPhotoFrontPath,
      ));
      request.files.add(await http.MultipartFile.fromPath(
        'id_photo_back',
        idPhotoBackPath,
      ));
      // NEW: Add Barangay Clearance photo to the request
      request.files.add(await http.MultipartFile.fromPath(
        'brgy_clearance_photo',
        brgyClearancePhotoPath,
      ));

      var response = await request.send();
      var responseBody = await response.stream.bytesToString();
      final decodedResponse = json.decode(responseBody);

      print('VerificationService Submit ID Response: ${response.statusCode} - $decodedResponse');

      if (response.statusCode == 200 && decodedResponse['success']) {
        // Refresh user data using new API-first approach
        await AuthService.refreshUserData();
        return {'success': true, 'message': decodedResponse['message']};
      } else {
        return {'success': false, 'message': decodedResponse['message'] ?? 'Failed to submit ID verification.'};
      }
    } catch (e) {
      print('VerificationService Error submitting ID verification: $e');
      return {'success': false, 'message': 'Network error: $e'};
    }
  }

  /// Submits a live photo for face verification.
  Future<Map<String, dynamic>> requestFaceVerification({
    required int userId,
    required String livePhotoPath,
    String verificationType = 'normal', // Default to 'normal' verification
  }) async {
    final url = Uri.parse('$_baseUrl/verification/request_face_verification.php');
    print('VerificationService: Submitting live photo to URL: $url with verification type: $verificationType');

    try {
      var request = http.MultipartRequest('POST', url)
        ..fields['user_id'] = userId.toString()
        ..fields['verification_type'] = verificationType;

      request.files.add(await http.MultipartFile.fromPath(
        'live_photo',
        livePhotoPath,
      ));

      var response = await request.send();
      var responseBody = await response.stream.bytesToString();
      final decodedResponse = json.decode(responseBody);

      print('VerificationService Request Face Response: ${response.statusCode} - $decodedResponse');

      if (response.statusCode == 200 && decodedResponse['success']) {
        // Refresh user data using new API-first approach
        await AuthService.refreshUserData();
        return {'success': true, 'message': decodedResponse['message']};
      } else {
        return {'success': false, 'message': decodedResponse['message'] ?? 'Face verification failed.'};
      }
    } catch (e) {
      print('VerificationService Error requesting face verification: $e');
      return {'success': false, 'message': 'Network error: $e'};
    }
  }

  /// Gets the current verification and badge status of a user.
  Future<Map<String, dynamic>> getVerificationStatus({required int userId}) async {
    final url = Uri.parse('$_baseUrl/verification/get_verification_status.php?user_id=$userId');
    print('VerificationService: Getting verification status from URL: $url');

    try {
      final response = await http.get(url);
      final decodedResponse = json.decode(response.body);

      print('VerificationService Get Status Response: ${response.statusCode} - $decodedResponse');

      if (response.statusCode == 200 && decodedResponse['success']) {
        final statusData = decodedResponse['status_data'];
        print('VerificationService Raw Status Data: $statusData');
        print('VerificationService verification_status type: ${statusData['verification_status'].runtimeType}');
        print('VerificationService badge_status type: ${statusData['badge_status'].runtimeType}');
        return {'success': true, 'status_data': statusData};
      } else {
        return {'success': false, 'message': decodedResponse['message'] ?? 'Failed to get verification status.'};
      }
    } catch (e) {
      print('VerificationService Error getting verification status: $e');
      return {'success': false, 'message': 'Network error: $e'};
    }
  }

  /// Acquires the verified badge for a user (simulates payment/opt-in).
  Future<Map<String, dynamic>> acquireVerifiedBadge({required int userId}) async {
    final url = Uri.parse('$_baseUrl/verification/acquire_badge.php');
    print('VerificationService: Acquiring badge for user: $userId');

    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'user_id': userId}),
      );
      final decodedResponse = json.decode(response.body);

      print('VerificationService Acquire Badge Response: ${response.statusCode} - $decodedResponse');

      if (response.statusCode == 200 && decodedResponse['success']) {
        // Refresh user data using new API-first approach after successful badge acquisition
        await AuthService.refreshUserData();
        return {'success': true, 'message': decodedResponse['message']};
      } else {
        return {'success': false, 'message': decodedResponse['message'] ?? 'Failed to acquire badge.'};
      }
    } catch (e) {
      print('VerificationService Error acquiring badge: $e');
      return {'success': false, 'message': 'Network error: $e'};
    }
  }

  /// Submit complete verification with all images (ID photos + face scan)
  Future<Map<String, dynamic>> submitCompleteVerification({
    required String idType,
    required String frontPhotoPath,
    required String backPhotoPath,
    required String brgyPhotoPath,
    required String facePhotoPath,
    required bool confirmation,
    required int userId,
  }) async {
    final url = Uri.parse('$_baseUrl/verification/submit_complete_verification.php');
    print('VerificationService: Submitting complete verification to URL: $url');

    try {
      var request = http.MultipartRequest('POST', url)
        ..fields['user_id'] = userId.toString()
        ..fields['id_type'] = idType
        ..fields['confirmation'] = confirmation.toString();

      // Add all image files
      request.files.add(await http.MultipartFile.fromPath(
        'id_photo_front',
        frontPhotoPath,
      ));
      request.files.add(await http.MultipartFile.fromPath(
        'id_photo_back',
        backPhotoPath,
      ));
      request.files.add(await http.MultipartFile.fromPath(
        'brgy_clearance_photo',
        brgyPhotoPath,
      ));
      request.files.add(await http.MultipartFile.fromPath(
        'live_photo',
        facePhotoPath,
      ));

      print('VerificationService: Sending complete verification request...');
      var response = await request.send();
      var responseBody = await response.stream.bytesToString();

      print('VerificationService Complete Verification Response: ${response.statusCode}');
      print('VerificationService Response Body: $responseBody');

      if (responseBody.isEmpty) {
        throw Exception('Empty response from server');
      }

      final decodedResponse = json.decode(responseBody);
      print('VerificationService Decoded Response: $decodedResponse');

      if (response.statusCode == 200 && decodedResponse['success']) {
        // Refresh user data using API-first approach
        await AuthService.refreshUserData();
        return {'success': true, 'message': decodedResponse['message']};
      } else {
        return {'success': false, 'message': decodedResponse['message'] ?? 'Complete verification failed.'};
      }
    } catch (e) {
      print('VerificationService Error submitting complete verification: $e');
      return {'success': false, 'message': 'Network error: $e'};
    }
  }

  /// Submit complete verification using data from SharedPreferences
  Future<Map<String, dynamic>> submitCompleteVerificationFromStorage({
    required String facePhotoPath,
  }) async {
    print('VerificationService: Submitting complete verification from storage');

    try {
      // Get stored verification data
      final storedData = await VerificationStorage.getVerificationData();
      if (storedData == null) {
        throw Exception('No verification data found in storage');
      }

      // Submit complete verification
      final result = await submitCompleteVerification(
        idType: storedData['idType'],
        frontPhotoPath: storedData['frontPhotoPath'],
        backPhotoPath: storedData['backPhotoPath'],
        brgyPhotoPath: storedData['brgyPhotoPath'],
        facePhotoPath: facePhotoPath,
        confirmation: storedData['confirmation'],
        userId: storedData['userId'],
      );

      // If successful, clear the stored data
      if (result['success']) {
        await VerificationStorage.clearVerificationData();
        print('VerificationService: Cleared verification data from storage after successful upload');
      }

      return result;
    } catch (e) {
      print('VerificationService Error submitting from storage: $e');
      return {'success': false, 'message': 'Error submitting verification: $e'};
    }
  }

  /// Submit complete badge verification with all images (ID photos + face scan, no barangay clearance)
  Future<Map<String, dynamic>> submitCompleteBadgeVerification({
    required String idType,
    required String frontPhotoPath,
    required String backPhotoPath,
    required String facePhotoPath,
    required bool confirmation,
    required int userId,
  }) async {
    final url = Uri.parse('$_baseUrl/verification/submit_complete_badge_verification.php');
    print('VerificationService: Submitting complete badge verification to URL: $url');

    try {
      var request = http.MultipartRequest('POST', url);

      // Add form fields
      request.fields['user_id'] = userId.toString();
      request.fields['id_type'] = idType;
      request.fields['confirmation'] = confirmation.toString();

      // Add image files
      request.files.add(await http.MultipartFile.fromPath('id_photo_front', frontPhotoPath));
      request.files.add(await http.MultipartFile.fromPath('id_photo_back', backPhotoPath));
      request.files.add(await http.MultipartFile.fromPath('live_photo', facePhotoPath));

      print('VerificationService: Sending badge verification request with ${request.files.length} files');

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      print('VerificationService: Badge verification response status: ${response.statusCode}');
      print('VerificationService: Badge verification response body: ${response.body}');

      final responseData = json.decode(response.body);
      return responseData;
    } catch (e) {
      print('VerificationService Error submitting complete badge verification: $e');
      return {'success': false, 'message': 'Network error: $e'};
    }
  }

  /// Submit complete badge verification from storage (called after face verification)
  Future<Map<String, dynamic>> submitCompleteBadgeVerificationFromStorage({
    required String facePhotoPath,
  }) async {
    print('VerificationService: Submitting complete badge verification from storage');

    try {
      // Get stored badge verification data
      final storedData = await VerificationStorage.getBadgeVerificationData();
      if (storedData == null) {
        throw Exception('No badge verification data found in storage');
      }

      // Submit complete badge verification
      final result = await submitCompleteBadgeVerification(
        idType: storedData['idType'],
        frontPhotoPath: storedData['frontPhotoPath'],
        backPhotoPath: storedData['backPhotoPath'],
        facePhotoPath: facePhotoPath,
        confirmation: storedData['confirmation'],
        userId: storedData['userId'],
      );

      // If successful, clear the stored data
      if (result['success']) {
        await VerificationStorage.clearBadgeVerificationData();
        print('VerificationService: Cleared badge verification data from storage after successful upload');
      }

      return result;
    } catch (e) {
      print('VerificationService Error submitting complete badge verification from storage: $e');
      return {'success': false, 'message': 'Network error: $e'};
    }
  }

  /// Submit badge ID verification only (without face scan)
  Future<Map<String, dynamic>> submitBadgeIdVerification({
    required int userId,
    required String idType,
    required String idPhotoFrontPath,
    required String idPhotoBackPath,
    required bool confirmation,
  }) async {
    final url = Uri.parse('$_baseUrl/verification/submit_badge_id_verification.php');
    print('VerificationService: Submitting badge ID verification to URL: $url');

    try {
      var request = http.MultipartRequest('POST', url);

      // Add form fields
      request.fields['user_id'] = userId.toString();
      request.fields['id_type'] = idType;
      request.fields['confirmation'] = confirmation.toString();

      // Add image files (no barangay clearance for badge verification)
      request.files.add(await http.MultipartFile.fromPath('id_photo_front', idPhotoFrontPath));
      request.files.add(await http.MultipartFile.fromPath('id_photo_back', idPhotoBackPath));

      print('VerificationService: Sending badge ID verification request with ${request.files.length} files');

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      print('VerificationService: Badge ID verification response status: ${response.statusCode}');
      print('VerificationService: Badge ID verification response body: ${response.body}');

      final responseData = json.decode(response.body);
      return responseData;
    } catch (e) {
      print('VerificationService Error submitting badge ID verification: $e');
      return {'success': false, 'message': 'Network error: $e'};
    }
  }
}
