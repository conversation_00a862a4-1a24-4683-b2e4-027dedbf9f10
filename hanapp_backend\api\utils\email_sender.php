<?php

define('SENDGRID_API_KEY', '*********************************************************************');
define('SENDGRID_FROM_EMAIL', '<EMAIL>');
define('SENDGRID_FROM_NAME', 'TAPP Support');
define('SENDGRID_OTP_TEMPLATE_ID', 'd-af530a28ecf44244ae4a971285b154ea'); // Example for a specific template ID

function sendEmailViaSendGrid($toEmail, $toName, $subject = '', $bodyHtml = '', $bodyText = '', $templateId = null, $dynamicTemplateData = [], $idempotencyKey = null) {
    $url = 'https://api.sendgrid.com/v3/mail/send';
    $apiKey = SENDGRID_API_KEY;
    
    // Add detailed logging to track email sending
    $requestId = uniqid('email_', true);
    error_log("[" . date('Y-m-d H:i:s') . "] SendGrid Request ID: $requestId - Starting email send to: $toEmail, Subject: $subject");

    // Basic validation for required parameters
    if (empty($toEmail)) {
        error_log("[" . date('Y-m-d H:i:s') . "] SendGrid Request ID: $requestId - Error: Recipient email is missing.");
        return ['success' => false, 'message' => 'Recipient email is required.'];
    }

    $personalizations = [
        [
            'to' => [
                ['email' => $toEmail, 'name' => $toName] // Correctly passing $toName here
            ],
        ]
    ];

    $emailContent = [];

    // Determine if a dynamic template should be used
    if ($templateId) {
        // CORRECTED: When using a dynamic template, SendGrid expects 'dynamic_template_data'
        // within personalizations and 'template_id' at the top level.
        $personalizations[0]['dynamic_template_data'] = $dynamicTemplateData;
        $emailContent['template_id'] = $templateId;
    } else {
        // Using plain subject and content (no template)
        if (empty($subject) || (empty($bodyHtml) && empty($bodyText))) {
            error_log("SendGrid Error: Subject and content (HTML or text) are required when not using a template.");
            return ['success' => false, 'message' => 'Subject and email content are required when not using a template.'];
        }
        $emailContent['subject'] = $subject;
        $emailContent['content'] = [
            // CORRECTED ORDER: text/plain must come before text/html as per SendGrid API requirement
            ['type' => 'text/plain', 'value' => $bodyText],
            ['type' => 'text/html', 'value' => $bodyHtml]
        ];
    }

    $data = [
        'personalizations' => $personalizations,
        'from' => [
            'email' => SENDGRID_FROM_EMAIL,
            'name' => SENDGRID_FROM_NAME
        ],
    ];

    // Merge template-specific or content-specific data into the main payload
    $data = array_merge($data, $emailContent);

    $headers = [
        'Authorization: Bearer ' . $apiKey,
        'Content-Type: application/json',
        $idempotencyKey ? 'X-Idempotency-Key: ' . $idempotencyKey : ''
    ];
    $headers = array_filter($headers);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // Return the response as a string
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true); // Always verify SSL certificates in production

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_close($ch);

    if ($curlError) {
        error_log("SendGrid cURL Error: " . $curlError);
        return ['success' => false, 'message' => "Failed to connect to email service: " . $curlError];
    }

    if ($httpCode >= 200 && $httpCode < 300) {
        // Successful response from SendGrid (usually 200 OK or 202 Accepted)
        error_log("[" . date('Y-m-d H:i:s') . "] SendGrid Request ID: $requestId - Email sent successfully to $toEmail. HTTP Code: $httpCode");
        return ['success' => true, 'message' => 'Email sent successfully.'];
    } else {
        // SendGrid API returned an error
        $decodedResponse = json_decode($response, true);
        $errorMessage = 'Unknown SendGrid error.';
        if (isset($decodedResponse['errors']) && is_array($decodedResponse['errors']) && !empty($decodedResponse['errors'][0]['message'])) {
            $errorMessage = $decodedResponse['errors'][0]['message'];
        } else if (isset($decodedResponse['errors']) && is_string($decodedResponse['errors'])) {
            $errorMessage = $decodedResponse['errors']; // Sometimes errors might be a string
        } else if (is_string($response) && !empty($response)) {
            $errorMessage = $response; // Fallback to raw response if JSON decoding fails
        }
        error_log("[" . date('Y-m-d H:i:s') . "] SendGrid Request ID: $requestId - API Error to $toEmail. HTTP Code: $httpCode, Response: " . $response);
        return ['success' => false, 'message' => "Email sending failed: " . $errorMessage];
    }
}
