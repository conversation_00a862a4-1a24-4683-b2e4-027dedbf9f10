import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'dart:async'; // Added for Completer and Timer

class LocationService {
  Future<Position?> getCurrentLocation() async {
    bool serviceEnabled;
    LocationPermission permission;

    // Test if location services are enabled.
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return Future.error('Location services are disabled.');
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return Future.error('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      return Future.error(
          'Location permissions are permanently denied, we cannot request permissions.');
    }

    // Advanced: Use a stream to get the most accurate position within 5 seconds
    final List<Position> positions = [];
    final stream = Geolocator.getPositionStream(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.bestForNavigation,
        distanceFilter: 0,
      ),
    );
    final completer = Completer<Position?>();
    late StreamSubscription<Position> subscription;
    Timer? timer;

    subscription = stream.listen((Position position) {
      positions.add(position);
      // If we get a very accurate fix, return immediately
      if (position.accuracy <= 20) {
        completer.complete(position);
        subscription.cancel();
        timer?.cancel();
      }
    }, onError: (e) {
      if (!completer.isCompleted) {
        completer.completeError(e);
      }
      subscription.cancel();
      timer?.cancel();
    });

    // Timeout after 5 seconds and return the best accuracy found
    timer = Timer(const Duration(seconds: 5), () {
      if (!completer.isCompleted) {
        if (positions.isNotEmpty) {
          // Pick the most accurate position
          positions.sort((a, b) => a.accuracy.compareTo(b.accuracy));
          completer.complete(positions.first);
        } else {
          completer.completeError('Could not get a location fix.');
        }
        subscription.cancel();
      }
    });

    try {
      return await completer.future;
    } catch (e) {
      // Fallback to single getCurrentPosition if stream fails
      return await Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.bestForNavigation);
    }
  }

  Future<LatLng?> getCoordinatesFromAddress(String address) async {
    // try {
    //   List<Placemark> locations = await placemarkFromAddress(address);
    //   if (locations.isNotEmpty) {
    //     return LatLng(locations.first.latitude, locations.first.longitude);
    //   }
    //   return null;
    // } catch (e) {
    //   print('Error getting coordinates for address $address: $e');
    //   return null;
    // }
  }

  Future<String?> getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude);
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks.first;
        return "${place.street}, ${place.subLocality}, ${place.locality}, ${place.administrativeArea}, ${place.country}";
      }
      return null;
    } catch (e) {
      print('Error getting address from coordinates $latitude, $longitude: $e');
      return null;
    }
  }

  double calculateDistance(LatLng start, LatLng end) {
    return Geolocator.distanceBetween(
      start.latitude,
      start.longitude,
      end.latitude,
      end.longitude,
    );
  }
}
