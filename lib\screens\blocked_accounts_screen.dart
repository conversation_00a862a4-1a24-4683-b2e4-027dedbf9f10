// lib/screens/blocked_accounts_screen.dart
import 'package:flutter/material.dart';
import 'package:hanapp/models/user.dart';
import 'package:hanapp/utils/auth_service.dart';
import 'package:hanapp/utils/user_service.dart'; // Ensure this is correctly imported
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/utils/image_utils.dart'; // Ensure this is correctly imported
import 'package:cached_network_image/cached_network_image.dart'; // For displaying network images

class BlockedAccountsScreen extends StatefulWidget {
  const BlockedAccountsScreen({super.key});

  @override
  State<BlockedAccountsScreen> createState() => _BlockedAccountsScreenState();
}

class _BlockedAccountsScreenState extends State<BlockedAccountsScreen> {
  User? _currentUser;
  List<User> _blockedUsers = [];
  bool _isLoading = true;
  final UserService _userService = UserService();

  @override
  void initState() {
    super.initState();
    _loadCurrentUserAndBlockedUsers();
  }

  Future<void> _loadCurrentUserAndBlockedUsers() async {
    _currentUser = await AuthService.getUser();
    if (_currentUser == null) {
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/login');
      }
      return;
    }
    await _fetchBlockedUsers();
  }

  Future<void> _fetchBlockedUsers() async {
    if (_currentUser == null) return;
    setState(() { _isLoading = true; });
    final response = await _userService.getBlockedUsers(_currentUser!.id);
    setState(() {
      _isLoading = false;
      if (response['success']) {
        _blockedUsers = response['blocked_users'];
      } else {
        _showSnackBar('Failed to load blocked users: ${response['message']}', isError: true);
      }
    });
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
      ),
    );
  }

  Future<void> _unblockUser(User userToUnblock) async {
    if (_currentUser == null || _currentUser!.id == null) {
      _showSnackBar('User not logged in.', isError: true);
      return;
    }

    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Unblock User?'),
          content: Text('Are you sure you want to unblock ${userToUnblock.fullName}?'),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('No'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Yes'),
            ),
          ],
        );
      },
    );

    if (confirm == true) {
      setState(() { _isLoading = true; }); // Show loading while processing
      final response = await _userService.unblockUser(_currentUser!.id, userToUnblock.id);
      setState(() { _isLoading = false; }); // Hide loading
      if (response['success']) {
        _showSnackBar('${userToUnblock.fullName} unblocked.');
        _fetchBlockedUsers(); // Refresh the list
      } else {
        _showSnackBar('Failed to unblock user: ${response['message']}', isError: true);
      }
    }
  }

  Widget _buildUserListTile(User user, {required String actionText, required VoidCallback onAction}) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 0.0),
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        leading: CircleAvatar(
          backgroundImage: ImageUtils.createProfileImageProvider(user.profilePictureUrl),
          child: (user.profilePictureUrl == null || user.profilePictureUrl!.isEmpty)
              ? const Icon(Icons.person, size: 24, color: Colors.white)
              : null,
        ),
        title: Text(user.fullName),
        subtitle: Text(user.email),
        trailing: TextButton(
          onPressed: onAction,
          child: Text(
            actionText,
            style: TextStyle(color: actionText == 'Unblock' ? Colors.green : Colors.red),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_currentUser == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Blocked Accounts'),
          backgroundColor: Constants.primaryColor,
          foregroundColor: Colors.white,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Blocked Accounts'),
        backgroundColor: Constants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Accounts you have blocked.',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _blockedUsers.isEmpty
                ? const Center(child: Text('No blocked accounts yet.'))
                : Expanded( // Use Expanded for ListView.builder in a Column
              child: ListView.builder(
                itemCount: _blockedUsers.length,
                itemBuilder: (context, index) {
                  final user = _blockedUsers[index];
                  return _buildUserListTile(
                    user,
                    actionText: 'Unblock',
                    onAction: () => _unblockUser(user),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
