<?php
// hanapp_backend/api/asap/add_tip.php
// Add tip to ASAP listing

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../../config/db_connect.php';

// Function to calculate transaction fee based on payment method
function calculateTransactionFee($doerFee, $paymentMethod, $cardType = null) {
    if (!$paymentMethod || $doerFee <= 0) {
        return 0.0;
    }

    $serviceFee = 0;
    $paymentMethodLower = strtolower(trim($paymentMethod));

    error_log("DEBUG - Fee calculation: Payment method='$paymentMethod', lowercased='$paymentMethodLower', Card type='" . ($cardType ?? 'NULL') . "', Doer fee=$doerFee");

    switch ($paymentMethodLower) {
        case 'gcash':
            $serviceFee = ($doerFee * 0.023) + 20; // 2.3% + ₱20
            break;
        case 'credit/debit card':
        case 'card':
        case 'philippine_card':
            $serviceFee = ($doerFee * 0.032) + 30; // 3.2% + ₱30 for Philippine cards
            break;
        case 'international_card':
            $serviceFee = ($doerFee * 0.042) + 30; // 4.2% + ₱30 for International cards
            break;
        case 'bpi':
        case 'bdo':
        case 'metrobank':
        case 'unionbank':
        case 'rcbc':
        case 'chinabank':
        case 'bank transfer':
            $serviceFee = ($doerFee * 0.01) + 20; // 1% + ₱20
            break;
        case 'hanapp balance':
        case 'hanapp_balance':
            $serviceFee = 0; // No fee for HanApp Balance
            break;
        default:
            $serviceFee = 0;
            break;
    }

    error_log("DEBUG - Final calculated fee: " . $serviceFee);
    return $serviceFee;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        throw new Exception('Invalid JSON input');
    }

    $listingId = $input['listing_id'] ?? null;
    $tipAmount = $input['tip_amount'] ?? null;

    if (!$listingId || !$tipAmount || $tipAmount <= 0) {
        throw new Exception('Missing or invalid parameters');
    }

    // Get the current listing details including payment method
    $selectQuery = "SELECT price, doer_fee, total_amount, transaction_fee, payment_method FROM asap_listings WHERE id = ? AND status = 'pending'";
    $selectStmt = $conn->prepare($selectQuery);
    $selectStmt->bind_param('i', $listingId);
    $selectStmt->execute();
    $result = $selectStmt->get_result();

    if ($result->num_rows === 0) {
        throw new Exception('Listing not found or not in pending status');
    }

    $listing = $result->fetch_assoc();
    $currentPrice = $listing['price'];
    $currentDoerFee = $listing['doer_fee'];
    $currentTotalAmount = $listing['total_amount'];
    $currentTransactionFee = $listing['transaction_fee'];
    $paymentMethod = $listing['payment_method'];

    // Calculate new doer fee with tip
    $newPrice = $currentPrice + $tipAmount;
    $newDoerFee = $currentDoerFee + $tipAmount;

    // Recalculate transaction fee based on new doer fee and payment method
    $newTransactionFee = calculateTransactionFee($newDoerFee, $paymentMethod);

    // Calculate new total amount
    $newTotalAmount = $newDoerFee + $newTransactionFee;

    error_log("add_tip.php: Adding tip ₱$tipAmount to listing $listingId");
    error_log("add_tip.php: Payment method: $paymentMethod");
    error_log("add_tip.php: Current price: ₱$currentPrice, new price: ₱$newPrice");
    error_log("add_tip.php: Current doer fee: ₱$currentDoerFee, new doer fee: ₱$newDoerFee");
    error_log("add_tip.php: Current transaction fee: ₱$currentTransactionFee, new transaction fee: ₱$newTransactionFee");
    error_log("add_tip.php: Current total amount: ₱$currentTotalAmount, new total amount: ₱$newTotalAmount");

    // Update the ASAP listing with new amounts (price, doer_fee, transaction_fee, and total_amount)
    $updateQuery = "UPDATE asap_listings SET price = ?, doer_fee = ?, transaction_fee = ?, total_amount = ?, updated_at = NOW() WHERE id = ? AND status = 'pending'";
    $updateStmt = $conn->prepare($updateQuery);
    $updateStmt->bind_param('ddddi', $newPrice, $newDoerFee, $newTransactionFee, $newTotalAmount, $listingId);

    if (!$updateStmt->execute()) {
        throw new Exception('Failed to update listing with tip');
    }

    if ($updateStmt->affected_rows === 0) {
        throw new Exception('Failed to update listing - no rows affected');
    }

    echo json_encode([
        'success' => true,
        'message' => 'Tip added successfully',
        'tip_amount' => $tipAmount,
        'new_price' => $newPrice,
        'new_doer_fee' => $newDoerFee,
        'new_transaction_fee' => $newTransactionFee,
        'new_total_amount' => $newTotalAmount,
        'payment_method' => $paymentMethod
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>