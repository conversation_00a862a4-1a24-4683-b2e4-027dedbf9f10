<?php
// reset_password_token.php
// Handles password reset using token from email link

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Function to send JSON response and exit
function sendJsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendJsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (json_last_error() !== JSON_ERROR_NONE) {
    sendJsonResponse(['success' => false, 'message' => 'Invalid JSON input'], 400);
}

if (!isset($input['token']) || empty($input['token']) ||
    !isset($input['new_password']) || empty($input['new_password'])) {
    sendJsonResponse(['success' => false, 'message' => 'Token and new password are required'], 400);
}

$token = $input['token'];
$new_password = $input['new_password'];

if (strlen($new_password) < 6) {
    sendJsonResponse(['success' => false, 'message' => 'Password must be at least 6 characters long'], 400);
}

// Database connection
require_once 'config/db_connect.php';

try {
    // Begin transaction
    $conn->begin_transaction();
    
    // Check if token exists and is valid
    $stmt = $conn->prepare("SELECT user_id, expires_at, used FROM password_reset_codes_email WHERE token = ?");
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();
    $tokenData = $result->fetch_assoc();
    $stmt->close();
    
    if (!$tokenData) {
        $conn->rollback();
        sendJsonResponse(['success' => false, 'message' => 'Invalid or expired token'], 400);
    }
    
    // Check if token is expired
    $expiresAt = new DateTime($tokenData['expires_at']);
    $now = new DateTime();
    
    if ($now > $expiresAt) {
        $conn->rollback();
        sendJsonResponse(['success' => false, 'message' => 'Token has expired'], 400);
    }
    
    // Check if token has been used
    if ($tokenData['used'] == 1) {
        $conn->rollback();
        sendJsonResponse(['success' => false, 'message' => 'Token has already been used'], 400);
    }
    
    // Hash the new password
    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
    
    // Update user's password
    $stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
    $stmt->bind_param("si", $hashed_password, $tokenData['user_id']);
    $stmt->execute();
    
    if ($stmt->affected_rows === 0) {
        $conn->rollback();
        sendJsonResponse(['success' => false, 'message' => 'User not found'], 404);
    }
    $stmt->close();
    
    // Mark token as used
    $stmt = $conn->prepare("UPDATE password_reset_codes_email SET used = 1 WHERE token = ?");
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $stmt->close();
    
    // Force logout from all devices for security
    $stmt = $conn->prepare("UPDATE users SET is_logged_in = 0 WHERE id = ?");
    $stmt->bind_param("i", $tokenData['user_id']);
    $stmt->execute();
    $stmt->close();
    
    // Commit transaction
    $conn->commit();
    
    sendJsonResponse([
        'success' => true, 
        'message' => 'Password updated successfully. Please log in with your new password.'
    ]);
    
} catch (Exception $e) {
    $conn->rollback();
    error_log("Password reset error: " . $e->getMessage());
    sendJsonResponse(['success' => false, 'message' => 'Database error occurred'], 500);
}

$conn->close();
?>