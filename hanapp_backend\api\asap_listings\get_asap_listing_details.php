<?php
// hanapp_backend/api/asap_listings/get_asap_listing_details.php
// Fetches details of a specific ASAP listing by its ID from the 'asap_listings' table.

require_once '../../config/db_connect.php'; // Database connection

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if (!isset($conn) || $conn->connect_error) {
    error_log("Database connection not established in get_asap_listing_details.php: " . $conn->connect_error);
    echo json_encode(["success" => false, "message" => "Database connection not established."]);
    exit();
}

$listingId = $_GET['listing_id'] ?? null;

if (empty($listingId) || !is_numeric($listingId)) {
    echo json_encode(["success" => false, "message" => "Listing ID is required and must be a number."]);
    exit();
}

$stmt = $conn->prepare("SELECT
    al.id, al.lister_id, al.title, al.description,
    CASE 
        WHEN al.is_open_price = 1 AND apo.offered_price IS NOT NULL THEN apo.offered_price
        ELSE al.price
    END as price,
    al.latitude, al.longitude,
    al.location_address, al.preferred_doer_gender, al.pictures_urls,
    al.doer_fee, al.transaction_fee, al.total_amount, al.payment_method, al.status,
    al.is_active, al.is_open_price,
    al.created_at, al.updated_at,
    u.full_name as lister_full_name, u.profile_picture_url as lister_profile_picture_url
FROM
    asap_listings al
JOIN
    users u ON al.lister_id = u.id
LEFT JOIN
    asap_price_offers apo ON al.id = apo.listing_id AND apo.status = 'accepted'
WHERE
    al.id = ?");

if ($stmt === false) {
    error_log("Failed to prepare ASAP listing details statement: " . $conn->error);
    echo json_encode(["success" => false, "message" => "Internal server error."]);
    exit();
}

$stmt->bind_param("i", $listingId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $listing = $result->fetch_assoc();
    $stmt->close();

    // Handle price display logic based on is_open_price
    // Always display the actual price, but add indicator for open price listings
    $listing['display_price'] = (float)$listing['price'];
    $listing['price_text'] = 'Php ' . number_format((float)$listing['price'], 2);
    $listing['is_open_price_indicator'] = ($listing['is_open_price'] == 1);
    $listing['open_price_text'] = ($listing['is_open_price'] == 1) ? 'Open Price' : null;

    // Add JSON encoding validation
    try {
        $jsonResponse = json_encode(
            ["success" => true, "listing" => $listing],
            JSON_THROW_ON_ERROR | JSON_INVALID_UTF8_SUBSTITUTE | JSON_PARTIAL_OUTPUT_ON_ERROR
        );
        header('Content-Length: ' . strlen($jsonResponse));
        header('Content-Type: application/json; charset=utf-8');
        echo $jsonResponse;
        exit; // Ensure no further output
    } catch (JsonException $e) {
        error_log("JSON encoding error: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(["success" => false, "message" => "Data serialization error"]);
        exit;
    }
} else {
    // Ensure valid JSON even for error cases
    echo json_encode(["success" => false, "message" => "ASAP listing not found."], JSON_THROW_ON_ERROR);
}

// Add cleanup before exit
$conn->close();
ob_end_flush();
?>
