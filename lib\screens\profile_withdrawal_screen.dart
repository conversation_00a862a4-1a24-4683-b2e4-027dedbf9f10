import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:hanapp/models/user.dart';
import 'package:hanapp/utils/auth_service.dart';
import 'package:hanapp/services/cash_out_service.dart';
import 'package:hanapp/services/currency_service.dart';
import 'package:hanapp/services/verification_service.dart';
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/utils/api_config.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class ProfileWithdrawalScreen extends StatefulWidget {
  const ProfileWithdrawalScreen({Key? key}) : super(key: key);

  @override
  State<ProfileWithdrawalScreen> createState() => _ProfileWithdrawalScreenState();
}

class _ProfileWithdrawalScreenState extends State<ProfileWithdrawalScreen> {
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _accountDetailsController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  User? _currentUser;
  String? _selectedWithdrawalMethod;
  String? _selectedBank;
  bool _isLoading = false;
  bool _isLoadingUserData = true;
  double _totalProfit = 0.0;
  bool _isVerified = false;
  CardType _detectedCardType = CardType.unknown;
  double _currentUsdToPhpRate = 56.0; // Default fallback rate
  String _lastRateUpdate = 'Not yet loaded'; // Track when rate was last updated

  final CashOutService _cashOutService = CashOutService();
  final VerificationService _verificationService = VerificationService();

  @override
  void initState() {
    super.initState();
    print('🏦 Profile Withdrawal Screen initialized');
    _loadUserData();
    _loadExchangeRate();
  }

  Future<void> _loadExchangeRate() async {
    try {
      final rateInfo = await CurrencyService.getExchangeRateInfo();
      final rate = await CurrencyService.getUsdToPhpRate();

      setState(() {
        _currentUsdToPhpRate = rate;
        _lastRateUpdate = rateInfo['last_updated'] ?? 'Unknown';
      });

      print('Withdrawal: Updated USD/PHP rate to: $rate');
      print('Withdrawal: Rate last updated: ${rateInfo['last_updated']}');
      print('Withdrawal: Hours since update: ${rateInfo['hours_since_update']}');
      print('Withdrawal: Auto-updated from database: ${rateInfo['auto_updated']}');
    } catch (e) {
      print('Withdrawal: Failed to load exchange rate: $e');
      setState(() {
        _lastRateUpdate = 'Failed to load';
      });
      // Keep using fallback rate
    }
  }





  void _showRateInfo() async {
    final rateInfo = await CurrencyService.getExchangeRateInfo();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Exchange Rate Information'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Current Rate: \$1 = ₱${_currentUsdToPhpRate.toStringAsFixed(4)}'),
            const SizedBox(height: 4),
            Text('Inverse Rate: ₱1 = \$${(1.0 / _currentUsdToPhpRate).toStringAsFixed(6)}'),
            const SizedBox(height: 8),
            Text('Last Updated: ${rateInfo['last_updated']}'),
            const SizedBox(height: 8),
            Text('Source: ${rateInfo['source'] ?? 'Database'}'),
            const SizedBox(height: 8),

            Row(
              children: [
                Icon(
                  rateInfo['is_recent'] == true ? Icons.check_circle : Icons.warning,
                  color: rateInfo['is_recent'] == true ? Colors.green : Colors.orange,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  rateInfo['is_recent'] == true ? 'Rate is current' : 'Rate may be outdated',
                  style: TextStyle(
                    color: rateInfo['is_recent'] == true ? Colors.green : Colors.orange,
                  ),
                ),
              ],
            ),

          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _amountController.dispose();
    _accountDetailsController.dispose();
    super.dispose();
  }

  /// Fetch fresh total_profit from API (similar to HanAppBalanceService approach)
  Future<double> _fetchFreshTotalProfit(int userId) async {
    try {
      final url = Uri.parse('${ApiConfig.baseUrl}/get_user_profile.php').replace(
        queryParameters: {'user_id': userId.toString()}
      );

      print('ProfileWithdrawal: Fetching fresh total_profit from: $url');
      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        print('ProfileWithdrawal: API response: $data');

        if (data['success'] && data['user'] != null) {
          final totalProfit = (data['user']['total_profit'] as num).toDouble();
          print('ProfileWithdrawal: Fresh total_profit from API: ₱$totalProfit');
          return totalProfit;
        } else {
          print('ProfileWithdrawal: API error: ${data['message']}');
          return 0.0;
        }
      } else {
        print('ProfileWithdrawal: HTTP error: ${response.statusCode}');
        return 0.0;
      }
    } catch (e) {
      print('ProfileWithdrawal: Exception fetching total_profit: $e');
      return 0.0;
    }
  }

  Future<void> _loadUserData() async {
    print('🔄 Loading user data for profile withdrawal...');
    setState(() {
      _isLoadingUserData = true;
    });

    try {
      // Get user data (we need the user ID)
      print('ProfileWithdrawal: Getting user data for ID...');
      final user = await AuthService.getUser(); // Use getUser() instead of refreshUserData()

      if (user != null && user.id != null) {
        print('✅ User data loaded successfully:');
        print('   User ID: ${user.id}');
        print('   Full Name: ${user.fullName}');
        print('   Cached Total Profit: ₱${user.totalProfit ?? 0.0}');
        print('   Role: ${user.role}');
        print('   Verification Status: ${user.verificationStatus}');

        setState(() {
          _currentUser = user;
        });

        // Now fetch fresh total_profit from API (like HanApp Balance does)
        print('ProfileWithdrawal: Fetching fresh total_profit from API...');
        final freshTotalProfit = await _fetchFreshTotalProfit(user.id!);

        setState(() {
          _totalProfit = freshTotalProfit;
        });

        print('✅ Fresh total_profit loaded: ₱$freshTotalProfit');

        print('ProfileWithdrawal: Calling _verificationService.getVerificationStatus()...');
        try {
          final response = await _verificationService.getVerificationStatus(userId: user.id!);
          print('ProfileWithdrawal: getVerificationStatus() completed successfully');

          if (response['success']) {
            final statusData = response['status_data'];

            // ===== DETAILED VERIFICATION STATUS LOGGING  =====
            print('======= WITHDRAWAL VERIFICATION STATUS DEBUG =======');
            print('Raw API Response: $response');
            print('Status Data: $statusData');
            print('--- VERIFICATION FIELDS ---');
            print('verification_status: "${statusData['verification_status']}" (Type: ${statusData['verification_status'].runtimeType})');
            print('badge_status: "${statusData['badge_status']}" (Type: ${statusData['badge_status'].runtimeType})');
            print('id_verified: ${statusData['id_verified']} (Type: ${statusData['id_verified'].runtimeType})');
            print('badge_acquired: ${statusData['badge_acquired']} (Type: ${statusData['badge_acquired'].runtimeType})');
            print('--- PHOTO URLS ---');
            print('id_photo_front_url: ${statusData['id_photo_front_url'] ?? 'None'}');
            print('id_photo_back_url: ${statusData['id_photo_back_url'] ?? 'None'}');
            print('brgy_clearance_photo_url: ${statusData['brgy_clearance_photo_url'] ?? 'None'}');
            print('live_photo_url: ${statusData['live_photo_url'] ?? 'None'}');
            print('=========================================');
            // ===== END VERIFICATION STATUS LOGGING =====

            setState(() {
              // Use the same logic as verification screen - only verified if status is exactly 'verified'
              final verificationStatus = statusData['verification_status'] ?? 'unverified';
              _isVerified = verificationStatus == 'verified';

              print('ProfileWithdrawal: Final verification status: "$verificationStatus"');
              print('ProfileWithdrawal: Final _isVerified: $_isVerified');
            });
          } else {
            print('ProfileWithdrawal: getVerificationStatus() failed: ${response['message']}');
            // Fallback to user model data
            setState(() {
              _isVerified = user.verificationStatus == 'verified';
              print('ProfileWithdrawal: Using fallback verification status: "${user.verificationStatus}"');
              print('ProfileWithdrawal: Fallback _isVerified: $_isVerified');
            });
          }
        } catch (e) {
          print('ProfileWithdrawal: ERROR in getVerificationStatus(): $e');
          // Fallback to user model data
          setState(() {
            _isVerified = user.verificationStatus == 'verified';
            print('ProfileWithdrawal: Using fallback verification status due to error: "${user.verificationStatus}"');
            print('ProfileWithdrawal: Fallback _isVerified: $_isVerified');
          });
        }
      } else {
        // Fallback to cached user if server fails
        print('❌ Server refresh failed, using cached data...');
        final cachedUser = await AuthService.getUser();
        if (cachedUser != null) {
          print('📱 Using cached user data:');
          print('   User ID: ${cachedUser.id}');
          print('   Total Profit: ₱${cachedUser.totalProfit ?? 0.0}');
          print('   Verification Status: ${cachedUser.verificationStatus}');

          setState(() {
            _currentUser = cachedUser;
            _totalProfit = cachedUser.totalProfit ?? 0.0;
            _isVerified = cachedUser.verificationStatus == 'verified';
            print('ProfileWithdrawal: Using cached user verification status: "${cachedUser.verificationStatus}"');
            print('ProfileWithdrawal: Cached _isVerified: $_isVerified');
          });
        }
      }
    } catch (e) {
      print('Error loading user data: $e');
      _showSnackBar('Failed to load user data', isError: true);
    } finally {
      setState(() {
        _isLoadingUserData = false;
      });
    }
  }

  Future<void> _submitWithdrawal() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedWithdrawalMethod == null) {
      _showSnackBar('Please select a withdrawal method', isError: true);
      return;
    }

    final amount = double.tryParse(_amountController.text) ?? 0.0;
    final accountDetails = _accountDetailsController.text.trim();

    // Check if amount exceeds maximum withdrawal limit
    if (amount > CashOutService.getMaximumAmount()) {
      _showSnackBar('Cannot withdraw more than ₱${NumberFormat('#,##0.00').format(CashOutService.getMaximumAmount())}', isError: true);
      return;
    }

    // Validate account details based on method
    if (!CashOutService.isValidAccountDetails(_selectedWithdrawalMethod!, accountDetails)) {
      _showSnackBar('Please enter valid account details for ${CashOutService.formatWithdrawalMethod(_selectedWithdrawalMethod!)}', isError: true);
      return;
    }

    // Show withdrawal summary instead of directly processing
    _showWithdrawalSummary(amount, accountDetails);
  }

  Future<void> _processWithdrawal(double requestedAmount, String accountDetails) async {
    setState(() {
      _isLoading = true;
    });

    // Calculate fee breakdown
    final feeBreakdown = _calculateWithdrawalFees(requestedAmount, _selectedWithdrawalMethod!);
    final netAmount = feeBreakdown['netAmount']!; // Amount user will receive
    final currency = feeBreakdown['currency']!; // Currency of withdrawal
    final deductedAmountPhp = feeBreakdown['deductedAmountPhp']!; // Amount deducted from PHP profit
    final serviceFeePhp = feeBreakdown['serviceFeePhp'] ?? feeBreakdown['serviceFee']!; // Service fee in PHP

    // Determine the actual withdrawal method and clean account details
    String actualWithdrawalMethod = _selectedWithdrawalMethod!;
    String cleanAccountDetails = accountDetails;

    if (_selectedWithdrawalMethod == 'Bank Transfer' && _selectedBank != null) {
      // Use specific bank name instead of "Bank Transfer"
      actualWithdrawalMethod = _getSelectedBankName();
      // Remove any bank prefix from account details, keep only the account number
      cleanAccountDetails = accountDetails.replaceAll(RegExp(r'^.*?\s*-\s*'), '').trim();
    }

    print('DEBUG: Original method: "$_selectedWithdrawalMethod"');
    print('DEBUG: Selected bank: "$_selectedBank"');
    print('DEBUG: Final method: "$actualWithdrawalMethod"');
    print('DEBUG: Final account details: "$cleanAccountDetails"');
    print('DEBUG: Requested amount: $requestedAmount, Net amount: $netAmount $currency, Service fee: $serviceFeePhp PHP');

    try {
      final result = await _cashOutService.createCashOutRequest(
        userId: _currentUser!.id,
        amount: netAmount, // Net amount user will receive
        requestedAmount: requestedAmount, // Original amount user requested (before fees)
        serviceFee: serviceFeePhp, // Service fee charged for the withdrawal
        currency: currency, // Currency of withdrawal
        withdrawalMethod: actualWithdrawalMethod,
        accountDetails: cleanAccountDetails,
      );

      if (result['success']) {
        _showSnackBar('Transaction complete. The amount will be credited soon.');

        // Clear form
        _amountController.clear();
        _accountDetailsController.clear();
        setState(() {
          _selectedWithdrawalMethod = null;
          _detectedCardType = CardType.unknown; // Reset card type detection
        });

        // Reload user data to get updated profit
        await _loadUserData();
      } else {
        _showSnackBar(result['message'] ?? 'Withdrawal failed', isError: true);
      }
    } catch (e) {
      _showSnackBar('Network error: ${e.toString()}', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Handle pull-to-refresh
  Future<void> _onRefresh() async {
    print('🔄 Pull-to-refresh triggered - refreshing withdrawal data...');

    try {
      // Reload user data and fresh total_profit
      await _loadUserData();

      // Also reload exchange rate
      await _loadExchangeRate();

      print('✅ Pull-to-refresh completed successfully');
      // _showSnackBar('Balance updated successfully');
    } catch (e) {
      print('❌ Pull-to-refresh failed: $e');
      _showSnackBar('Failed to refresh data', isError: true);
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: Duration(seconds: isError ? 4 : 3),
      ),
    );
  }

  void _updateBankAccountDetails() {
    if (_selectedBank != null) {
      // Clear the account details when bank is selected
      // User will enter only the account number
      _accountDetailsController.clear();
    }
  }

  String _getSelectedBankName() {
    if (_selectedBank != null) {
      final withdrawalBanks = [
        {'id': 'bpi', 'name': 'BPI'},
        {'id': 'chinabank', 'name': 'China Bank'},
        {'id': 'rcbc', 'name': 'RCBC'},
        {'id': 'unionbank', 'name': 'UnionBank'},
      ];

      final selectedBankData = withdrawalBanks.firstWhere(
            (bank) => bank['id'] == _selectedBank,
        orElse: () => {'id': '', 'name': ''},
      );

      return selectedBankData['name'] ?? '';
    }
    return '';
  }



  String _getBankTransferHintText() {
    if (_selectedWithdrawalMethod == 'Bank Transfer' && _selectedBank != null) {
      return 'Please enter your account number';
    }
    return CashOutService.getAccountDetailsPlaceholder(_selectedWithdrawalMethod!);
  }

  String _getBankTransferLabel() {
    if (_selectedBank != null) {
      final bankName = _getSelectedBankName();
      return '$bankName (**********)';
    }
    return 'Bank Transfer (**********)';
  }

  List<TextInputFormatter> _getCreditCardFormatters() {
    return [
      FilteringTextInputFormatter.digitsOnly,
      LengthLimitingTextInputFormatter(19), // Max 19 digits for credit cards
      _CreditCardFormatter(),
    ];
  }

  List<TextInputFormatter> _getAmountFormatters() {
    return [
      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
    ];
  }

  // Calculate withdrawal fees based on method and card type
  Map<String, dynamic> _calculateWithdrawalFees(double amount, String method) {
    double platformFeePercentage = 0.15; // 15% platform fee on total amount
    double processingFeePercentage = 0.0;
    double fixedFee = 0.0;
    String currency = 'PHP'; // Default currency
    double netAmount = 0.0;

    switch (method) {
      case 'GCash':
        processingFeePercentage = 0.023; // 2.3%
        fixedFee = 0.0; // No fixed fee
        currency = 'PHP';
        break;
      case 'Credit Card':
        if (_detectedCardType == CardType.philippine) {
          processingFeePercentage = 0.032; // 3.2%
          fixedFee = 30.0; // ₱10 + ₱20 = ₱30
          currency = 'PHP';
        } else if (_detectedCardType == CardType.internationalPhp) {
          processingFeePercentage = 0.042; // 4.2%
          fixedFee = 30.0; // ₱10 + ₱20 = ₱30
          currency = 'PHP';
        } else if (_detectedCardType == CardType.internationalUsd) {
          processingFeePercentage = 0.04; // 4%
          fixedFee = 1.0; // $1 USD (₱20 will be added separately)
          currency = 'USD';
        } else {
          processingFeePercentage = 0.032; // Default to Philippine rate
          fixedFee = 30.0;
          currency = 'PHP';
        }
        break;
      case 'Bank Transfer':
        processingFeePercentage = 0.01; // 1%
        fixedFee = 0.0; // No fixed fee
        currency = 'PHP';
        break;
      default:
        processingFeePercentage = 0.01; // Default
        fixedFee = 0.0; // No fixed fee for default
        currency = 'PHP';
        break;
    }

    if (currency == 'USD') {
      // For USD cards: user enters PHP amount, calculate fees based on new structure
      final amountInUsd = amount / _currentUsdToPhpRate; // Convert PHP input to USD

      // Calculate platform fee (15% of total amount)
      final platformFeePhp = amount * platformFeePercentage;

      // Calculate processing fee (4% of USD amount)
      final processingFeeUsd = amountInUsd * processingFeePercentage;
      final processingFeePhp = processingFeeUsd * _currentUsdToPhpRate;

      // Fixed fee ($1 USD converted to PHP + ₱20)
      final fixedFeePhp = (fixedFee * _currentUsdToPhpRate) + 20.0;

      // Total service fee in PHP
      final totalServiceFeePhp = platformFeePhp + processingFeePhp + fixedFeePhp;

      // Convert total fee to USD for display
      final totalServiceFeeUsd = totalServiceFeePhp / _currentUsdToPhpRate;

      // Calculate net amount in USD
      final netAmountUsd = amountInUsd - totalServiceFeeUsd;

      // Original PHP amount to deduct from profit
      final deductedAmountPhp = amount;

      return {
        'serviceFee': totalServiceFeeUsd, // Total service fee in USD for display
        'serviceFeePhp': totalServiceFeePhp, // Total service fee in PHP
        'platformFeePhp': platformFeePhp, // 15% platform fee
        'processingFeePhp': processingFeePhp, // 4% processing fee
        'platformFeePercentage': platformFeePercentage,
        'processingFeePercentage': processingFeePercentage,
        'fixedFee': fixedFee, // $1 USD
        'fixedFeePhp': fixedFeePhp, // $1 converted to PHP
        'totalFees': totalServiceFeeUsd,
        'netAmount': netAmountUsd,
        'requestedAmount': amount, // Original PHP amount
        'requestedAmountUsd': amountInUsd, // USD equivalent
        'currency': currency,
        'deductedAmountPhp': deductedAmountPhp,
        'usdToPhpRate': _currentUsdToPhpRate,
      };
    } else {
      // For PHP cards: calculate everything in PHP based on new fee structure
      // Calculate platform fee (15% of total amount)
      final platformFee = amount * platformFeePercentage;

      // Calculate processing fee (varies by method)
      final processingFee = amount * processingFeePercentage;

      // Total service fee
      final totalServiceFee = platformFee + processingFee + fixedFee;

      // Calculate net amount
      netAmount = amount - totalServiceFee;

      return {
        'serviceFee': totalServiceFee,
        'platformFee': platformFee,
        'processingFee': processingFee,
        'platformFeePercentage': platformFeePercentage,
        'processingFeePercentage': processingFeePercentage,
        'fixedFee': fixedFee,
        'totalFees': totalServiceFee,
        'netAmount': netAmount,
        'requestedAmount': amount,
        'currency': currency,
        'deductedAmountPhp': amount, // Same as requested amount for PHP
      };
    }
  }

  String _getWithdrawalMethodDisplayName() {
    if (_selectedWithdrawalMethod == 'Bank Transfer' && _selectedBank != null) {
      return _getSelectedBankName();
    }
    if (_selectedWithdrawalMethod == 'Credit Card') {
      return '${_selectedWithdrawalMethod} (${CardTypeDetector.getCardTypeName(_detectedCardType)})';
    }
    return _selectedWithdrawalMethod ?? '';
  }



  void _showWithdrawalSummary(double amount, String accountDetails) {
    final feeBreakdown = _calculateWithdrawalFees(amount, _selectedWithdrawalMethod!);
    final methodDisplayName = _getWithdrawalMethodDisplayName();
    final currency = feeBreakdown['currency']!;
    final currencySymbol = currency == 'USD' ? '\$' : '₱';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Text(
          'Withdrawal Summary',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Withdrawal Method - Compact
              Text(
                'Withdrawal Method: $methodDisplayName',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.blue.shade700,
                ),
              ),
              const SizedBox(height: 12),

              // Amount Breakdown - Compact
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  children: [
                    // 1. Requested Amount (always show in PHP since that's what user entered)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('Requested Amount:', style: TextStyle(fontSize: 13)),
                        Text(
                          '₱${feeBreakdown['requestedAmount']!.toStringAsFixed(2)} PHP',
                          style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),

                    // 2. USD equivalent (only for USD cards)
                    if (currency == 'USD') ...[
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('USD equivalent:', style: TextStyle(fontSize: 13)),
                          Text(
                            '\$${feeBreakdown['requestedAmountUsd']!.toStringAsFixed(2)} USD',
                            style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
                          ),
                        ],
                      ),
                      const SizedBox(height: 6),
                    ],

                    // 3. Service Fee
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                            'Service Fee:',
                            style: TextStyle(fontSize: 13)
                        ),
                        Text(
                          '-₱${feeBreakdown['serviceFeePhp']?.toStringAsFixed(2) ?? feeBreakdown['serviceFee']!.toStringAsFixed(2)} PHP',
                          style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500, color: Colors.red),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),

                    // 4. Net amount in USD (only for USD cards)
                    if (currency == 'USD') ...[
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Net amount in USD:', style: TextStyle(fontSize: 13)),
                          Text(
                            '\$${feeBreakdown['netAmount']!.toStringAsFixed(2)} USD',
                            style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500, color: Colors.green),
                          ),
                        ],
                      ),
                    ],

                    const SizedBox(height: 6),

                    // Divider
                    Container(
                      height: 1,
                      color: Colors.grey.shade300,
                      margin: const EdgeInsets.symmetric(vertical: 6),
                    ),

                    // Net Amount (what user receives)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'You will receive:',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '$currencySymbol${feeBreakdown['netAmount']!.toStringAsFixed(2)} $currency',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Constants.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),

              // Compact Instructions
              Text(
                'Click "Confirm Withdrawal" to submit your request. $currencySymbol${feeBreakdown['netAmount']!.toStringAsFixed(2)} $currency will be transferred to your account after admin approval.',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: Colors.grey.shade600,
            ),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _processWithdrawal(amount, accountDetails);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Constants.primaryColor,
              foregroundColor: Colors.white,
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text(
              'Confirm Withdrawal',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }


  Widget _buildWithdrawalMethodOption(String title, String value, String icon) {
    final isSelected = _selectedWithdrawalMethod == value;

    return InkWell(
      onTap: () {
        setState(() {
          _selectedWithdrawalMethod = isSelected ? null : value;
          // Clear account details when changing method
          if (!isSelected) {
            _accountDetailsController.clear();
            // Set default card type for Credit Card
            if (value == 'Credit Card') {
              _detectedCardType = CardType.philippine; // Default to Philippine
            } else {
              _detectedCardType = CardType.unknown;
            }
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isSelected ? Constants.primaryColor.withOpacity(0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: isSelected ? Constants.primaryColor : Colors.transparent,
                border: isSelected ? null : Border.all(color: Colors.grey.shade400, width: 2),
                shape: BoxShape.circle,
              ),
              child: isSelected
                  ? const Icon(
                Icons.check,
                color: Colors.white,
                size: 16,
              )
                  : null,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: isSelected ? Constants.primaryColor : Colors.black87,
                ),
              ),
            ),
            if (value == 'bank_transfer')
              Icon(
                isSelected ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                color: isSelected ? Constants.primaryColor : Colors.grey.shade600,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBankTransferOption() {
    final isSelected = _selectedWithdrawalMethod == 'Bank Transfer';

    // List of banks for withdrawal
    final withdrawalBanks = [
      {'id': 'bpi', 'name': 'BPI Direct Debit', 'icon': '🏦'},
      {'id': 'chinabank', 'name': 'China Bank Direct Debit', 'icon': '🏦'},
      {'id': 'rcbc', 'name': 'RCBC Direct Debit', 'icon': '🏦'},
      {'id': 'unionbank', 'name': 'UBP Direct Debit', 'icon': '🏦'},
    ];

    return Column(
      children: [
        InkWell(
          onTap: () {
            setState(() {
              if (_selectedWithdrawalMethod == 'Bank Transfer') {
                _selectedWithdrawalMethod = null;
                _selectedBank = null;
              } else {
                _selectedWithdrawalMethod = 'Bank Transfer';
              }
              _accountDetailsController.clear();
              _detectedCardType = CardType.unknown; // Not applicable for bank transfers
            });
          },
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: isSelected ? Constants.primaryColor.withOpacity(0.1) : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: isSelected ? Constants.primaryColor : Colors.transparent,
                    border: isSelected ? null : Border.all(color: Colors.grey.shade400, width: 2),
                    shape: BoxShape.circle,
                  ),
                  child: isSelected
                      ? const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 16,
                  )
                      : null,
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Text(
                    'Bank Transfer',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Icon(
                  isSelected ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                  color: isSelected ? Constants.primaryColor : Colors.grey.shade600,
                ),
              ],
            ),
          ),
        ),

        // Bank options (shown when Bank Transfer is selected)
        if (isSelected) ...[
          Container(
            margin: const EdgeInsets.only(left: 20, right: 20, bottom: 10),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              children: withdrawalBanks.map((bank) {
                return InkWell(
                  onTap: () {
                    setState(() {
                      _selectedBank = bank['id'];
                      _updateBankAccountDetails();
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                    margin: const EdgeInsets.only(bottom: 8),
                    decoration: BoxDecoration(
                      color: _selectedBank == bank['id'] ? Constants.primaryColor.withOpacity(0.1) : Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: _selectedBank == bank['id'] ? Constants.primaryColor : Colors.grey.shade300,
                      ),
                    ),
                    child: Row(
                      children: [
                        Text(bank['icon']!, style: const TextStyle(fontSize: 20)),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            bank['name']!,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: _selectedBank == bank['id'] ? Constants.primaryColor : Colors.black87,
                            ),
                          ),
                        ),
                        if (_selectedBank == bank['id'])
                          Icon(
                            Icons.check_circle,
                            color: Constants.primaryColor,
                            size: 20,
                          ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ],
    );
  }

  String _getWithdrawalMethodDescription(String method) {
    switch (method) {
      case 'Bank Transfer':
        return 'Direct transfer to your bank account';
      case 'BPI':
      case 'China Bank':
      case 'RCBC':
      case 'UnionBank':
        return 'Direct transfer to your $method account';
      case 'GCash':
        return 'Instant transfer to your GCash wallet';
      case 'Maya':
        return 'Instant transfer to your Maya wallet';
      case 'Credit Card':
        return 'Transfer to your credit/debit card';
      default:
        return 'Secure withdrawal method';
    }
  }

  Widget _buildDivider() {
    return Container(
      height: 1,
      color: Colors.grey.shade200,
      margin: const EdgeInsets.symmetric(horizontal: 20),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'Withdrawal',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Constants.primaryColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: _isLoadingUserData
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _onRefresh,
              color: Constants.primaryColor,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                physics: const AlwaysScrollableScrollPhysics(), // Ensures pull-to-refresh works even when content doesn't fill screen
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
              // Balance Card
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Constants.primaryColor, Constants.primaryColor.withOpacity(0.8)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Constants.primaryColor.withOpacity(0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Available Balance',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '₱${NumberFormat('#,##0.00').format(_totalProfit)}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          _isVerified ? Icons.verified : Icons.warning,
                          color: _isVerified ? Colors.white : Colors.orange,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _isVerified ? 'Verified Account' : 'Verification Required',
                          style: TextStyle(
                            color: _isVerified ? Colors.white70 : Colors.orange,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Verification Warning (if not verified)
              if (!_isVerified) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade50,
                    border: Border.all(color: Colors.orange.shade200),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(Icons.warning, color: Colors.orange.shade700),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'Account Verification Required',
                              style: TextStyle(
                                color: Colors.orange.shade700,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'You need to verify your account before you can withdraw funds. Please complete the verification process first.',
                        style: TextStyle(
                          color: Colors.orange.shade600,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 12),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pushNamed('/verification');
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange.shade700,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('Go to Verification'),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
              ],

              // Withdrawal Form (only show if verified)
              if (_isVerified) ...[
                // Amount Input
                Text(
                  'Withdrawal Amount',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Constants.textColor,
                  ),
                ),
                const SizedBox(height: 16),

                // Amount Input Container (matching cash-in design)
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Enter Amount',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey.shade700,
                          ),
                        ),
                        const SizedBox(height: 12),
                        TextFormField(
                          controller: _amountController,
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          inputFormatters: _getAmountFormatters(),
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                          decoration: InputDecoration(
                            hintText: '0.00',
                            hintStyle: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade400,
                            ),
                            prefixText: '₱ ',
                            prefixStyle: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.zero,
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter withdrawal amount';
                            }
                            final amount = double.tryParse(value);
                            if (amount == null) {
                              return 'Please enter a valid amount';
                            }
                            if (!CashOutService.isValidAmount(amount)) {
                              return 'Amount must be between ₱${NumberFormat('#,##0.00').format(CashOutService.getMinimumAmount())} and ₱${NumberFormat('#,##0.00').format(CashOutService.getMaximumAmount())}';
                            }
                            if (amount > _totalProfit) {
                              return 'Insufficient balance';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Minimum: ₱${CashOutService.getMinimumAmount().toStringAsFixed(0)} • Available: ₱${_totalProfit.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Withdrawal Methods
                Text(
                  'Withdrawal Method',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Constants.textColor,
                  ),
                ),
                const SizedBox(height: 16),

                // Payment Methods Container (matching cash-in design)
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      _buildWithdrawalMethodOption('GCash E-Wallet', 'GCash', '💳'),
                      _buildDivider(),
                      // _buildWithdrawalMethodOption('Credit/Debit Card', 'Credit Card', '💳'),
                      // _buildDivider(),
                      _buildBankTransferOption(),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Account Details Input
                if (_selectedWithdrawalMethod != null) ...[
                  const SizedBox(height: 24),
                  Text(
                    'Account Details',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Constants.textColor,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Account Details Container (matching design)
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            CashOutService.formatWithdrawalMethod(_selectedWithdrawalMethod!),
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey.shade700,
                            ),
                          ),
                          const SizedBox(height: 12),
                          // Professional separator line
                          Container(
                            height: 1,
                            color: Colors.grey.shade300,
                            margin: const EdgeInsets.only(bottom: 12),
                          ),
                          TextFormField(
                            controller: _accountDetailsController,
                            keyboardType: _selectedWithdrawalMethod == 'Credit Card' ? TextInputType.number : TextInputType.text,
                            inputFormatters: _selectedWithdrawalMethod == 'Credit Card' ? _getCreditCardFormatters() : null,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                            decoration: InputDecoration(
                              hintText: _selectedWithdrawalMethod == 'Bank Transfer' ? 'Please enter your account number' : _getBankTransferHintText(),
                              hintStyle: TextStyle(
                                fontSize: 16,
                                color: Colors.grey.shade400,
                              ),
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.zero,
                            ),
                            onChanged: (value) {
                              // No automatic detection - user will select card type manually
                            },
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Please enter account details';
                              }
                              if (!CashOutService.isValidAccountDetails(_selectedWithdrawalMethod!, value)) {
                                return 'Please enter valid account details';
                              }
                              return null;
                            },
                          ),

                          // Card Type Selection (only for Credit Cards) - COMMENTED OUT
                          /*
                          if (_selectedWithdrawalMethod == 'Credit Card') ...[
                            const SizedBox(height: 16),
                            Text(
                              'Card Type',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey.shade800,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade300),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Column(
                                children: [
                                  // Philippine Card Option
                                  RadioListTile<CardType>(
                                    title: const Text('Philippine Bank Card'),
                                    subtitle: Text(
                                      'Service fee: 18.2% + ₱30',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey.shade600,
                                      ),
                                    ),
                                    value: CardType.philippine,
                                    groupValue: _detectedCardType,
                                    onChanged: (CardType? value) {
                                      setState(() {
                                        _detectedCardType = value!;
                                      });
                                    },
                                    activeColor: Colors.green,
                                  ),
                                  Divider(height: 1, color: Colors.grey.shade300),

                                  // International PHP Card Option
                                  RadioListTile<CardType>(
                                    title: const Text('International Card (PHP-billed)'),
                                    subtitle: Text(
                                      'Service fee: 19.2% + ₱30',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey.shade600,
                                      ),
                                    ),
                                    value: CardType.internationalPhp,
                                    groupValue: _detectedCardType,
                                    onChanged: (CardType? value) {
                                      setState(() {
                                        _detectedCardType = value!;
                                      });
                                    },
                                    activeColor: Colors.orange,
                                  ),
                                  Divider(height: 1, color: Colors.grey.shade300),

                                  // International USD Card Option
                                  RadioListTile<CardType>(
                                    title: const Text('International Card (USD-billed)'),
                                    subtitle: Text(
                                      'Service fee: 19% + ₱${(_currentUsdToPhpRate + 20).toStringAsFixed(0)}',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey.shade600,
                                      ),
                                    ),
                                    value: CardType.internationalUsd,
                                    groupValue: _detectedCardType,
                                    onChanged: (CardType? value) {
                                      setState(() {
                                        _detectedCardType = value!;
                                      });
                                    },
                                    activeColor: Colors.blue,
                                  ),
                                ],
                              ),
                            ),
                          ],
                          */


                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Submit Button (matching cash-in design)
                  SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _submitWithdrawal,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Constants.primaryColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        elevation: 0,
                      ),
                      child: _isLoading
                          ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                          : Text(
                        _amountController.text.isNotEmpty
                            ? 'Withdraw ₱${_amountController.text}'
                            : 'Withdraw',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ],

              const SizedBox(height: 24),

              // Info Card
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                  border: Border.all(
                    color: Colors.blue.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: Colors.blue.shade700),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Withdrawal Information',
                            style: TextStyle(
                              color: Colors.blue.shade700,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        // Rate info button
                        /*
                        InkWell(
                          onTap: () async {
                            _showRateInfo();
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.blue.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.blue.withOpacity(0.3)),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  size: 14,
                                  color: Colors.blue.shade600,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Details',
                                  style: TextStyle(
                                    fontSize: 11,
                                    color: Colors.blue.shade600,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        */
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '• Minimum withdrawal: ₱${NumberFormat('#,##0.00').format(CashOutService.getMinimumAmount())}\n'
                          '• Maximum withdrawal: ₱${NumberFormat('#,##0.00').format(CashOutService.getMaximumAmount())}\n'
                          // '• Current USD rate: \$1 = ₱${_currentUsdToPhpRate.toStringAsFixed(2)}\n'
                          '• Service fee (varies by method):\n'
                      // '  • Philippine Cards: 18.2% + ₱30\n'
                      // '  • International Cards (PHP): 19.2% + ₱30\n'
                      // '  • International Cards (USD): 19% + ₱${(_currentUsdToPhpRate + 20).toStringAsFixed(0)}\n'
                          '  • GCash: 17.3% \n'
                          '  • Bank Transfer: 16% \n'
                          '• Withdrawals require admin approval\n'
                          '• Funds are reserved until approval\n'
                          '• Account verification is required\n'
                          '• Ensure account details are correct',
                      style: const TextStyle(
                        color: Colors.black87,
                        fontSize: 14,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
              ),
            ),
    );
  }
}

// Card type utility (UI helpers only - detection now uses BIN lookup)
class CardTypeDetector {
  static String getCardTypeName(CardType type) {
    switch (type) {
      case CardType.philippine:
        return 'Philippine Card';
      case CardType.internationalPhp:
        return 'International (PHP-billed)';
      case CardType.internationalUsd:
        return 'International (USD-billed)';
      case CardType.unknown:
        return 'Unknown Card Type';
    }
  }

  static Color getCardTypeColor(CardType type) {
    switch (type) {
      case CardType.philippine:
        return Colors.green;
      case CardType.internationalPhp:
        return Colors.orange;
      case CardType.internationalUsd:
        return Colors.blue;
      case CardType.unknown:
        return Colors.grey;
    }
  }
}

enum CardType {
  philippine,
  internationalPhp,
  internationalUsd,
  unknown,
}

class _CreditCardFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue,
      TextEditingValue newValue,
      ) {
    final text = newValue.text;

    if (text.isEmpty) {
      return newValue;
    }

    // Remove all spaces
    final digitsOnly = text.replaceAll(' ', '');

    // Add spaces every 4 digits
    final buffer = StringBuffer();
    for (int i = 0; i < digitsOnly.length; i++) {
      if (i > 0 && i % 4 == 0) {
        buffer.write(' ');
      }
      buffer.write(digitsOnly[i]);
    }

    final formattedText = buffer.toString();

    return TextEditingValue(
      text: formattedText,
      selection: TextSelection.collapsed(offset: formattedText.length),
    );
  }
}




