<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>758079153152-10qtkb7ftmfjtbaljigeen85jhh9gfkp.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.758079153152-10qtkb7ftmfjtbaljigeen85jhh9gfkp</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>758079153152-3ep62fkktfqqtrr9aq3jtuc5ba4u33eq.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyDRM0X30q1yZI_0ByiADTBSvVsRmbYf83Y</string>
	<key>GCM_SENDER_ID</key>
	<string>758079153152</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.debcompanylimited.hanapp</string>
	<key>PROJECT_ID</key>
	<string>hanappproject</string>
	<key>STORAGE_BUCKET</key>
	<string>hanappproject.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:758079153152:ios:51b2fd756a47dbfc6a64e5</string>
	<key>PROJECT_ID</key>
	<string>hanappproject</string>
	<key>STORAGE_BUCKET</key>
	<string>hanappproject.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:758079153152:ios:4b471869e75120996a64e5</string>
</dict>
</plist>