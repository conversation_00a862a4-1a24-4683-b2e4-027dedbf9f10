<?php
// hanapp_backend/api/wallet/cash_out.php
// Handle cash-out (withdrawal) requests - similar to cash_in.php but for withdrawals

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Start output buffering to prevent any unexpected output
ob_start();

// Set up error handler to catch fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        ob_clean();
        http_response_code(500);
        echo json_encode([
            "success" => false,
            "message" => "Server error: " . $error['message'],
            "error_code" => "FATAL_ERROR"
        ]);
    }
});

require_once '../../config/db_connect.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    ob_clean();
    http_response_code(200);
    exit();
}

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("Only POST method is allowed.");
    }

    // Log the start of the request
    error_log("Cash-out request started at " . date('Y-m-d H:i:s'));

    // Get JSON input
    $rawInput = file_get_contents('php://input');
    error_log("Cash-out raw input: " . $rawInput);

    $input = json_decode($rawInput, true);

    if (!$input) {
        throw new Exception("Invalid JSON input. Raw input: " . $rawInput);
    }

    error_log("Cash-out parsed input: " . json_encode($input));

    // Validate required fields
    $userId = $input['user_id'] ?? null;
    $amount = $input['amount'] ?? null; // Net amount user will receive
    $requestedAmount = $input['requested_amount'] ?? $amount; // Original amount requested (fallback to amount for backward compatibility)
    $deductedAmount = $requestedAmount; // Total amount deducted from profit should equal requested amount
    $serviceFee = $input['service_fee'] ?? 0.0;
    $withdrawalMethod = $input['withdrawal_method'] ?? 'Bank Transfer';
    $accountDetails = $input['account_details'] ?? null;

    // Debug: Log what we received
    error_log("Cash-out DEBUG - Received withdrawal method: '" . $withdrawalMethod . "'");
    error_log("Cash-out DEBUG - Received account details: '" . $accountDetails . "'");

    // Validate withdrawal method
    $validMethods = ['GCash', 'Maya', 'Credit Card', 'Bank Transfer', 'BPI', 'China Bank', 'RCBC', 'UnionBank'];
    if (!in_array($withdrawalMethod, $validMethods)) {
        error_log("Cash-out ERROR - Invalid method received: '" . $withdrawalMethod . "'. Valid methods: " . implode(', ', $validMethods));
        throw new Exception("Invalid withdrawal method. Valid methods: " . implode(', ', $validMethods));
    }

    error_log("Cash-out DEBUG - Method validation passed for: '" . $withdrawalMethod . "'");

    if (empty($userId) || !is_numeric($userId)) {
        throw new Exception("User ID is required and must be numeric.");
    }

    if (empty($amount) || !is_numeric($amount) || $amount <= 0) {
        throw new Exception("Amount is required and must be a positive number.");
    }

    if (empty($accountDetails)) {
        throw new Exception("Account details are required for withdrawal.");
    }

    // Validate account details based on withdrawal method
    switch ($withdrawalMethod) {
        case 'GCash':
        case 'Maya':
            // Validate Philippine mobile number
            $cleanedNumber = preg_replace('/[\s\-\(\)]/', '', $accountDetails);
            if (!preg_match('/^(09|\+639)\d{9}$/', $cleanedNumber)) {
                throw new Exception("Invalid mobile number format for " . $withdrawalMethod . ". Use format: ***********");
            }
            break;

        case 'Credit Card':
            // Validate card number (basic validation)
            $cleanedCard = preg_replace('/[\s\-]/', '', $accountDetails);
            if (!preg_match('/^\d{13,19}$/', $cleanedCard)) {
                throw new Exception("Invalid card number format. Card number should be 13-19 digits.");
            }
            break;

        case 'Bank Transfer':
        case 'BPI':
        case 'China Bank':
        case 'RCBC':
        case 'UnionBank':
            // Validate bank account number (should be numeric and reasonable length)
            $cleanedAccount = preg_replace('/[\s\-]/', '', $accountDetails);
            if (!preg_match('/^\d{10,20}$/', $cleanedAccount)) {
                throw new Exception("Invalid account number format for " . $withdrawalMethod . ". Account number should be 10-20 digits.");
            }
            break;
    }

    $userId = intval($userId);
    $amount = floatval($amount);
    $requestedAmount = floatval($requestedAmount);
    $deductedAmount = floatval($deductedAmount);
    $serviceFee = floatval($serviceFee);
    $minimumAmount = 200.00; // Minimum withdrawal amount

    // Validate minimum amount against requested amount (before fees)
    if ($requestedAmount < $minimumAmount) {
        throw new Exception("Minimum withdrawal amount is ₱" . number_format($minimumAmount, 2));
    }

    // Check if user exists and get user details
    $stmt = $conn->prepare("
        SELECT id, full_name, email, verification_status, id_verified, total_profit
        FROM users
        WHERE id = ?
    ");

    if ($stmt === false) {
        throw new Exception("Failed to prepare user check statement.");
    }

    $stmt->bind_param("i", $userId);

    if (!$stmt->execute()) {
        $stmt->close();
        throw new Exception("Failed to execute user check.");
    }

    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        $stmt->close();
        throw new Exception("User not found.");
    }

    $user = $result->fetch_assoc();
    $stmt->close();

    // Check if user is verified (match verification screen logic exactly)
    // Only consider user verified if verification_status is exactly 'verified'
    $isVerified = ($user['verification_status'] === 'verified');

    error_log("Cash-out verification check: verification_status='{$user['verification_status']}' (type: " . gettype($user['verification_status']) . "), id_verified='{$user['id_verified']}', isVerified=" . ($isVerified ? 'true' : 'false'));

    if (!$isVerified) {
        throw new Exception("User must be verified to withdraw funds. Please complete verification first.");
    }

    // Check if user has sufficient profit (validate against deducted amount which includes fees)
    $userProfit = floatval($user['total_profit']);
    if ($deductedAmount > $userProfit) {
        throw new Exception("Insufficient balance. Your total profit is ₱" . number_format($userProfit, 2) . ".");
    }

    // Generate unique external ID for tracking
    $timestamp = time();
    $externalId = "hanapp_cashout_{$userId}_{$timestamp}";

    // Start database transaction for atomicity
    $conn->begin_transaction();

    try {
        // 1. Create withdrawal request with 'approved' status (skip admin approval)
        $stmt = $conn->prepare("
            INSERT INTO withdrawal_requests (
                user_id, amount, deducted_amount, transaction_fee, method, account_details, status, request_date, processed_date
            ) VALUES (?, ?, ?, ?, ?, ?, 'approved', NOW(), NOW())
        ");

        if ($stmt === false) {
            throw new Exception("Failed to prepare withdrawal request insert statement.");
        }

        // Debug: Log what we're about to insert
        error_log("Cash-out DEBUG - About to insert: user_id=$userId, requested_amount=$requestedAmount, net_amount=$amount, deducted_amount=$deductedAmount, transaction_fee=$serviceFee, method='$withdrawalMethod', account_details='$accountDetails'");

        $stmt->bind_param("idddss", $userId, $amount, $deductedAmount, $serviceFee, $withdrawalMethod, $accountDetails);

        if (!$stmt->execute()) {
            $stmt->close();
            error_log("Cash-out ERROR - Failed to execute withdrawal request insert: " . $stmt->error);
            throw new Exception("Failed to record withdrawal request: " . $stmt->error);
        }

        $withdrawalRequestId = $conn->insert_id;
        error_log("Cash-out DEBUG - Withdrawal request inserted with ID: $withdrawalRequestId");
        $stmt->close();

        // Verify what was actually inserted
        $verifyStmt = $conn->prepare("SELECT method, account_details, deducted_amount, transaction_fee FROM withdrawal_requests WHERE id = ?");
        $verifyStmt->bind_param("i", $withdrawalRequestId);
        $verifyStmt->execute();
        $result = $verifyStmt->get_result();
        if ($row = $result->fetch_assoc()) {
            error_log("Cash-out DEBUG - Verified insertion: method='" . $row['method'] . "', account_details='" . $row['account_details'] . "', deducted_amount='" . $row['deducted_amount'] . "', transaction_fee='" . $row['transaction_fee'] . "'");
        }
        $verifyStmt->close();

        // 2. Record transaction in transactions table (for consistent tracking like cash_in)
        $stmt = $conn->prepare("
            INSERT INTO transactions (
                user_id, type, method, amount, status, description,
                xendit_external_id, processed_by
            ) VALUES (?, 'cash_out', ?, ?, 'processing', ?, ?, 'system')
        ");

        if ($stmt === false) {
            throw new Exception("Failed to prepare transaction insert statement.");
        }

        $description = "Withdrawal request ₱" . number_format($amount, 2) . " via " . $withdrawalMethod . " - Request ID: " . $withdrawalRequestId;

        $stmt->bind_param(
            "isdss",
            $userId,
            $withdrawalMethod,
            $amount,
            $description,
            $externalId
        );

        if (!$stmt->execute()) {
            $stmt->close();
            throw new Exception("Failed to record transaction: " . $stmt->error);
        }

        $transactionId = $conn->insert_id;
        $stmt->close();

        // 3. DIRECTLY CALL XENDIT TO PROCESS DISBURSEMENT (NO ADMIN APPROVAL NEEDED)
        error_log("Cash-out DEBUG - Starting direct Xendit disbursement for withdrawal ID: $withdrawalRequestId");

        $xenditResult = createXenditDisbursement($withdrawalRequestId, $userId, $amount, $withdrawalMethod, $accountDetails, $user['full_name'], $conn);

        if (!$xenditResult['success']) {
            // If Xendit fails, update status to failed but don't rollback the withdrawal request
            // This allows admin to retry or handle manually
            $stmt = $conn->prepare("UPDATE withdrawal_requests SET status = 'failed', admin_notes = ? WHERE id = ?");
            $errorNote = "Xendit disbursement failed: " . $xenditResult['message'];
            $stmt->bind_param("si", $errorNote, $withdrawalRequestId);
            $stmt->execute();
            $stmt->close();

            $stmt = $conn->prepare("UPDATE transactions SET status = 'failed' WHERE id = ?");
            $stmt->bind_param("i", $transactionId);
            $stmt->execute();
            $stmt->close();

            error_log("Cash-out ERROR - Xendit disbursement failed: " . $xenditResult['message']);
            throw new Exception("Payment processing failed: " . $xenditResult['message']);
        }

        error_log("Cash-out SUCCESS - Xendit disbursement created successfully: " . json_encode($xenditResult));

        // 4. Deduct amount from user's total_profit (reserve the funds)
        $stmt = $conn->prepare("
            UPDATE users
            SET total_profit = total_profit - ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");

        if ($stmt === false) {
            throw new Exception("Failed to prepare profit update statement.");
        }

        $stmt->bind_param("di", $deductedAmount, $userId);

        if (!$stmt->execute()) {
            $stmt->close();
            throw new Exception("Failed to update user profit: " . $stmt->error);
        }

        $stmt->close();

        // Commit transaction
        $conn->commit();

        error_log("Cash-out request created: User $userId, Requested Amount $requestedAmount, Net Amount $amount, Service Fee $serviceFee, Withdrawal Request $withdrawalRequestId, Transaction $transactionId");

        // Success response
        ob_clean();
        echo json_encode([
            "success" => true,
            "message" => "Withdrawal request processed successfully. Money is being sent to your account.",
            "transaction_details" => [
                "withdrawal_request_id" => $withdrawalRequestId,
                "transaction_id" => $transactionId,
                "external_id" => $externalId,
                "requested_amount" => $requestedAmount,
                "net_amount" => $amount,
                "service_fee" => $serviceFee,
                "method" => $withdrawalMethod,
                "status" => "processing",
                "new_profit_balance" => $userProfit - $deductedAmount,
                "xendit_info" => [
                    "disbursement_id" => $xenditResult['disbursement_id'] ?? null,
                    "external_id" => $xenditResult['external_id'] ?? null,
                    "status" => $xenditResult['status'] ?? 'PENDING'
                ]
            ]
        ]);

    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        throw $e;
    }

} catch (Exception $e) {
    error_log("Cash-out Exception: " . $e->getMessage());
    ob_clean();
    echo json_encode([
        "success" => false,
        "message" => $e->getMessage(),
        "error_code" => "CASH_OUT_ERROR"
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}

/**
 * Create Xendit disbursement for immediate cash out processing
 */
function createXenditDisbursement($withdrawalId, $userId, $amount, $method, $accountDetails, $userFullName, $conn) {
    try {
        // Xendit configuration
        $xenditSecretKey = 'xnd_production_k5NqlGpmZlTPGEvBlYrk7a9ukwr8b2DzfQtEh3YThOcZazymwOlXwFT5ZEHIZm2';
        $xenditBaseUrl = 'https://api.xendit.co';

        // Generate unique external ID for this disbursement
        $externalId = 'hanapp_cashout_' . $withdrawalId . '_' . time();

        // Prepare payout data based on withdrawal method
        $payoutData = preparePayoutData($method, $accountDetails, $amount, $userFullName, $externalId);

        if (!$payoutData['success']) {
            return $payoutData; // Return error from preparePayoutData
        }

        error_log("Xendit disbursement - Sending payout data: " . json_encode($payoutData['data']));

        // Make API call to Xendit Payout API
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $xenditBaseUrl . '/v2/payouts');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payoutData['data']));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Basic ' . base64_encode($xenditSecretKey . ':'),
            'idempotency-key: ' . $externalId
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($curlError) {
            error_log("Xendit disbursement - cURL error: " . $curlError);
            return [
                'success' => false,
                'message' => 'Network error: ' . $curlError,
                'error_code' => 'NETWORK_ERROR'
            ];
        }

        if ($httpCode !== 200) {
            error_log("Xendit disbursement - HTTP error $httpCode: " . $response);
            $errorData = json_decode($response, true);
            $errorMessage = $errorData['message'] ?? 'Unknown Xendit API error';
            return [
                'success' => false,
                'message' => $errorMessage,
                'error_code' => 'XENDIT_API_ERROR',
                'http_code' => $httpCode
            ];
        }

        $xenditResponse = json_decode($response, true);

        if (!$xenditResponse || !isset($xenditResponse['id'])) {
            error_log("Xendit disbursement - Invalid response: " . $response);
            return [
                'success' => false,
                'message' => 'Invalid response from Xendit',
                'error_code' => 'INVALID_RESPONSE'
            ];
        }

        // Store disbursement record in database
        $stmt = $conn->prepare("
            INSERT INTO xendit_disbursements (
                withdrawal_request_id, xendit_disbursement_id, xendit_external_id,
                disbursement_status, disbursement_amount, disbursement_created_at
            ) VALUES (?, ?, ?, ?, ?, NOW())
        ");

        if ($stmt) {
            $status = $xenditResponse['status'] ?? 'PENDING';
            $stmt->bind_param("isssd",
                $withdrawalId,
                $xenditResponse['id'],
                $externalId,
                $status,
                $amount
            );
            $stmt->execute();
            $stmt->close();
        }

        error_log("Xendit disbursement - Success: " . json_encode($xenditResponse));

        return [
            'success' => true,
            'data' => $xenditResponse,
            'external_id' => $externalId,
            'disbursement_id' => $xenditResponse['id'],
            'status' => $xenditResponse['status'] ?? 'PENDING'
        ];

    } catch (Exception $e) {
        error_log("Xendit disbursement - Exception: " . $e->getMessage());
        return [
            'success' => false,
            'message' => $e->getMessage(),
            'error_code' => 'SYSTEM_ERROR'
        ];
    }
}

/**
 * Prepare payout data based on withdrawal method
 */
function preparePayoutData($method, $accountDetails, $amount, $userFullName, $externalId) {
    $method = strtolower(trim($method));

    switch ($method) {
        case 'gcash':
            return prepareGCashPayout($accountDetails, $amount, $userFullName, $externalId);

        case 'maya':
        case 'paymaya':
            return prepareMayaPayout($accountDetails, $amount, $userFullName, $externalId);

        case 'bpi':
            return prepareBPIPayout($accountDetails, $amount, $userFullName, $externalId);

        case 'china bank':
        case 'chinabank':
            return prepareChinaBankPayout($accountDetails, $amount, $userFullName, $externalId);

        case 'rcbc':
            return prepareRCBCPayout($accountDetails, $amount, $userFullName, $externalId);

        case 'unionbank':
            return prepareUnionBankPayout($accountDetails, $amount, $userFullName, $externalId);

        default:
            return [
                'success' => false,
                'message' => "Unsupported withdrawal method: $method",
                'error_code' => 'UNSUPPORTED_METHOD'
            ];
    }
}

/**
 * Prepare GCash payout data
 */
function prepareGCashPayout($accountDetails, $amount, $userFullName, $externalId) {
    // Format phone number for Xendit
    $phoneNumber = formatPhoneForXendit($accountDetails);

    if (!$phoneNumber) {
        return [
            'success' => false,
            'message' => 'Invalid GCash number format. Use format: ***********',
            'error_code' => 'INVALID_PHONE_FORMAT'
        ];
    }

    return [
        'success' => true,
        'data' => [
            'reference_id' => $externalId,
            'channel_code' => 'PH_GCASH',
            'channel_properties' => [
                'account_number' => $phoneNumber,
                'account_holder_name' => $userFullName ?: 'GCash User'
            ],
            'amount' => (float) $amount,
            'currency' => 'PHP',
            'description' => "HanApp withdrawal - GCash"
        ]
    ];
}

/**
 * Prepare Maya/PayMaya payout data
 */
function prepareMayaPayout($accountDetails, $amount, $userFullName, $externalId) {
    // Format phone number for Xendit
    $phoneNumber = formatPhoneForXendit($accountDetails);

    if (!$phoneNumber) {
        return [
            'success' => false,
            'message' => 'Invalid Maya number format. Use format: ***********',
            'error_code' => 'INVALID_PHONE_FORMAT'
        ];
    }

    return [
        'success' => true,
        'data' => [
            'reference_id' => $externalId,
            'channel_code' => 'PH_PAYMAYA',
            'channel_properties' => [
                'account_number' => $phoneNumber,
                'account_holder_name' => $userFullName ?: 'Maya User'
            ],
            'amount' => (float) $amount,
            'currency' => 'PHP',
            'description' => "HanApp withdrawal - Maya"
        ]
    ];
}

/**
 * Prepare BPI bank payout data
 */
function prepareBPIPayout($accountDetails, $amount, $userFullName, $externalId) {
    $accountNumber = extractBankAccountNumber($accountDetails);

    if (!$accountNumber) {
        return [
            'success' => false,
            'message' => 'Invalid BPI account number format',
            'error_code' => 'INVALID_ACCOUNT_FORMAT'
        ];
    }

    return [
        'success' => true,
        'data' => [
            'reference_id' => $externalId,
            'channel_code' => 'PH_BPI',
            'channel_properties' => [
                'account_number' => $accountNumber,
                'account_holder_name' => $userFullName ?: 'BPI Account Holder'
            ],
            'amount' => (float) $amount,
            'currency' => 'PHP',
            'description' => "HanApp withdrawal - BPI"
        ]
    ];
}

/**
 * Prepare China Bank payout data
 */
function prepareChinaBankPayout($accountDetails, $amount, $userFullName, $externalId) {
    $accountNumber = extractBankAccountNumber($accountDetails);

    if (!$accountNumber) {
        return [
            'success' => false,
            'message' => 'Invalid China Bank account number format',
            'error_code' => 'INVALID_ACCOUNT_FORMAT'
        ];
    }

    return [
        'success' => true,
        'data' => [
            'reference_id' => $externalId,
            'channel_code' => 'PH_CBC',
            'channel_properties' => [
                'account_number' => $accountNumber,
                'account_holder_name' => $userFullName ?: 'China Bank Account Holder'
            ],
            'amount' => (float) $amount,
            'currency' => 'PHP',
            'description' => "HanApp withdrawal - China Bank"
        ]
    ];
}

/**
 * Prepare RCBC payout data
 */
function prepareRCBCPayout($accountDetails, $amount, $userFullName, $externalId) {
    $accountNumber = extractBankAccountNumber($accountDetails);

    if (!$accountNumber) {
        return [
            'success' => false,
            'message' => 'Invalid RCBC account number format',
            'error_code' => 'INVALID_ACCOUNT_FORMAT'
        ];
    }

    return [
        'success' => true,
        'data' => [
            'reference_id' => $externalId,
            'channel_code' => 'PH_RCBC',
            'channel_properties' => [
                'account_number' => $accountNumber,
                'account_holder_name' => $userFullName ?: 'RCBC Account Holder'
            ],
            'amount' => (float) $amount,
            'currency' => 'PHP',
            'description' => "HanApp withdrawal - RCBC"
        ]
    ];
}

/**
 * Prepare UnionBank payout data
 */
function prepareUnionBankPayout($accountDetails, $amount, $userFullName, $externalId) {
    $accountNumber = extractBankAccountNumber($accountDetails);

    if (!$accountNumber) {
        return [
            'success' => false,
            'message' => 'Invalid UnionBank account number format',
            'error_code' => 'INVALID_ACCOUNT_FORMAT'
        ];
    }

    return [
        'success' => true,
        'data' => [
            'reference_id' => $externalId,
            'channel_code' => 'PH_UBP',
            'channel_properties' => [
                'account_number' => $accountNumber,
                'account_holder_name' => $userFullName ?: 'UnionBank Account Holder'
            ],
            'amount' => (float) $amount,
            'currency' => 'PHP',
            'description' => "HanApp withdrawal - UnionBank"
        ]
    ];
}

/**
 * Format Philippine phone number for Xendit API
 */
function formatPhoneForXendit($phoneNumber) {
    // Remove all non-numeric characters
    $cleaned = preg_replace('/[^0-9]/', '', $phoneNumber);

    // Handle different formats
    if (strlen($cleaned) === 11 && substr($cleaned, 0, 2) === '09') {
        // 09XXXXXXXXX format - convert to 639XXXXXXXXX
        return '63' . substr($cleaned, 1);
    } elseif (strlen($cleaned) === 12 && substr($cleaned, 0, 3) === '639') {
        // 639XXXXXXXXX format - already correct
        return $cleaned;
    } elseif (strlen($cleaned) === 13 && substr($cleaned, 0, 4) === '+639') {
        // +639XXXXXXXXX format - remove +
        return substr($cleaned, 1);
    } elseif (strlen($cleaned) === 10 && substr($cleaned, 0, 1) === '9') {
        // 9XXXXXXXXX format - add country code
        return '63' . $cleaned;
    }

    // Invalid format
    return false;
}

/**
 * Extract and validate bank account number
 */
function extractBankAccountNumber($accountDetails) {
    // Remove all non-numeric characters
    $cleaned = preg_replace('/[^0-9]/', '', $accountDetails);

    // Bank account numbers are typically 10-16 digits
    if (strlen($cleaned) >= 10 && strlen($cleaned) <= 16) {
        return $cleaned;
    }

    // Invalid format
    return false;
}

?>
