<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cash-in Successful - HanApp</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        .success-icon {
            font-size: 64px;
            color: #4CAF50;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        .amount-display {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-size: 24px;
            font-weight: bold;
        }
        .breakdown {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: left;
        }
        .breakdown-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            font-size: 14px;
        }
        .breakdown-total {
            border-top: 1px solid #ddd;
            padding-top: 8px;
            margin-top: 8px;
            font-weight: bold;
        }
        .info {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #4CAF50;
            text-align: left;
        }
        .instruction {
            background: #f0f8ff;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #2196F3;
        }
        .instruction h3 {
            margin-top: 0;
            color: #1976D2;
        }
        .instruction p:first-of-type {
            font-weight: bold;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">💰</div>
        <h1>Cash-in Successful!</h1>
        <p id="message">Your HanApp Balance has been topped up successfully!</p>

        <div class="amount-display">
            <div>Amount Added to Balance</div>
            <div id="amount-added">₱0.00</div>
        </div>

        <div class="breakdown">
            <div class="breakdown-row">
                <span>Cash-in Amount:</span>
                <span id="base-amount">₱0.00</span>
            </div>
            <div class="breakdown-row">
                <span>Transaction Fee:</span>
                <span id="transaction-fee">₱20.00</span>
            </div>
            <div class="breakdown-row breakdown-total">
                <span>Total Paid:</span>
                <span id="total-paid">₱0.00</span>
            </div>
        </div>

        <div class="info">
            <strong>What happens next?</strong><br>
            • Your balance has been updated immediately<br>
            • You'll receive an email confirmation<br>
            • Transaction history is available in the app<br>
            • You can now use your balance for services
        </div>

        <div class="instruction">
            <h3>💳 Cash-in Complete!</h3>
            <p><strong>Please return to the HanApp mobile application to see your updated balance.</strong></p>
            <p>Your balance has been updated automatically and is ready to use.</p>
            <p>You can close this page and go back to the app.</p>
        </div>
    </div>

    <script>
        // Get URL parameters to display transaction details
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        // Display transaction details if available
        window.onload = function() {
            const baseAmount = getUrlParameter('base_amount');
            const transactionFee = getUrlParameter('transaction_fee') || '20.00';
            const totalAmount = getUrlParameter('total_amount');
            
            if (baseAmount) {
                document.getElementById('amount-added').textContent = '₱' + parseFloat(baseAmount).toFixed(2);
                document.getElementById('base-amount').textContent = '₱' + parseFloat(baseAmount).toFixed(2);
            }
            
            if (totalAmount) {
                document.getElementById('total-paid').textContent = '₱' + parseFloat(totalAmount).toFixed(2);
            }
            
            document.getElementById('transaction-fee').textContent = '₱' + parseFloat(transactionFee).toFixed(2);
        };

        // Simple script to show the page is loaded
        console.log('Cash-in success page loaded');
    </script>
</body>
</html>
