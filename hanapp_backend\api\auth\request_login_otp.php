<?php
require_once '../config/db_connect.php';
require_once '../utils/email_sender.php';

date_default_timezone_set('Asia/Manila');

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['email'])) {
    echo json_encode(['success' => false, 'message' => 'Email is required']);
    exit();
}

$email = $input['email'];

// Generate unique request ID and add comprehensive logging
$requestId = uniqid('otp_req_', true);
error_log("[" . date('Y-m-d H:i:s') . "] request_login_otp.php: Request ID: $requestId - OTP request received for email: $email");

// Atomic check with transaction locking
$conn->begin_transaction();
try {
    // Lock user's OTP records for update
    $stmt = $conn->prepare("SELECT created_at FROM login_otp_codes WHERE user_id = (SELECT id FROM users WHERE email = ?) ORDER BY created_at DESC LIMIT 1 FOR UPDATE");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        $lastOtpTime = strtotime($row['created_at']);
        $currentTime = time();
        $timeDiff = $currentTime - $lastOtpTime;

        if ($timeDiff < 30) {
            error_log("[" . date('Y-m-d H:i:s') . "] request_login_otp.php: Request ID: $requestId - Atomic lock blocked duplicate (last OTP $timeDiff seconds ago)");
            $conn->rollback();
            echo json_encode(['success' => false, 'message' => 'OTP already sent. Please check your email.']);
            exit();
        }
    }
    $stmt->close();

    // Generate and store OTP...
    $conn->commit();
} catch (Exception $e) {
    $conn->rollback();
    throw $e;
}

try {
    // Check if user exists and has 2FA enabled
    $stmt = $conn->prepare("SELECT id, full_name, two_factor_enabled FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        if (!$row['two_factor_enabled']) {
            echo json_encode(['success' => false, 'message' => '2FA is not enabled for this account']);
            exit();
        }
        
        $userId = $row['id'];
        $firstName = explode(' ', $row['full_name'])[0];
        
        // Generate 6-digit OTP
$otp = sprintf('%06d', mt_rand(0, 999999));
$expiryTime = date('Y-m-d H:i:s', strtotime('+10 minutes'));

// Atomic insert/update
$stmt = $conn->prepare("REPLACE INTO login_otp_codes (user_id, otp_code, expires_at, created_at) VALUES (?, ?, ?, NOW())");
$stmt->bind_param("iss", $userId, $otp, $expiryTime);
        
        if ($stmt->execute()) {
            // Generate idempotency key and send OTP email
            $idempotencyKey = hash('sha256', $requestId);
            $subject = "Your Login Verification Code";
            $htmlMessage = "<h2>Login Verification</h2><p>Hello $firstName,</p><p>Your verification code is: <strong>$otp</strong></p><p>This code will expire in 10 minutes.</p>";
            $textMessage = "Hello $firstName, Your login verification code is: $otp. This code will expire in 10 minutes.";
            
            $emailResult = sendEmailViaSendGrid(
                $email, 
                $firstName, 
                $subject, 
                $htmlMessage, 
                $textMessage,
                null, // templateId
                [], // dynamicTemplateData
                $idempotencyKey
            );
            
            if ($emailResult['success']) {
                error_log("[" . date('Y-m-d H:i:s') . "] request_login_otp.php: Request ID: $requestId - OTP email sent successfully for $email");
                echo json_encode(['success' => true, 'message' => 'Login OTP sent successfully']);
            } else {
                error_log("[" . date('Y-m-d H:i:s') . "] request_login_otp.php: Request ID: $requestId - Failed to send OTP email for $email: " . $emailResult['message']);
                echo json_encode(['success' => false, 'message' => 'Failed to send login OTP: ' . $emailResult['message']]);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to generate login OTP']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'User not found']);
    }
    
    $stmt->close();
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}

$conn->close();
?>