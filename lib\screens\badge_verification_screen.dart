import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io'; // For File
import 'dart:async'; // For Timer
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/utils/api_config.dart'; // For API base URL
import 'package:hanapp/services/verification_service.dart';
import 'package:hanapp/utils/auth_service.dart'; // For User model and AuthService
import 'package:hanapp/models/user.dart'; // User model
import 'package:hanapp/utils/cache_cleaner.dart'; // For cache cleaning
import 'package:hanapp/screens/enhanced_face_verification_screen.dart';
import 'package:hanapp/utils/verification_storage.dart';
import 'package:hanapp/screens/verified_badge_payment_screen.dart';

class BadgeVerificationScreen extends StatefulWidget {
  const BadgeVerificationScreen({Key? key}) : super(key: key);

  @override
  State<BadgeVerificationScreen> createState() => _BadgeVerificationScreenState();
}

class _BadgeVerificationScreenState extends State<BadgeVerificationScreen> {
  String? _selectedIdType;
  XFile? _idPhotoFront; // Stores the selected front ID image file
  XFile? _idPhotoBack; // Stores the selected back ID image file
  bool? _confirmIdBelongsToUser; // Yes/No radio button state
  bool _isLoading = false; // For submission loading indicator

  // Store uploaded image URLs from server
  String? _uploadedFrontImageUrl;
  String? _uploadedBackImageUrl;

  // Auto-refresh verification status
  Timer? _statusPollingTimer;
  static const Duration _pollingInterval = Duration(seconds: 10); // Check every 10 seconds
  bool _isPollingActive = false;

  // Current user and verification status
  User? _currentUser;
  String _currentVerificationStatus = 'not_started';
  bool _isIdVerified = false;
  bool _isBadgeAcquired = false;

  final VerificationService _verificationService = VerificationService();
  final AuthService _authService = AuthService();

  @override
  void initState() {
    super.initState();
    _loadUserAndVerificationStatus();
  }

  @override
  void dispose() {
    _stopStatusPolling();
    super.dispose();
  }

  Future<void> _loadUserAndVerificationStatus() async {
    try {
      final user = await AuthService.getUser();
      if (user != null && mounted) {
        setState(() {
          _currentUser = user;
        });
        await _loadVerificationStatus();
      }
    } catch (e) {
      print('Error loading user: $e');
    }
  }

  Future<void> _loadVerificationStatus() async {
    if (_currentUser == null) return;

    try {
      final response = await _verificationService.getVerificationStatus(userId: _currentUser!.id!);
      if (response['success'] && mounted) {
        final data = response['data'];
        setState(() {
          _currentVerificationStatus = data['verification_status'] ?? 'not_started';
          _isIdVerified = data['is_id_verified'] ?? false;
          _isBadgeAcquired = data['is_badge_acquired'] ?? false;
          
          // Load existing uploaded images if available
          _uploadedFrontImageUrl = data['id_photo_front_url'];
          _uploadedBackImageUrl = data['id_photo_back_url'];
        });
      }
    } catch (e) {
      print('Error loading verification status: $e');
    }
  }

  void _startStatusPolling() {
    if (_isPollingActive) return;
    
    _isPollingActive = true;
    _statusPollingTimer = Timer.periodic(_pollingInterval, (timer) {
      if (mounted) {
        _loadVerificationStatus();
      } else {
        timer.cancel();
      }
    });
  }

  void _stopStatusPolling() {
    _statusPollingTimer?.cancel();
    _statusPollingTimer = null;
    _isPollingActive = false;
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  Future<void> _pickImage(String imageType) async {
    final ImagePicker picker = ImagePicker();
    
    try {
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        setState(() {
          if (imageType == 'front') {
            _idPhotoFront = image;
          } else if (imageType == 'back') {
            _idPhotoBack = image;
          }
        });
      }
    } catch (e) {
      _showSnackBar('Error picking image: $e', isError: true);
    }
  }

  Widget _buildImagePreview(XFile? imageFile, String? uploadedUrl, String label) {
    return Container(
      height: 120,
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: imageFile != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.file(
                File(imageFile.path),
                fit: BoxFit.cover,
              ),
            )
          : uploadedUrl != null
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    '${ApiConfig.baseUrl}/$uploadedUrl',
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return const Center(
                        child: Icon(Icons.error, color: Colors.red),
                      );
                    },
                  ),
                )
              : Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.camera_alt, color: Colors.grey.shade400, size: 40),
                      const SizedBox(height: 8),
                      Text(
                        label,
                        style: TextStyle(color: Colors.grey.shade600),
                      ),
                    ],
                  ),
                ),
    );
  }

  // Validation method
  bool _isFormValid() {
    return _selectedIdType != null &&
           _idPhotoFront != null &&
           _idPhotoBack != null &&
           _confirmIdBelongsToUser == true;
  }

  // Submit badge verification
  Future<void> _submitBadgeVerification() async {
    if (!_isFormValid()) {
      _showSnackBar('Please complete all required fields', isError: true);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Save badge verification data to SharedPreferences
      await VerificationStorage.saveBadgeVerificationData(
        idType: _selectedIdType!,
        frontPhotoPath: _idPhotoFront!.path,
        backPhotoPath: _idPhotoBack!.path,
        confirmation: _confirmIdBelongsToUser!,
        userId: _currentUser!.id!,
      );

      // Navigate to face verification
      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const EnhancedFaceVerificationScreen(
              shouldSubmitIdAfterFace: true, // Will submit badge verification after face scan
              isBadgeVerification: true, // Flag to indicate this is badge verification
            ),
          ),
        ).then((_) => _loadUserAndVerificationStatus());
      }
    } catch (e) {
      _showSnackBar('Error: $e', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'Get Verified Badge',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Constants.primaryColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: _currentUser == null
          ? const Center(child: CircularProgressIndicator())
          : _buildBody(),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          _buildHeaderSection(),
          const SizedBox(height: 24),
          
          // ID Type selection
          _buildIdTypeSection(),
          const SizedBox(height: 24),
          
          // Photo upload sections
          _buildPhotoUploadSection(),
          const SizedBox(height: 24),
          
          // Confirmation section
          _buildConfirmationSection(),
          const SizedBox(height: 32),
          
          // Submit button
          _buildSubmitButton(),
        ],
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.verified,
            size: 60,
            color: Constants.primaryColor,
          ),
          const SizedBox(height: 16),
          const Text(
            'Get Your Verified Badge',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          const Text(
            'Upload your ID photos and complete face verification to get your verified badge. No barangay clearance required for badge verification.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildIdTypeSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Select ID Type',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedIdType,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              hintText: 'Choose your ID type',
            ),
            items: const [
              DropdownMenuItem(value: 'drivers_license', child: Text("Driver's License")),
              DropdownMenuItem(value: 'passport', child: Text('Passport')),
              DropdownMenuItem(value: 'national_id', child: Text('National ID')),
              DropdownMenuItem(value: 'voters_id', child: Text("Voter's ID")),
              DropdownMenuItem(value: 'sss_id', child: Text('SSS ID')),
              DropdownMenuItem(value: 'philhealth_id', child: Text('PhilHealth ID')),
              DropdownMenuItem(value: 'tin_id', child: Text('TIN ID')),
              DropdownMenuItem(value: 'postal_id', child: Text('Postal ID')),
            ],
            onChanged: (String? newValue) {
              setState(() {
                _selectedIdType = newValue;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoUploadSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Upload ID Photos',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),

          // Front ID Photo
          const Text(
            'Front of ID',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () => _pickImage('front'),
            child: _buildImagePreview(_idPhotoFront, _uploadedFrontImageUrl, 'Tap to take front photo'),
          ),
          const SizedBox(height: 20),

          // Back ID Photo
          const Text(
            'Back of ID',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () => _pickImage('back'),
            child: _buildImagePreview(_idPhotoBack, _uploadedBackImageUrl, 'Tap to take back photo'),
          ),
        ],
      ),
    );
  }

  Widget _buildConfirmationSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Confirmation',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'Does this ID belong to you?',
            style: TextStyle(
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: RadioListTile<bool>(
                  title: const Text('Yes'),
                  value: true,
                  groupValue: _confirmIdBelongsToUser,
                  onChanged: (bool? value) {
                    setState(() {
                      _confirmIdBelongsToUser = value;
                    });
                  },
                  activeColor: Constants.primaryColor,
                ),
              ),
              Expanded(
                child: RadioListTile<bool>(
                  title: const Text('No'),
                  value: false,
                  groupValue: _confirmIdBelongsToUser,
                  onChanged: (bool? value) {
                    setState(() {
                      _confirmIdBelongsToUser = value;
                    });
                  },
                  activeColor: Constants.primaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: (_isLoading || !_isFormValid()) ? null : _submitBadgeVerification,
        style: ElevatedButton.styleFrom(
          backgroundColor: _isFormValid() ? Constants.primaryColor : Colors.grey.shade300,
          foregroundColor: _isFormValid() ? Colors.white : Colors.grey.shade600,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                'Continue to Face Verification',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }
}
