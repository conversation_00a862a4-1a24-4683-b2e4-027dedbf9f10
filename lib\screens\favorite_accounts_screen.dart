// lib/screens/favorite_accounts_screen.dart
import 'package:flutter/material.dart';
import 'package:hanapp/models/user.dart';
import 'package:hanapp/utils/auth_service.dart';
import 'package:hanapp/utils/user_service.dart'; // Ensure this is correctly imported
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/utils/image_utils.dart'; // Ensure this is correctly imported
import 'package:cached_network_image/cached_network_image.dart'; // For displaying network images

class FavoriteAccountsScreen extends StatefulWidget {
  const FavoriteAccountsScreen({super.key});

  @override
  State<FavoriteAccountsScreen> createState() => _FavoriteAccountsScreenState();
}

class _FavoriteAccountsScreenState extends State<FavoriteAccountsScreen> {
  User? _currentUser;
  List<User> _favoriteUsers = [];
  bool _isLoading = true;
  final UserService _userService = UserService();

  @override
  void initState() {
    super.initState();
    _loadCurrentUserAndFavorites();
  }

  Future<void> _loadCurrentUserAndFavorites() async {
    _currentUser = await AuthService.getUser();
    if (_currentUser == null) {
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/login');
      }
      return;
    }
    await _fetchFavorites();
  }

  Future<void> _fetchFavorites() async {
    if (_currentUser == null) return;
    setState(() { _isLoading = true; });
    final response = await _userService.getFavorites(_currentUser!.id);
    setState(() {
      _isLoading = false;
      if (response['success']) {
        _favoriteUsers = response['favorites'];
      } else {
        _showSnackBar('Failed to load favorites: ${response['message']}', isError: true);
      }
    });
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
      ),
    );
  }

  Future<void> _removeFavorite(User userToRemove) async {
    if (_currentUser == null || _currentUser!.id == null) {
      _showSnackBar('User not logged in.', isError: true);
      return;
    }

    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Remove from Favorites?'),
          content: Text('Are you sure you want to remove ${userToRemove.fullName} from your favorite lists?'),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('No'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Yes'),
            ),
          ],
        );
      },
    );

    if (confirm == true) {
      setState(() { _isLoading = true; }); // Show loading while processing
      final response = await _userService.removeFavorite(_currentUser!.id, userToRemove.id);
      setState(() { _isLoading = false; }); // Hide loading
      if (response['success']) {
        _showSnackBar('${userToRemove.fullName} removed from favorites.');
        _fetchFavorites(); // Refresh the list
      } else {
        _showSnackBar('Failed to remove from favorites: ${response['message']}', isError: true);
      }
    }
  }

  Widget _buildUserListTile(User user, {required String actionText, required VoidCallback onAction}) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 0.0), // No horizontal margin here as it's in a list view
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        leading: CircleAvatar(
          backgroundImage: ImageUtils.createProfileImageProvider(user.profilePictureUrl),
          child: (user.profilePictureUrl == null || user.profilePictureUrl!.isEmpty)
              ? const Icon(Icons.person, size: 24, color: Colors.white)
              : null,
        ),
        title: Text(user.fullName),
        subtitle: Text(user.email),
        trailing: TextButton(
          onPressed: onAction,
          child: Text(
            actionText,
            style: TextStyle(color: actionText == 'Remove' ? Colors.red : Colors.green),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_currentUser == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Favorite Accounts'),
          backgroundColor: Constants.primaryColor,
          foregroundColor: Colors.white,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Favorite Accounts'),
        backgroundColor: Constants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Accounts you have marked as favorite.',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _favoriteUsers.isEmpty
                ? const Center(child: Text('No favorite accounts yet.'))
                : Expanded( // Use Expanded for ListView.builder in a Column
              child: ListView.builder(
                itemCount: _favoriteUsers.length,
                itemBuilder: (context, index) {
                  final user = _favoriteUsers[index];
                  return _buildUserListTile(
                    user,
                    actionText: 'Remove',
                    onAction: () => _removeFavorite(user),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

