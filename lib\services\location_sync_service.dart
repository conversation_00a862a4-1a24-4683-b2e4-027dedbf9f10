import 'dart:async';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../utils/location_service.dart';
import 'location_update_service.dart';
import '../utils/auth_service.dart';

class LocationSyncService {
  static final LocationSyncService _instance = LocationSyncService._internal();
  factory LocationSyncService() => _instance;
  LocationSyncService._internal();

  static LocationSyncService get instance => _instance;

  // Stream controllers for location updates
  final StreamController<LocationData> _locationController = StreamController<LocationData>.broadcast();
  final LocationService _locationService = LocationService();

  // Current location data
  LocationData? _currentLocationData;

  // Getters
  Stream<LocationData> get locationStream => _locationController.stream;
  LocationData? get currentLocationData => _currentLocationData;

  // Update location and notify all listeners
  Future<void> updateLocation({
    required int userId,
    required double latitude,
    required double longitude,
    String? addressDetails,
  }) async {
    try {
      // Get address from coordinates if not provided
      String? address = addressDetails;
      if (address == null) {
        address = await _locationService.getAddressFromCoordinates(latitude, longitude);
      }

      // Update database
      await LocationUpdateService.updateUserLocation(
        userId: userId,
        latitude: latitude,
        longitude: longitude,
      );

      // Create location data
      final locationData = LocationData(
        latitude: latitude,
        longitude: longitude,
        addressDetails: address ?? 'Location coordinates: $latitude, $longitude',
        timestamp: DateTime.now(),
      );

      // Update current location data
      _currentLocationData = locationData;

      // Notify all listeners
      _locationController.add(locationData);

      print('LocationSyncService: Location updated and broadcasted');
    } catch (e) {
      print('LocationSyncService: Error updating location: $e');
    }
  }

  // Get current location and update
  Future<void> getCurrentLocationAndUpdate() async {
    try {
      final user = await AuthService.getUser();
      if (user?.id == null) return;

      final position = await _locationService.getCurrentLocation();
      if (position != null) {
        await updateLocation(
          userId: user!.id,
          latitude: position.latitude,
          longitude: position.longitude,
        );
      }
    } catch (e) {
      print('LocationSyncService: Error getting current location: $e');
    }
  }

  // Dispose resources
  void dispose() {
    _locationController.close();
  }
}

// Location data model
class LocationData {
  final double latitude;
  final double longitude;
  final String addressDetails;
  final DateTime timestamp;

  LocationData({
    required this.latitude,
    required this.longitude,
    required this.addressDetails,
    required this.timestamp,
  });

  @override
  String toString() {
    return 'LocationData(lat: $latitude, lng: $longitude, address: $addressDetails)';
  }
}