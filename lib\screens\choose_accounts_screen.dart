// lib/screens/accounts_screen.dart
import 'package:flutter/material.dart';
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/screens/favorite_accounts_screen.dart'; // NEW: Import favorite accounts screen
import 'package:hanapp/screens/blocked_accounts_screen.dart';   // NEW: Import blocked accounts screen

class ChooseAccountsScreen extends StatelessWidget {
  const ChooseAccountsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Accounts'),
        backgroundColor: Constants.primaryColor, // Consistent app bar color
        foregroundColor: Colors.white, // White icons/text for app bar
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          Card(
            margin: const EdgeInsets.symmetric(vertical: 8.0),
            elevation: 2,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: ListTile(
              leading: Icon(Icons.favorite, color: Colors.red.shade400),
              title: const Text('Favorite Accounts'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(builder: (context) => const FavoriteAccountsScreen()),
                );
              },
            ),
          ),
          Card(
            margin: const EdgeInsets.symmetric(vertical: 8.0),
            elevation: 2,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: ListTile(
              leading: Icon(Icons.block, color: Colors.grey.shade600),
              title: const Text('Blocked Accounts'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(builder: (context) => const BlockedAccountsScreen()),
                );
              },
            ),
          ),
          // You can add more account-related options here
        ],
      ),
    );
  }
}