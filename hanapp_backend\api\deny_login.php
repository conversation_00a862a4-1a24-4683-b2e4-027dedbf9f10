<?php
// hanapp_backend/api/deny_login.php
// Handles user denial of login and secures the account

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'config/db_connect.php';
require_once 'utils/email_sender.php';

header('Content-Type: text/html');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $token = $_GET['token'] ?? null;

    if (empty($token)) {
        throw new Exception("Invalid or missing confirmation token.");
    }

    // Find the login notification record
    $stmt = $conn->prepare("SELECT id, user_id, status, created_at FROM login_notifications WHERE confirmation_token = ?");
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception("Invalid confirmation token.");
    }
    
    $notification = $result->fetch_assoc();
    $stmt->close();

    // Check if token is expired (24 hours)
    $createdAt = new DateTime($notification['created_at']);
    $now = new DateTime();
    $diff = $now->diff($createdAt);
    
    if ($diff->days >= 1) {
        throw new Exception("Confirmation token has expired.");
    }

    // Check if already processed
    if ($notification['status'] !== 'pending') {
        $message = $notification['status'] === 'denied' ? 
            "This account has already been secured." : 
            "This login has already been processed.";
        
        echo generateResponsePage("Already Processed", $message, "info");
        exit();
    }

    // Get user information
    $userStmt = $conn->prepare("SELECT id, full_name, email FROM users WHERE id = ?");
    $userStmt->bind_param("i", $notification['user_id']);
    $userStmt->execute();
    $userResult = $userStmt->get_result();
    
    if ($userResult->num_rows === 0) {
        throw new Exception("User not found.");
    }
    
    $user = $userResult->fetch_assoc();
    $userStmt->close();

    // Begin transaction for security actions
    $conn->begin_transaction();

    try {
        // 1. Update notification status to denied
        $updateStmt = $conn->prepare("UPDATE login_notifications SET status = 'denied', confirmed_at = NOW() WHERE id = ?");
        $updateStmt->bind_param("i", $notification['id']);
        $updateStmt->execute();
        $updateStmt->close();

        // 2. Force logout from all devices
        $logoutStmt = $conn->prepare("UPDATE users SET is_logged_in = 0 WHERE id = ?");
        $logoutStmt->bind_param("i", $notification['user_id']);
        $logoutStmt->execute();
        $logoutStmt->close();

        // 3. Generate a password reset token
        $resetToken = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', strtotime('+1 hour'));
        
        // Create password_reset_codes_email table if it doesn't exist
        $createTableSql = "
            CREATE TABLE IF NOT EXISTS password_reset_codes_email (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                token VARCHAR(64) NOT NULL UNIQUE,
                expires_at DATETIME NOT NULL,
                used TINYINT(1) DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        ";
        $conn->query($createTableSql);

        // Delete any existing reset tokens for this user
        $deleteTokensStmt = $conn->prepare("DELETE FROM password_reset_codes_email WHERE user_id = ?");
        $deleteTokensStmt->bind_param("i", $notification['user_id']);
        $deleteTokensStmt->execute();
        $deleteTokensStmt->close();

        // Insert new reset token
        $insertTokenStmt = $conn->prepare("INSERT INTO password_reset_codes_email (user_id, token, expires_at) VALUES (?, ?, ?)");
        $insertTokenStmt->bind_param("iss", $notification['user_id'], $resetToken, $expiresAt);
        $insertTokenStmt->execute();
        $insertTokenStmt->close();

        // 4. Log security incident
        $logIncidentSql = "
            CREATE TABLE IF NOT EXISTS security_incidents (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                incident_type VARCHAR(50) NOT NULL,
                description TEXT,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        ";
        $conn->query($logIncidentSql);

        $incidentStmt = $conn->prepare("
            INSERT INTO security_incidents (user_id, incident_type, description, ip_address, user_agent) 
            VALUES (?, 'unauthorized_login_reported', 'User reported unauthorized login attempt', ?, ?)
        ");
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
        $incidentStmt->bind_param("iss", $notification['user_id'], $ipAddress, $userAgent);
        $incidentStmt->execute();
        $incidentStmt->close();

        $conn->commit();

        // 5. Send security notification email
        $resetUrl = "https://autosell.io//api/reset_password_form.php?token=$resetToken";
        
        $emailSubject = 'Account Secured - Password Reset Required - TAPP';
        $emailBodyHtml = '
            <html>
            <head>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; background-color: #f4f4f4; margin: 0; padding: 20px; }
                    .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
                    .header { text-align: center; margin-bottom: 30px; }
                    .logo { font-size: 24px; font-weight: bold; color: #141CC9; }
                    .alert-box { background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 15px; margin: 20px 0; color: #721c24; }
                    .success-box { background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin: 20px 0; color: #155724; }
                    .btn { display: inline-block; background-color: #141CC9; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }
                    .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <div class="logo">TAPP</div>
                        <h2>Account Secured</h2>
                    </div>
                    
                    <div class="success-box">
                        <strong>Account Secured:</strong> We have taken immediate action to protect your account.
                    </div>
                    
                    <p>Hello ' . htmlspecialchars($user['full_name']) . ',</p>
                    
                    <p>Thank you for reporting the unauthorized login attempt. We have immediately secured your account by:</p>
                    
                    <ul>
                        <li>Logging out all devices</li>
                        <li>Requiring a password reset</li>
                        <li>Logging this security incident</li>
                    </ul>
                    
                    <div class="alert-box">
                        <strong>Action Required:</strong> You must reset your password before you can log in again.
                    </div>
                    
                    <p>To reset your password and regain access to your account:</p>
                    
                    <div style="text-align: center;">
                        <a href="' . $resetUrl . '" class="btn">Reset Password</a>
                    </div>
                    
                    <p><strong>This reset link will expire in 1 hour.</strong></p>
                    
                    <p>If you need assistance or have any questions about this security incident, please contact our support team immediately.</p>
                    
                    <div class="footer">
                        <p>This is an automated security notification from TAPP.</p>
                        <p>&copy; ' . date('Y') . ' TAPP. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
        ';
        
        $emailBodyText = "TAPP Account Secured\n\n" .
                         "Hello {$user['full_name']},\n\n" .
                         "Thank you for reporting the unauthorized login attempt. We have immediately secured your account.\n\n" .
                         "Actions taken:\n" .
                         "- Logged out all devices\n" .
                         "- Required password reset\n" .
                         "- Logged security incident\n\n" .
                         "To reset your password: $resetUrl\n\n" .
                         "This link expires in 1 hour.\n\n" .
                         "Best regards,\nTAPP Security Team";

        $mailResult = sendEmailViaSendGrid(
            $user['email'], 
            $user['full_name'], 
            $emailSubject, 
            $emailBodyHtml, 
            $emailBodyText
        );

        if ($mailResult['success']) {
            echo generateResponsePage(
                "Account Secured", 
                "Your account has been secured and all devices have been logged out. A password reset link has been sent to your email address. You must reset your password before logging in again.",
                "success"
            );
        } else {
            echo generateResponsePage(
                "Account Secured", 
                "Your account has been secured, but there was an issue sending the password reset email. Please contact support for assistance.",
                "warning"
            );
        }

    } catch (Exception $e) {
        $conn->rollback();
        throw $e;
    }

} catch (Exception $e) {
    error_log("Login denial error: " . $e->getMessage());
    echo generateResponsePage(
        "Error", 
        "An error occurred while securing your account: " . $e->getMessage() . " Please contact support immediately.",
        "error"
    );
}

$conn->close();

function generateResponsePage($title, $message, $type) {
    $color = $type === 'success' ? '#28a745' : ($type === 'error' ? '#dc3545' : ($type === 'warning' ? '#ffc107' : '#17a2b8'));
    $icon = $type === 'success' ? '✓' : ($type === 'error' ? '✗' : ($type === 'warning' ? '⚠' : 'ℹ'));
    
    return '
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>' . htmlspecialchars($title) . ' - TAPP</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                background-color: #f4f4f4;
                margin: 0;
                padding: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
            }
            .container {
                background-color: white;
                padding: 40px;
                border-radius: 10px;
                box-shadow: 0 0 20px rgba(0,0,0,0.1);
                text-align: center;
                max-width: 500px;
                width: 100%;
            }
            .logo {
                font-size: 28px;
                font-weight: bold;
                color: #141CC9;
                margin-bottom: 20px;
            }
            .icon {
                font-size: 48px;
                color: ' . $color . ';
                margin-bottom: 20px;
            }
            h1 {
                color: ' . $color . ';
                margin-bottom: 20px;
            }
            p {
                color: #666;
                line-height: 1.6;
                margin-bottom: 30px;
            }
            .btn {
                display: inline-block;
                background-color: #141CC9;
                color: white;
                padding: 12px 30px;
                text-decoration: none;
                border-radius: 5px;
                font-weight: bold;
            }
            .btn:hover {
                background-color: #0f16a3;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="logo">TAPP</div>
            <div class="icon">' . $icon . '</div>
            <h1>' . htmlspecialchars($title) . '</h1>
            <p>' . htmlspecialchars($message) . '</p>
            <a href="#" onclick="window.close()" class="btn">Close</a>
        </div>
    </body>
    </html>
    ';
}
?>