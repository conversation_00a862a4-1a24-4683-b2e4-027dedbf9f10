import 'package:flutter/material.dart';
import 'dart:async';
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/utils/auth_service.dart';

class LoginOtpDialog extends StatefulWidget {
  final String email;
  final String password; // Add password parameter
  final VoidCallback onSuccess;

  const LoginOtpDialog({
    Key? key,
    required this.email,
    required this.password, // Add password parameter
    required this.onSuccess,
  }) : super(key: key);

  @override
  State<LoginOtpDialog> createState() => _LoginOtpDialogState();
}

class _LoginOtpDialogState extends State<LoginOtpDialog> {
  final TextEditingController _otpController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  final AuthService _authService = AuthService();
  bool _isLoading = false;
  int _resendTimerSeconds = 60;
  Timer? _timer;
  bool _canResend = false;
  bool _initialOtpSent = false; // Add flag to prevent multiple initial OTP requests
  final String _instanceId = DateTime.now().millisecondsSinceEpoch.toString(); // Unique instance ID

  @override
  void initState() {
    super.initState();
    print('LoginOtpDialog ($_instanceId): initState called for email: ${widget.email} at ${DateTime.now()}');
    _startResendTimer();
    // Delay OTP sending until after the dialog is fully rendered
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _sendInitialOtp();
    });
  }

  @override
  void dispose() {
    _otpController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  void _startResendTimer() {
    _resendTimerSeconds = 60;
    _canResend = false;
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_resendTimerSeconds == 0) {
        setState(() {
          _canResend = true;
          timer.cancel();
        });
      } else {
        setState(() {
          _resendTimerSeconds--;
        });
      }
    });
  }

  Future<void> _sendInitialOtp() async {
    // Prevent multiple initial OTP requests
    if (_initialOtpSent) {
      print('LoginOtpDialog ($_instanceId): Initial OTP already sent, skipping duplicate request');
      return;
    }
    
    _initialOtpSent = true;
    print('LoginOtpDialog ($_instanceId): Sending initial OTP for email: ${widget.email} at ${DateTime.now()}');
    
    setState(() { _isLoading = true; });
    
    final response = await _authService.requestLoginOtp(email: widget.email);
    
    setState(() { _isLoading = false; });
    
    if (response['success']) {
      _showSnackBar('Login verification code sent to your email');
      print('LoginOtpDialog ($_instanceId): Initial OTP sent successfully at ${DateTime.now()}');
    } else {
      _showSnackBar('Failed to send verification code: ${response['message']}', isError: true);
      print('LoginOtpDialog ($_instanceId): Failed to send initial OTP: ${response['message']} at ${DateTime.now()}');
      _initialOtpSent = false; // Reset flag on failure
    }
  }

  Future<void> _verifyOtp() async {
    if (_formKey.currentState!.validate()) {
      setState(() { _isLoading = true; });
      
      final response = await _authService.verifyLoginOtp(
        email: widget.email,
        otp: _otpController.text.trim(),
      );
      
      setState(() { _isLoading = false; });
      
      if (response['success']) {
        // _showSnackBar('Verification successful!'); // Removed login successful snackbar
        
        // Complete the 2FA login process
        final loginResponse = await _authService.completeLoginAfter2FA(
          widget.email, 
          widget.password
        );
        
        if (loginResponse['success']) {
          // Set login status
          final user = await AuthService.getUser();
          if (user != null) {
            await AuthService.setLoginStatus(user.id!);
            
            // Pop the dialog first
            Navigator.of(context).pop(true);
            
            // Navigate based on role immediately
            final userRole = user.role;
            
            if (userRole == 'lister') {
              Navigator.of(context).pushNamedAndRemoveUntil(
                '/choose_listing_type',
                (route) => false,
              );
            } else if (userRole == 'doer') {
              Navigator.of(context).pushNamedAndRemoveUntil(
                '/doer_job_listings',
                (route) => false,
              );
            } else {
              Navigator.of(context).pushNamedAndRemoveUntil(
                '/role_selection',
                (route) => false,
              );
            }
          }
        } else {
          _showSnackBar('Login completion failed: ${loginResponse['message']}', isError: true);
        }
      } else {
        _showSnackBar('Invalid verification code: ${response['message']}', isError: true);
      }
    }
  }

  Future<void> _resendOtp() async {
    if (_canResend) {
      print('LoginOtpDialog ($_instanceId): Resending OTP for email: ${widget.email} at ${DateTime.now()}');
      setState(() { _isLoading = true; _canResend = false; });
      
      final response = await _authService.requestLoginOtp(email: widget.email);
      
      setState(() { _isLoading = false; });
      
      if (response['success']) {
        _showSnackBar('Verification code resent successfully!');
        print('LoginOtpDialog ($_instanceId): OTP resent successfully at ${DateTime.now()}');
        _startResendTimer();
      } else {
        _showSnackBar('Failed to resend verification code: ${response['message']}', isError: true);
        print('LoginOtpDialog ($_instanceId): Failed to resend OTP: ${response['message']} at ${DateTime.now()}');
        setState(() { _canResend = true; });
      }
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text(
        '2-Factor Authentication',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Enter the verification code sent to your email to complete login.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 20),
            TextFormField(
              controller: _otpController,
              keyboardType: TextInputType.number,
              textAlign: TextAlign.center,
              maxLength: 6,
              decoration: InputDecoration(
                labelText: 'Enter Verification Code',
                hintText: 'XXXXXX',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                counterText: '',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter the verification code';
                }
                if (value.length != 6 || !RegExp(r'^[0-9]+$').hasMatch(value)) {
                  return 'Please enter a valid 6-digit code';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextButton(
              onPressed: _canResend ? _resendOtp : null,
              child: Text(
                _canResend ? 'Resend Code' : 'Resend in: $_resendTimerSeconds',
                style: TextStyle(
                  color: _canResend ? Constants.primaryColor : Colors.grey,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _verifyOtp,
          style: ElevatedButton.styleFrom(
            backgroundColor: Constants.primaryColor,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2),
                )
              : const Text('Verify'),
        ),
      ],
    );
  }
}