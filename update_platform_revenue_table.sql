-- S<PERSON> query to update platform_revenue table for job payment transparency
-- Add payment_amount column to track total payment amount vs platform revenue

-- 1. Add payment_amount column to platform_revenue table
ALTER TABLE `platform_revenue`
ADD COLUMN `payment_amount` decimal(10,2) DEFAULT NULL COMMENT 'Total amount paid by user (for transparency)' AFTER `amount`,
ADD COLUMN `payment_method` varchar(50) DEFAULT NULL COMMENT 'Payment method used (hanapp_balance, gcash, grabpay, maya, card, bank_transfer)' AFTER `payment_amount`,
ADD COLUMN `xendit_invoice_id` varchar(255) DEFAULT NULL COMMENT 'Xendit invoice ID for tracking' AFTER `payment_method`,
ADD COLUMN `xendit_external_id` varchar(255) DEFAULT NULL COMMENT 'External ID sent to Xendit' AFTER `xendit_invoice_id`,
ADD COLUMN `application_id` int(11) DEFAULT NULL COMMENT 'Related application ID for job payments' AFTER `reference_id`,
ADD COLUMN `user_id` int(11) DEFAULT NULL COMMENT 'User who made the payment' AFTER `application_id`;

-- 2. Add indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_payment_method` ON `platform_revenue` (`payment_method`);
CREATE INDEX IF NOT EXISTS `idx_xendit_invoice` ON `platform_revenue` (`xendit_invoice_id`);
CREATE INDEX IF NOT EXISTS `idx_xendit_external` ON `platform_revenue` (`xendit_external_id`);
CREATE INDEX IF NOT EXISTS `idx_application_id` ON `platform_revenue` (`application_id`);
CREATE INDEX IF NOT EXISTS `idx_user_id` ON `platform_revenue` (`user_id`);

-- 3. Update existing records to have payment_method
UPDATE `platform_revenue` 
SET `payment_method` = 'hanapp_balance' 
WHERE `source` = 'hanapp_balance_payment' AND `payment_method` IS NULL;

-- 4. Sample data structure after update:
/*
Example records after update:

1. HanApp Balance Job Payment:
INSERT INTO platform_revenue VALUES (
  1,                                    -- id
  'job_payment_fee',                   -- transaction_type
  181,                                 -- reference_id (transaction_id)
  188,                                 -- application_id
  87,                                  -- user_id (lister who paid)
  25.00,                               -- amount (platform fee)
  525.00,                              -- payment_amount (total paid by user)
  'hanapp_balance',                    -- payment_method
  NULL,                                -- xendit_invoice_id (not applicable)
  NULL,                                -- xendit_external_id (not applicable)
  'hanapp_balance_payment',            -- source
  'Platform fee from HanApp Balance job payment - Application #188', -- description
  '2025-07-09 08:30:00'               -- created_at
);

2. GCash Job Payment:
INSERT INTO platform_revenue VALUES (
  2,                                    -- id
  'job_payment_fee',                   -- transaction_type
  182,                                 -- reference_id (transaction_id)
  189,                                 -- application_id
  87,                                  -- user_id (lister who paid)
  25.00,                               -- amount (platform fee)
  525.00,                              -- payment_amount (total paid by user)
  'gcash',                             -- payment_method
  '686bad2fe0ca0a092cd7d720',         -- xendit_invoice_id
  'hanapp_jobpay_87_189_1720345800',  -- xendit_external_id
  'xendit_job_payment',               -- source
  'Platform fee from GCash job payment - Application #189', -- description
  '2025-07-09 08:35:00'               -- created_at
);

3. Maya Job Payment:
INSERT INTO platform_revenue VALUES (
  3,                                    -- id
  'job_payment_fee',                   -- transaction_type
  183,                                 -- reference_id (transaction_id)
  190,                                 -- application_id
  87,                                  -- user_id (lister who paid)
  25.00,                               -- amount (platform fee)
  525.00,                              -- payment_amount (total paid by user)
  'maya',                              -- payment_method
  '686bad2fe0ca0a092cd7d721',         -- xendit_invoice_id
  'hanapp_jobpay_87_190_1720345900',  -- xendit_external_id
  'xendit_job_payment',               -- source
  'Platform fee from Maya job payment - Application #190', -- description
  '2025-07-09 08:40:00'               -- created_at
);
*/

-- 5. Query to get revenue transparency report
/*
SELECT
    pr.id,
    pr.transaction_type,
    pr.payment_method,
    pr.payment_amount as total_paid_by_user,
    pr.amount as platform_revenue,
    (pr.payment_amount - pr.amount) as amount_to_doer,
    pr.application_id,
    pr.user_id as lister_id,
    u.full_name as lister_name,
    pr.description,
    pr.created_at
FROM platform_revenue pr
LEFT JOIN users u ON pr.user_id = u.id
WHERE pr.transaction_type = 'job_payment_fee'
ORDER BY pr.created_at DESC;
*/
