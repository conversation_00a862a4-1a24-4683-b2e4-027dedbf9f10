import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:hanapp/utils/api_config.dart';
import 'package:hanapp/models/public_listing.dart'; // Use the updated Listing model
import 'package:hanapp/models/user.dart'; // Import User model to get current user ID
import 'package:hanapp/utils/auth_service.dart'; // To get current user

class ListingService {
  Future<Map<String, dynamic>> createListing({
    required String title,
    String? description,
    double? price,
    required String category,
    double? latitude,
    double? longitude,
    String? locationAddress,
    String preferredDoerGender = 'Any',
    List<String>? picturesUrls,
    bool isActive = true,
    String? tags,
  }) async {
    final url = Uri.parse(ApiConfig.createPublicListingEndpoint);
    User? currentUser = await AuthService.getUser();

    if (currentUser == null) {
      return {'success': false, 'message': 'User not logged in.'};
    }

    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'lister_id': currentUser.id,
          'title': title,
          'description': description,
          'price': price,
          'category': category,
          'latitude': latitude,
          'longitude': longitude,
          'location_address': locationAddress,
          'preferred_doer_gender': preferredDoerGender,
          'pictures_urls': picturesUrls,
          'is_active': isActive,
          'tags': tags,
        }),
      );
      final responseData = json.decode(response.body);
      return responseData;
    } catch (e) {
      return {'success': false, 'message': 'Network error: $e'};
    }
  }

  Future<Map<String, dynamic>> getListingDetails(int listingId) async {
    final url = Uri.parse('${ApiConfig.getPublicListingEndpoint}?listing_id=$listingId');
    try {
      final response = await http.get(url);
      final responseData = json.decode(response.body);

      if (response.statusCode == 200 && responseData['success']) {
        return {'success': true, 'listing': PublicListing.fromJson(responseData['listing'])};
      } else {
        return {'success': false, 'message': responseData['message'] ?? 'Failed to fetch listing details.'};
      }
    } catch (e) {
      return {'success': false, 'message': 'Network error: $e'};
    }
  }

  // Test server connectivity
  Future<Map<String, dynamic>> testConnection() async {
    final testUrl = Uri.parse('${ApiConfig.baseUrl}/public_listing/test_connection.php');
    print('🧪 Testing server connection: $testUrl');

    try {
      final response = await http.get(testUrl);
      print('🧪 Test Response Status: ${response.statusCode}');
      print('🧪 Test Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return responseData;
      } else {
        return {'success': false, 'message': 'Test failed with status: ${response.statusCode}'};
      }
    } catch (e) {
      print('🧪 Test Exception: $e');
      return {'success': false, 'message': 'Test error: $e'};
    }
  }

  Future<Map<String, dynamic>> updateListing(PublicListing listing, {String? cardType}) async {
    // First test the connection
    print('🧪 Testing server connection before update...');
    final testResult = await testConnection();
    print('🧪 Connection test result: $testResult');

    final url = Uri.parse(ApiConfig.publicUpdateListingEndpoint);

    // Create request body with listing data and optional card type
    final requestData = listing.toJson();
    if (cardType != null) {
      requestData['card_type'] = cardType;
    }

    final requestBody = json.encode(requestData);

    // DEBUG: Print request details
    print('🔄 UPDATE LISTING DEBUG:');
    print('📍 URL: $url');
    print('📦 Request Body: $requestBody');
    print('🏷️ Listing ID: ${listing.id}');
    print('📝 Title: ${listing.title}');
    print('💳 Card Type: ${cardType ?? 'NULL'}');

    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: requestBody,
      );

      // DEBUG: Print response details
      print('📊 Response Status: ${response.statusCode}');
      print('📄 Response Headers: ${response.headers}');
      print('📋 Response Body: ${response.body}');

      if (response.statusCode != 200) {
        print('❌ HTTP Error: ${response.statusCode}');
        return {'success': false, 'message': 'HTTP Error: ${response.statusCode}'};
      }

      if (response.body.isEmpty) {
        print('❌ Empty response body');
        return {'success': false, 'message': 'Empty response from server'};
      }

      final responseData = json.decode(response.body);
      print('✅ Parsed Response: $responseData');
      return responseData;
    } catch (e) {
      print('❌ Exception in updateListing: $e');
      return {'success': false, 'message': 'Network error: $e'};
    }
  }

  Future<Map<String, dynamic>> deleteListing(int listingId) async {
    final url = Uri.parse(ApiConfig.publicDeleteListingEndpoint);
    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'listing_id': listingId}),
      );
      final responseData = json.decode(response.body);
      return responseData;
    } catch (e) {
      return {'success': false, 'message': 'Network error: $e'};
    }
  }

  Future<Map<String, dynamic>> updateListingStatus(int listingId, bool isActive) async {
    final url = Uri.parse(ApiConfig.updateListingStatusEndpoint);
    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'listing_id': listingId, 'is_active': isActive}),
      );
      final responseData = json.decode(response.body);
      return responseData;
    } catch (e) {
      return {'success': false, 'message': 'Network error: $e'};
    }
  }
}