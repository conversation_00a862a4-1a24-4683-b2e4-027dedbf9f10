<?php
require_once '../config/db_connect.php';

date_default_timezone_set('Asia/Manila');

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['email']) || !isset($input['otp'])) {
    echo json_encode(['success' => false, 'message' => 'Email and OTP are required']);
    exit();
}

$email = $input['email'];
$otp = $input['otp'];
$currentTime = date('Y-m-d H:i:s');

try {
    // Verify OTP
    $stmt = $conn->prepare("
        SELECT loc.user_id 
        FROM login_otp_codes loc 
        JOIN users u ON loc.user_id = u.id 
        WHERE u.email = ? AND loc.otp_code = ? AND loc.expires_at > ?
    ");
    $stmt->bind_param("sss", $email, $otp, $currentTime);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        // OTP is valid, delete it
        $deleteStmt = $conn->prepare("DELETE FROM login_otp_codes WHERE user_id = ?");
        $deleteStmt->bind_param("i", $row['user_id']);
        $deleteStmt->execute();
        $deleteStmt->close();
        
        echo json_encode(['success' => true, 'message' => 'Login OTP verified successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Invalid or expired OTP']);
    }
    
    $stmt->close();
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}

$conn->close();
?>