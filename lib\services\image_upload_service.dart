import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart' as http_parser;
import 'dart:convert';
import '../utils/api_config.dart';

class ImageUploadService {
  static final ImageUploadService _instance = ImageUploadService._internal();
  factory ImageUploadService() => _instance;
  ImageUploadService._internal();

  /// Upload multiple images for listings
  Future<Map<String, dynamic>> uploadListingImages(List<File> imageFiles) async {
    try {
      if (imageFiles.isEmpty) {
        return {
          'success': false,
          'message': 'No images to upload'
        };
      }

      print('🖼️ Uploading ${imageFiles.length} images...');

      var request = http.MultipartRequest(
        'POST', 
        Uri.parse(ApiConfig.uploadListingImagesEndpoint)
      );

      // Add each image file
      for (int i = 0; i < imageFiles.length; i++) {
        final file = imageFiles[i];
        
        // Validate file exists
        if (!await file.exists()) {
          print('⚠️ File does not exist: ${file.path}');
          continue;
        }

        // Get file extension and set MIME type
        final extension = file.path.split('.').last.toLowerCase();
        String mimeType;
        switch (extension) {
          case 'jpg':
          case 'jpeg':
            mimeType = 'image/jpeg';
            break;
          case 'png':
            mimeType = 'image/png';
            break;
          case 'gif':
            mimeType = 'image/gif';
            break;
          case 'webp':
            mimeType = 'image/webp';
            break;
          default:
            mimeType = 'image/jpeg'; // Default fallback
        }

        print('🔧 Adding file ${i + 1}: ${file.path} (${mimeType})');

        request.files.add(await http.MultipartFile.fromPath(
          'image_$i', // Use individual field names instead of array notation
          file.path,
          contentType: http_parser.MediaType.parse(mimeType),
        ));
      }

      print('📤 Sending upload request...');
      
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      
      print('📡 Upload response status: ${response.statusCode}');
      print('📡 Upload response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return responseData;
      } else {
        return {
          'success': false,
          'message': 'Upload failed with status: ${response.statusCode}'
        };
      }

    } catch (e) {
      print('❌ Image upload error: $e');
      return {
        'success': false,
        'message': 'Upload error: $e'
      };
    }
  }

  /// Upload a single image (for backward compatibility)
  Future<Map<String, dynamic>> uploadSingleImage(File imageFile) async {
    return await uploadListingImages([imageFile]);
  }
}
