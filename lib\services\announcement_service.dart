import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:hanapp/models/announcement.dart';
import 'package:hanapp/utils/api_config.dart';

class AnnouncementService {
  static String get baseUrl => ApiConfig.baseUrl;

  /// Fetch all active announcements from the API
  Future<List<Announcement>> getActiveAnnouncements() async {
    try {
      final url = Uri.parse('$baseUrl/videos/get_active_announcements.php');
      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final announcementsJson = json.decode(response.body);
        
        if (announcementsJson['success'] == true && announcementsJson['announcements'] is List) {
          return (announcementsJson['announcements'] as List)
              .map((announcementJson) => Announcement.fromJson(announcementJson))
              .toList();
        } else {
          return [];
        }
      } else {
        return [];
      }
    } catch (e) {
      print('Error in getActiveAnnouncements: $e');
      return [];
    }
  }

  /// Fetch announcements by category
  Future<List<Announcement>> getAnnouncementsByCategory(String category) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/videos/get_announcements_by_category.php?category=$category'),
        headers: {
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        
        if (data['success'] == true) {
          final List<dynamic> announcementsJson = data['announcements'] ?? [];
          return announcementsJson.map((json) => Announcement.fromJson(json)).toList();
        } else {
          throw Exception(data['message'] ?? 'Failed to fetch announcements');
        }
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.reasonPhrase}');
      }
    } catch (e) {
      throw Exception('Error fetching announcements by category: $e');
    }
  }

  /// Fetch active videos from the API (video category only)
  Future<List<Announcement>> getActiveVideos() async {
    try {
      final url = Uri.parse('$baseUrl/videos/get_active_videos.php');
      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final videosJson = json.decode(response.body);
        
        if (videosJson['success'] == true && videosJson['videos'] is List) {
          return (videosJson['videos'] as List)
              .map((videoJson) => Announcement.fromJson(videoJson))
              .toList();
        } else {
          return [];
        }
      } else {
        return [];
      }
    } catch (e) {
      print('Error in getActiveVideos: $e');
      return [];
    }
  }

  /// Get announcement categories
  Future<List<String>> getAnnouncementCategories() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/videos/get_announcement_categories.php'),
        headers: {
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        
        if (data['success'] == true) {
          final List<dynamic> categoriesJson = data['categories'] ?? [];
          return categoriesJson.map((json) => json.toString()).toList();
        } else {
          throw Exception(data['message'] ?? 'Failed to fetch categories');
        }
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.reasonPhrase}');
      }
    } catch (e) {
      throw Exception('Error fetching announcement categories: $e');
    }
  }
}