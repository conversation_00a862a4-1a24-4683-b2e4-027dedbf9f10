<?php
// hanapp_backend/api/logout.php
// Handles user logout and sets is_logged_in = 0

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config/db_connect.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST method is allowed');
    }

    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!$data) {
        throw new Exception('Invalid JSON input');
    }

    $user_id = $data['user_id'] ?? null;

    if (empty($user_id)) {
        throw new Exception('User ID is required');
    }

    // Set user as logged out
    $stmt = $conn->prepare("UPDATE users SET is_logged_in = 0 WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    
    if ($stmt->execute()) {
        echo json_encode([
            "success" => true,
            "message" => "User logged out successfully"
        ]);
    } else {
        throw new Exception('Failed to update logout status');
    }
    
    $stmt->close();

} catch (Exception $e) {
    error_log("logout.php error: " . $e->getMessage());
    echo json_encode([
        "success" => false,
        "message" => $e->getMessage()
    ]);
}

$conn->close();
?>