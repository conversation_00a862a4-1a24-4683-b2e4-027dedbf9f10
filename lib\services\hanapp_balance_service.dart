import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:hanapp/utils/api_config.dart';
import 'package:hanapp/models/transaction.dart';

class HanAppBalanceService {
  final String _baseUrl = ApiConfig.baseUrl;

  /// Fetches the user's current HanApp Balance
  Future<Map<String, dynamic>> getHanAppBalance({required int userId}) async {
    final url = Uri.parse('$_baseUrl/api/wallet/get_hanapp_balance.php?user_id=$userId');
    print('TappBalanceService: Fetching balance for user $userId from $url');

    try {
      final response = await http.get(url);
      final decodedResponse = json.decode(response.body);

      print('TappBalanceService Get Balance Response: ${response.statusCode} - $decodedResponse');

      if (response.statusCode == 200 && decodedResponse['success']) {
        return {
          'success': true,
          'balance': double.parse(decodedResponse['balance'].toString()),
          'user_id': decodedResponse['user_id'],
          'message': decodedResponse['message']
        };
      } else {
        return {
          'success': false,
          'message': decodedResponse['message'] ?? 'Failed to fetch Tapp Balance.'
        };
      }
    } catch (e) {
      print('TappBalanceService Error fetching balance: $e');
      return {
        'success': false,
        'message': 'Network error: $e'
      };
    }
  }

  /// Fetches HanApp Balance transaction history
  Future<Map<String, dynamic>> getTransactionHistory({
    required int userId,
    int limit = 50,
    int offset = 0,
  }) async {
    final url = Uri.parse('$_baseUrl/api/wallet/get_hanapp_transactions.php?user_id=$userId&limit=$limit&offset=$offset');
    print('TappBalanceService: Fetching transactions for user $userId from $url');

    try {
      final response = await http.get(url);
      final decodedResponse = json.decode(response.body);

      print('TappBalanceService Get Transactions Response: ${response.statusCode} - $decodedResponse');

      if (response.statusCode == 200 && decodedResponse['success']) {
        List<Transaction> transactions = [];

        if (decodedResponse['transactions'] != null) {
          transactions = (decodedResponse['transactions'] as List)
              .map((txnJson) => Transaction.fromJson(txnJson))
              .toList();
        }

        return {
          'success': true,
          'transactions': transactions,
          'total_count': decodedResponse['total_count'] ?? 0,
          'limit': decodedResponse['limit'] ?? limit,
          'offset': decodedResponse['offset'] ?? offset,
          'has_more': decodedResponse['has_more'] ?? false,
          'message': decodedResponse['message']
        };
      } else {
        return {
          'success': false,
          'message': decodedResponse['message'] ?? 'Failed to fetch transaction history.'
        };
      }
    } catch (e) {
      print('TappBalanceService Error fetching transaction history: $e');
      return {
        'success': false,
        'message': 'Network error: $e'
      };
    }
  }

  /// Check Xendit Live Invoices to see external_id (Reference ID)
  Future<Map<String, dynamic>> checkLiveInvoices({int limit = 5}) async {
    final url = Uri.parse('$_baseUrl/api/wallet/check_xendit_live_invoices.php?limit=$limit');
    print('TappBalanceService: Checking live invoices from Xendit API');

    try {
      final response = await http.get(url);
      print('Live Invoices Response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final decodedResponse = json.decode(response.body);

        if (decodedResponse['success']) {
          final invoices = decodedResponse['invoices'] as List;

          // Log each invoice with its external_id (Reference ID)
          for (var invoice in invoices) {
            print('📋 Invoice: ${invoice['id']}');
            print('🔗 Reference ID (external_id): ${invoice['external_id']}');
            print('💰 Amount: ₱${invoice['amount']}');
            print('📊 Status: ${invoice['status']}');
            print('📝 Description: ${invoice['description']}');
            print('---');
          }

          return decodedResponse;
        }
      }

      return {
        'success': false,
        'message': 'Failed to fetch live invoices'
      };
    } catch (e) {
      print('Error checking live invoices: $e');
      return {
        'success': false,
        'message': 'Network error: $e'
      };
    }
  }



  /// Formats amount for display
  static String formatAmount(double amount) {
    return '₱${amount.toStringAsFixed(2)}';
  }
}
