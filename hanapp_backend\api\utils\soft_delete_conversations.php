<?php
// hanapp_backend/api/utils/soft_delete_conversations.php
// Cron job to soft-delete conversations 1 hour after review submission for completed jobs

require_once '../../config/db_connect.php';

$logFile = __DIR__ . '/soft_delete_log.txt';

try {
    // Find completed applications with reviews submitted >1 hour ago, and conversation not yet deleted
    $sql = "
        SELECT c.id AS conversation_id, r.created_at AS review_time
        FROM conversationsv2 c
        INNER JOIN applicationsv2 a ON c.listing_id = a.listing_id AND c.listing_type = a.listing_type AND c.lister_id = a.lister_id AND c.doer_id = a.doer_id
        INNER JOIN reviews r ON a.id = r.application_id
        WHERE a.status = 'completed'
          AND r.created_at <= DATE_SUB(NOW(), INTERVAL 1 HOUR)
          AND c.deleted_at IS NULL
    ";

    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        throw new Exception("Failed to prepare query: " . $conn->error);
    }
    $stmt->execute();
    $result = $stmt->get_result();

    $deletedCount = 0;
    while ($row = $result->fetch_assoc()) {
        $updateSql = "UPDATE conversationsv2 SET deleted_at = NOW() WHERE id = ?";
        $updateStmt = $conn->prepare($updateSql);
        $updateStmt->bind_param("i", $row['conversation_id']);
        if ($updateStmt->execute()) {
            $deletedCount++;
        }
        $updateStmt->close();
    }
    $stmt->close();

    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Soft-deleted $deletedCount conversations\n", FILE_APPEND);

} catch (Exception $e) {
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Error: " . $e->getMessage() . "\n", FILE_APPEND);
}

$conn->close();
?>