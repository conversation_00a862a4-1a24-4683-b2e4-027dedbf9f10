<?php
// hanapp_backend/api/actions/report_user.php
// <PERSON>les submitting a report against a user with image upload.

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

require_once '../config/db_connect.php';

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // Check if it's a multipart form (for file upload)
    if (empty($_POST) && empty($_FILES)) {
        $input = file_get_contents("php://input");
        $data = json_decode($input, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("Invalid JSON payload.");
        }
    } else {
        $data = $_POST;
    }

    $reporterUserId = $data['reporter_user_id'] ?? null;
    $reportedUserId = $data['reported_user_id'] ?? null;
    $listingId = $data['listing_id'] ?? null;
    $applicationId = $data['application_id'] ?? null;
    $reportReason = $data['report_reason'] ?? null;
    $reportDetails = $data['report_details'] ?? null;

    if (empty($reporterUserId) || !is_numeric($reporterUserId) ||
        empty($reportedUserId) || !is_numeric($reportedUserId) ||
        empty($reportReason)) {
        throw new Exception("Reporter ID, Reported User ID, and Report Reason are required.");
    }

    if ($reporterUserId == $reportedUserId) {
        throw new Exception("You cannot report yourself.");
    }

    $reportImage = null;
    if (isset($_FILES['report_image']) && $_FILES['report_image']['error'] === 0) {
        $uploadDir = '../../uploads/reports/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        $fileName = uniqid() . '_' . basename($_FILES['report_image']['name']);
        $uploadFile = $uploadDir . $fileName;

        if (move_uploaded_file($_FILES['report_image']['tmp_name'], $uploadFile)) {
            $reportImage = 'uploads/reports/' . $fileName;
        } else {
            throw new Exception("Failed to upload image.");
        }
    }

    $sql = "INSERT INTO reports (reporter_user_id, reported_user_id, listing_id, application_id, report_reason, report_details, report_image)
            VALUES (?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);

    if ($stmt === false) {
        throw new Exception("Database query preparation failed: " . $conn->error);
    }

    $stmt->bind_param("iiissss",
        $reporterUserId,
        $reportedUserId,
        $listingId,
        $applicationId,
        $reportReason,
        $reportDetails,
        $reportImage
    );

    if ($stmt->execute()) {
        echo json_encode(["success" => true, "message" => "User reported successfully."]);
    } else {
        throw new Exception("Failed to report user: " . $stmt->error);
    }
    $stmt->close();

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "An error occurred: " . $e->getMessage()
    ]);
} finally {
    if (isset($conn) && $conn instanceof mysqli && $conn->ping()) {
        $conn->close();
    }
}

