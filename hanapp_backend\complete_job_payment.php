<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $applicationId = $input['application_id'] ?? null;
    $doerFee = $input['doer_fee'] ?? null;
    
    if (!$applicationId || !$doerFee) {
        throw new Exception('Missing required parameters');
    }
    
    // Start transaction
    $pdo->beginTransaction();
    
    // Get application details
    $stmt = $pdo->prepare("SELECT doer_id, listing_id FROM applications WHERE id = ?");
    $stmt->execute([$applicationId]);
    $application = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$application) {
        throw new Exception('Application not found');
    }
    
    $doerId = $application['doer_id'];
    $listingId = $application['listing_id'];
    
    // Get job payment invoice details
    $stmt = $pdo->prepare("SELECT * FROM job_payment_invoices WHERE application_id = ? ORDER BY created_at DESC LIMIT 1");
    $stmt->execute([$applicationId]);
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        throw new Exception('Payment invoice not found');
    }
    
    $totalAmount = floatval($invoice['amount']);
    $serviceFee = $totalAmount - $doerFee;
    
    // Add doer fee (not total amount) to doer's total_profit
    $stmt = $pdo->prepare("UPDATE users SET total_profit = total_profit + ? WHERE id = ?");
    $stmt->execute([$doerFee, $doerId]);
    
    // Update application status to completed
    $stmt = $pdo->prepare("UPDATE applications SET status = 'completed', completed_at = NOW() WHERE id = ?");
    $stmt->execute([$applicationId]);
    
    // Update listing status to completed
    $stmt = $pdo->prepare("UPDATE listings SET status = 'completed' WHERE id = ?");
    $stmt->execute([$listingId]);
    
    // Update invoice status to completed
    $stmt = $pdo->prepare("UPDATE job_payment_invoices SET status = 'completed', completed_at = NOW() WHERE id = ?");
    $stmt->execute([$invoice['id']]);
    
    // Create transaction record for doer (earning)
    $stmt = $pdo->prepare("
        INSERT INTO transactions (user_id, type, amount, description, transaction_date, method, status, xendit_invoice_id)
        VALUES (?, ?, ?, ?, NOW(), ?, ?, ?)
    ");
    $stmt->execute([
        $doerId,
        'credit',
        $doerFee,
        "Job earning from application #$applicationId",
        $invoice['payment_method'],
        'completed',
        $invoice['xendit_invoice_id']
    ]);
    
    // Commit transaction
    $pdo->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'Job payment completed successfully',
        'doer_earning' => $doerFee,
        'service_fee' => $serviceFee,
        'total_amount' => $totalAmount
    ]);
    
} catch (Exception $e) {
    // Rollback transaction on error
    if ($pdo->inTransaction()) {
        $pdo->rollback();
    }
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
