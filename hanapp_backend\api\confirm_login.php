<?php
// hanapp_backend/api/confirm_login.php
// Handles user confirmation of legitimate login

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'config/db_connect.php';

header('Content-Type: text/html');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $token = $_GET['token'] ?? null;

    if (empty($token)) {
        throw new Exception("Invalid or missing confirmation token.");
    }

    // Find the login notification record
    $stmt = $conn->prepare("SELECT id, user_id, status, created_at FROM login_notifications WHERE confirmation_token = ?");
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception("Invalid confirmation token.");
    }
    
    $notification = $result->fetch_assoc();
    $stmt->close();

    // Check if token is expired (24 hours)
    $createdAt = new DateTime($notification['created_at']);
    $now = new DateTime();
    $diff = $now->diff($createdAt);
    
    if ($diff->days >= 1) {
        throw new Exception("Confirmation token has expired.");
    }

    // Check if already processed
    if ($notification['status'] !== 'pending') {
        $message = $notification['status'] === 'confirmed' ? 
            "This login has already been confirmed." : 
            "This login has already been processed.";
        
        echo generateResponsePage("Already Processed", $message, "info");
        exit();
    }

    // Update status to confirmed
    $updateStmt = $conn->prepare("UPDATE login_notifications SET status = 'confirmed', confirmed_at = NOW() WHERE id = ?");
    $updateStmt->bind_param("i", $notification['id']);
    
    if ($updateStmt->execute()) {
        echo generateResponsePage(
            "Login Confirmed", 
            "Thank you for confirming this login. Your account remains secure and no further action is needed.",
            "success"
        );
    } else {
        throw new Exception("Failed to confirm login.");
    }
    $updateStmt->close();

} catch (Exception $e) {
    error_log("Login confirmation error: " . $e->getMessage());
    echo generateResponsePage(
        "Error", 
        "An error occurred: " . $e->getMessage(),
        "error"
    );
}

$conn->close();

function generateResponsePage($title, $message, $type) {
    $color = $type === 'success' ? '#28a745' : ($type === 'error' ? '#dc3545' : '#17a2b8');
    $icon = $type === 'success' ? '✓' : ($type === 'error' ? '✗' : 'ℹ');
    
    return '
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>' . htmlspecialchars($title) . ' - TAPP</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                background-color: #f4f4f4;
                margin: 0;
                padding: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
            }
            .container {
                background-color: white;
                padding: 40px;
                border-radius: 10px;
                box-shadow: 0 0 20px rgba(0,0,0,0.1);
                text-align: center;
                max-width: 500px;
                width: 100%;
            }
            .logo {
                font-size: 28px;
                font-weight: bold;
                color: #141CC9;
                margin-bottom: 20px;
            }
            .icon {
                font-size: 48px;
                color: ' . $color . ';
                margin-bottom: 20px;
            }
            h1 {
                color: ' . $color . ';
                margin-bottom: 20px;
            }
            p {
                color: #666;
                line-height: 1.6;
                margin-bottom: 30px;
            }
            .btn {
                display: inline-block;
                background-color: #141CC9;
                color: white;
                padding: 12px 30px;
                text-decoration: none;
                border-radius: 5px;
                font-weight: bold;
            }
            .btn:hover {
                background-color: #0f16a3;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="logo">TAPP</div>
            <div class="icon">' . $icon . '</div>
            <h1>' . htmlspecialchars($title) . '</h1>
            <p>' . htmlspecialchars($message) . '</p>
            <a href="#" onclick="window.close()" class="btn">Close</a>
        </div>
    </body>
    </html>
    ';
}
?>