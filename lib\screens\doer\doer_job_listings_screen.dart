import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:hanapp/viewmodels/doer_job_listings_view_model.dart';
import 'package:hanapp/models/doer_listing_item.dart';
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/screens/doer/doer_job_filter_modal.dart';
import 'package:hanapp/utils/image_utils.dart';
import 'package:hanapp/screens/profile_settings_screen.dart';
import 'package:hanapp/screens/notifications_screen.dart';
import 'package:hanapp/services/notification_service.dart';
import 'package:hanapp/utils/auth_service.dart';
// Removed CombinedListingsScreen import - using doer-specific screens instead
import 'package:hanapp/screens/doer/doer_job_listings_mark_screen.dart';
import 'dart:async';
import 'package:hanapp/services/notification_popup_service.dart';

import 'package:provider/provider.dart';
import 'package:hanapp/models/user.dart';

class DoerJobListingsScreen extends StatefulWidget {
  const DoerJobListingsScreen({super.key});

  @override
  State<DoerJobListingsScreen> createState() => _DoerJobListingsScreenState();
}

class _DoerJobListingsScreenState extends State<DoerJobListingsScreen> {
  final TextEditingController _searchController = TextEditingController();
  bool _isRefreshingButton = false;
  bool _showCenterLoading = false;
  int _selectedIndex = 1; // Set to 1 since this is the job listings tab
  int _unreadCount = 0;

  // Remove the late final declaration and make it a getter instead
  List<Widget> get _screens => <Widget>[
    Container(), // Placeholder for home - will navigate to DoerDashboardScreen
    _buildJobListingsContent(), // Current job listings content
    const DoerJobListingsScreenMark(), // Applications - using doer-specific screen
    Builder(
  builder: (context) => NotificationsScreen(
    userRole: Provider.of<User>(context).role,
  ),
),
    const ProfileSettingsScreen(), // Profile
  ];

  // Screen titles
  static const List<String> _screenTitles = <String>[
    'Doer',
    'Job Listings',
    'Applications', 
    'Notifications',
    'Profile',
  ];

  @override
  void initState() {
    super.initState();
    _loadUnreadCount();
    // Set the callback for navigating to notifications
    NotificationPopupService().setNavigateToNotificationsCallback(() {
      print('DEBUG: Notification callback triggered, current index: $_selectedIndex, navigating to notifications');
      setState(() {
        _selectedIndex = 3; // Navigate to notifications tab
      });
      print('DEBUG: After setState, new index: $_selectedIndex, title should be: ${_screenTitles[_selectedIndex]}');
      _loadUnreadCount(); // Refresh unread count when navigating to notifications
    });
    // Immediate initialization without delay
    print('DEBUG: DoerJobListingsScreen - Starting notification polling service');
    NotificationPopupService().startPolling(context);
    
    // Add initial poll with delay to ensure context is ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      print('DEBUG: DoerJobListingsScreen - Starting notification polling service (post frame callback)');
      NotificationPopupService().startPolling(context);
    });
  }

  @override
  void dispose() {
    print('DEBUG: DoerJobListingsScreen - Stopping notification polling service');
    NotificationPopupService().stopPolling();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadUnreadCount() async {
    final user = await AuthService.getUser();
    if (user != null && user.id != null) {
      final response = await NotificationService().getUnreadCount(userId: user.id!);
      if (response['success']) {
        setState(() {
          _unreadCount = response['unread_count'] ?? 0;
        });
      }
    }
  }

  void _onItemTapped(int index) {
    if (index == 0) {
      // Navigate to DoerDashboardScreen (home)
      Navigator.of(context).pushReplacementNamed('/doer_dashboard');
      return;
    }
    
    if (index == 1) { 
      // Job Listings - navigate to DoerJobListingsScreenMark
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const DoerJobListingsScreenMark()),
      );
      return; 
    } 
    
    // Adjust index for other screens
    int actualScreenIndex = index;
    if (index == 3) { // Notifications
      actualScreenIndex = 3;
      _loadUnreadCount();
    } else if (index == 4) { // Profile
      actualScreenIndex = 4;
    }

    setState(() {
      _selectedIndex = actualScreenIndex;
    });
  }

  Widget _buildNotificationIcon() {
    return Stack(
      children: [
        Icon(
          Icons.notifications,
          color: _selectedIndex == 3 ? Colors.yellow.shade700 : Colors.white70,
        ),
        if (_unreadCount > 0)
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(10),
              ),
              constraints: const BoxConstraints(
                minWidth: 16,
                minHeight: 16,
              ),
              child: Text(
                _unreadCount > 99 ? '99+' : _unreadCount.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }

  // Method to show the filter modal
  void _showFilterModal() async {
    final viewModel = Provider.of<DoerJobListingsViewModel>(context, listen: false);

    final Map<String, dynamic>? filters = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (BuildContext context) {
        return DoerJobFilterModal(
          initialDistance: viewModel.distanceFilter,
          initialMinBudget: viewModel.minBudgetFilter,
          initialDatePosted: viewModel.datePostedFilter,
        );
      },
    );

    if (filters != null) {
      viewModel.applyFilters(
        distance: filters['distance'],
        minBudget: filters['minBudget'],
        datePosted: filters['datePosted'],
      );
    }
  }

  // Method to handle refresh button press
  Future<void> _handleRefreshButton() async {
    setState(() {
      _isRefreshingButton = true;
      _showCenterLoading = true;
    });

    final viewModel = Provider.of<DoerJobListingsViewModel>(context, listen: false);
    await viewModel.fetchJobListings();

    setState(() {
      _isRefreshingButton = false;
      _showCenterLoading = false;
    });
  }

  Widget _buildJobListingsContent() {
    return Column(
      children: [
        // Search and Filter Section
        Container(
          margin: const EdgeInsets.all(8.0),
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Constants.primaryColor,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Search Bar
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search jobs...',
                      prefixIcon: const Icon(Icons.search, color: Colors.grey),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                      contentPadding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    onSubmitted: (value) {
                      Provider.of<DoerJobListingsViewModel>(context, listen: false)
                          .setSearchQuery(value);
                    },
                  ),
                ),
              ),
              const SizedBox(width: 12),
              // Filter Button
              Container(
                decoration: BoxDecoration(
                  color: Constants.primaryColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: IconButton(
                  icon: const Icon(Icons.filter_list, color: Colors.white),
                  onPressed: _showFilterModal,
                  tooltip: 'Filter',
                ),
              ),
            ],
          ),
        ),

        // Category Filter Tabs
        Consumer<DoerJobListingsViewModel>(
          builder: (context, viewModel, child) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildCategoryTab('All', viewModel),
                  _buildCategoryTab('Onsite', viewModel),
                  _buildCategoryTab('Hybrid', viewModel),
                  _buildCategoryTab('Remote', viewModel),
                ],
              ),
            );
          },
        ),

        // Job Listings List with RefreshIndicator
        Expanded(
          child: Stack(
            children: [
              Consumer<DoerJobListingsViewModel>(
                builder: (context, viewModel, child) {
                  return RefreshIndicator(
                    onRefresh: () async {
                      await viewModel.fetchJobListings();
                    },
                    color: Constants.primaryColor,
                    child: viewModel.isLoading && viewModel.listings.isEmpty && !_showCenterLoading
                        ? const Center(child: CircularProgressIndicator())
                        : viewModel.errorMessage != null
                        ? Center(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          'Error: ${viewModel.errorMessage}',
                          textAlign: TextAlign.center,
                          style: const TextStyle(color: Colors.red, fontSize: 16),
                        ),
                      ),
                    )
                        : viewModel.listings.isEmpty
                        ? const Center(
                      child: Text(
                        'No jobs found matching your criteria.',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                    )
                        : ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 12.0),
                      itemCount: viewModel.listings.length,
                      itemBuilder: (context, index) {
                        final listing = viewModel.listings[index];
                        return _buildJobListingCard(listing);
                      },
                    ),
                  );
                },
              ),
              if (_showCenterLoading)
                Container(
                  color: Colors.white.withOpacity(0.8),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Constants.primaryColor),
                          strokeWidth: 3,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Refreshing job listings...',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_screenTitles[_selectedIndex]), // Dynamic title based on selected tab
        backgroundColor: Colors.white,
        foregroundColor: Constants.primaryColor,
        automaticallyImplyLeading: false,
        leading: (_selectedIndex == 3 || _selectedIndex == 4) ? IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            setState(() {
              _selectedIndex = 1; // Return to Job Listings tab
            });
          },
        ) : null,
        actions: [
          // Chat icon moved to AppBar
          IconButton(
            icon: const Icon(Icons.chat_bubble_outline),
            onPressed: () {
              Navigator.of(context).pushNamed('/chat_list');
            },
            tooltip: 'Chats',
          ),
        ],
      ),
      body: _selectedIndex == 1 ? _buildJobListingsContent() : _screens[_selectedIndex],
      bottomNavigationBar: BottomAppBar(
        color: Constants.primaryColor,
        shape: const CircularNotchedRectangle(),
        notchMargin: 8.0,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: <Widget>[
            IconButton(
              icon: const Icon(Icons.home),
              color: _selectedIndex == 0 ? Colors.yellow.shade700 : Colors.white70,
              onPressed: () => _onItemTapped(0),
            ),
            IconButton(
              icon: const Icon(Icons.list_alt),
              color: Colors.white70, // Remove highlighting from list icon
              onPressed: () => _onItemTapped(1),
            ),
            const SizedBox(width: 48), // Space for FAB
            IconButton(
              icon: _buildNotificationIcon(),
              onPressed: () => _onItemTapped(3),
            ),
            IconButton(
              icon: const Icon(Icons.person),
              color: _selectedIndex == 4 ? Colors.yellow.shade700 : Colors.white70,
              onPressed: () => _onItemTapped(4),
            ),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        transform: Matrix4.translationValues(0, _selectedIndex == 1 ? -8.0 : 0.0, 0),
        child: FloatingActionButton(
          onPressed: () {
            // Change functionality based on current screen
            switch (_selectedIndex) {
              case 0: // Home
                Navigator.push(
                  context, 
                  MaterialPageRoute(builder: (context) => const DoerJobListingsScreen())
                );
                break;
              case 1: // Job Listings - show search functionality
                _searchController.clear();
                break;
              case 2: // Applications
                Navigator.of(context).pushNamed('/create_application');
                break;
              case 3: // Notifications
                _loadUnreadCount();
                break;
              case 4: // Profile
                Navigator.of(context).pushNamed('/edit_profile');
                break;
            }
          },
          backgroundColor: Constants.primaryColor,
          shape: const CircleBorder(),
          child: _getFABIcon(),
        ),
      ),
    );
  }

  // Add this method to get the appropriate FAB icon based on selected screen
  Widget _getFABIcon() {
    switch (_selectedIndex) {
      case 0: // Home
        return const Icon(Icons.work, color: Colors.white, size: 35);
      case 1: // Job Listings
        return const Icon(Icons.search, color: Colors.white, size: 35);
      case 2: // Applications
        return const Icon(Icons.add, color: Colors.white, size: 35);
      case 3: // Notifications
        return const Icon(Icons.refresh, color: Colors.white, size: 35);
      case 4: // Profile
        return const Icon(Icons.edit, color: Colors.white, size: 35);
      default:
        return const Icon(Icons.work, color: Colors.white, size: 35);
    }
  }

  Widget _buildCategoryTab(String category, DoerJobListingsViewModel viewModel) {
    bool isSelected = viewModel.selectedCategory == category;
    return GestureDetector(
      onTap: () {
        viewModel.setSelectedCategory(category);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Constants.primaryColor : Colors.grey.shade200,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: isSelected ? Constants.primaryColor : Colors.transparent),
        ),
        child: Text(
          category,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey.shade800,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildJobListingCard(DoerListingItem listing) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12.0),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          if (listing.listingType == 'ASAP') {
            Navigator.of(context).pushNamed(
              '/asap_listing_details',
              arguments: {'listing_id': listing.id},
            );
          } else {
            Navigator.of(context).pushNamed(
              '/public_listing_details',
              arguments: {'listing_id': listing.id},
            );
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundImage: ImageUtils.createProfileImageProvider(listing.listerProfilePictureUrl) ??
                      const AssetImage('assets/11.png') as ImageProvider,
                  onBackgroundImageError: (exception, stackTrace) {
                    print('DoerListing: Error loading profile image: $exception');
                  },
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              listing.title,
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Constants.textColor,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        listing.description ?? 'No description provided.',
                        style: const TextStyle(fontSize: 10, color: Colors.grey),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              listing.locationAddress?.split(',').first.trim() ?? 'Unknown',
                              style: const TextStyle(fontSize: 10, color: Colors.grey),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Flexible(
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: listing.listingType == 'ASAP'
                                    ? Colors.red.shade100
                                    : Constants.primaryColor.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(5),
                              ),
                              child: Text(
                                listing.listingType == 'ASAP' ? 'ASAP' : listing.category,
                                style: TextStyle(
                                  color: listing.listingType == 'ASAP' ? Colors.red : Constants.primaryColor,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    if (listing.price != null && listing.price! > 0)
                      Text(
                        '₱${listing.price!.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Constants.primaryColor,
                        ),
                      ),
                    const SizedBox(height: 4),
                    Text(
                      listing.getTimeAgo(),
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
