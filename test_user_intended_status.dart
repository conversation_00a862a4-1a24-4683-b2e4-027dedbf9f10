import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hanapp/services/app_lifecycle_service.dart';

void main() {
  group('User Intended Status Tests', () {
    test('should preserve offline status when user was manually offline', () async {
      // This test verifies that if a user manually sets themselves offline,
      // they stay offline when returning from background
      final appLifecycleService = AppLifecycleService.instance;
      
      // Simulate user manually setting themselves offline
      await appLifecycleService.testUpdateStatus(false);
      
      // Simulate app going to background (should not change database since already offline)
      await appLifecycleService.handleAppLifecycleState(AppLifecycleState.paused);
      
      // Simulate app resuming (should restore offline status)
      await appLifecycleService.handleAppLifecycleState(AppLifecycleState.resumed);
      
      // User should still be offline
      expect(appLifecycleService.isOnline, false);
    });

    test('should preserve online status when user was manually online', () async {
      // This test verifies that if a user manually sets themselves online,
      // they go offline when backgrounding but return to online when resuming
      final appLifecycleService = AppLifecycleService.instance;
      
      // Simulate user manually setting themselves online
      await appLifecycleService.testUpdateStatus(true);
      
      // Simulate app going to background (should set offline in database)
      await appLifecycleService.handleAppLifecycleState(AppLifecycleState.paused);
      
      // Simulate app resuming (should restore online status)
      await appLifecycleService.handleAppLifecycleState(AppLifecycleState.resumed);
      
      // User should be back online
      expect(appLifecycleService.isOnline, true);
    });

    test('should handle status changes correctly', () async {
      final appLifecycleService = AppLifecycleService.instance;
      
      // Test scenario: User is online, goes to background, comes back
      await appLifecycleService.testUpdateStatus(true);
      expect(appLifecycleService.isOnline, true);
      
      await appLifecycleService.handleAppLifecycleState(AppLifecycleState.paused);
      await appLifecycleService.handleAppLifecycleState(AppLifecycleState.resumed);
      expect(appLifecycleService.isOnline, true);
      
      // Test scenario: User is offline, goes to background, comes back
      await appLifecycleService.testUpdateStatus(false);
      expect(appLifecycleService.isOnline, false);
      
      await appLifecycleService.handleAppLifecycleState(AppLifecycleState.paused);
      await appLifecycleService.handleAppLifecycleState(AppLifecycleState.resumed);
      expect(appLifecycleService.isOnline, false);
    });
  });
}

// Manual test widget for user intended status
class UserIntendedStatusTestWidget extends StatefulWidget {
  @override
  _UserIntendedStatusTestWidgetState createState() => _UserIntendedStatusTestWidgetState();
}

class _UserIntendedStatusTestWidgetState extends State<UserIntendedStatusTestWidget> {
  String _status = 'Unknown';
  bool _isOnline = false;
  String _lastAction = 'None';

  @override
  void initState() {
    super.initState();
    _initializeService();
  }

  Future<void> _initializeService() async {
    try {
      await AppLifecycleService.instance.initialize();
      setState(() {
        _isOnline = AppLifecycleService.instance.isOnline;
        _status = 'Service initialized';
      });
    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
    }
  }

  Future<void> _setOnline() async {
    setState(() {
      _lastAction = 'Setting Online';
    });
    
    try {
      await AppLifecycleService.instance.testUpdateStatus(true);
      setState(() {
        _isOnline = AppLifecycleService.instance.isOnline;
        _status = 'Set to Online';
      });
    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
    }
  }

  Future<void> _setOffline() async {
    setState(() {
      _lastAction = 'Setting Offline';
    });
    
    try {
      await AppLifecycleService.instance.testUpdateStatus(false);
      setState(() {
        _isOnline = AppLifecycleService.instance.isOnline;
        _status = 'Set to Offline';
      });
    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
    }
  }

  Future<void> _simulateBackground() async {
    setState(() {
      _lastAction = 'Simulating Background';
    });
    
    try {
      await AppLifecycleService.instance.handleAppLifecycleState(AppLifecycleState.paused);
      setState(() {
        _isOnline = AppLifecycleService.instance.isOnline;
        _status = 'App Backgrounded';
      });
    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
    }
  }

  Future<void> _simulateResume() async {
    setState(() {
      _lastAction = 'Simulating Resume';
    });
    
    try {
      await AppLifecycleService.instance.handleAppLifecycleState(AppLifecycleState.resumed);
      setState(() {
        _isOnline = AppLifecycleService.instance.isOnline;
        _status = 'App Resumed';
      });
    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('User Intended Status Test'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Status:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text(_status),
                    SizedBox(height: 16),
                    Text(
                      'Online Status: ${_isOnline ? "Online" : "Offline"}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: _isOnline ? Colors.green : Colors.red,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text('Last Action: $_lastAction'),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Manual Status Control:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _setOnline,
                            child: Text('Set Online'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                            ),
                          ),
                        ),
                        SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _setOffline,
                            child: Text('Set Offline'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Background Simulation:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _simulateBackground,
                            child: Text('Simulate Background'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                            ),
                          ),
                        ),
                        SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _simulateResume,
                            child: Text('Simulate Resume'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Test Scenarios:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text('1. Set Online → Background → Resume (should stay online)'),
                    Text('2. Set Offline → Background → Resume (should stay offline)'),
                    Text('3. Check database after each action'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 