<?php
// hanapp_backend/api/applications/get_application_details.php
// Fetches details for a specific application.

// Crucial for production: Do NOT display errors directly
// Log all errors to the server error log instead.
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

require_once '../config/db_connect.php'; // Adjust path if necessary

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}



try {
 
    $applicationId = $_GET['application_id'] ?? null;

    if (empty($applicationId) || !is_numeric($applicationId)) {
        error_log("get_application_details.php: Validation failed - Application ID is missing or invalid. Received application_id: " . var_export($applicationId, true), 0);
        throw new Exception("Application ID is required and must be numeric.");
    }

    $sql = "
        SELECT
            id,
            listing_id,
            listing_type,
            lister_id,
            doer_id,
            listing_title,
            message,
            status,
            applied_at,
            conversation_id
        FROM
            applicationsv2
        WHERE
            id = ?
    ";
    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        error_log("get_application_details.php: Failed to prepare statement: " . $conn->error, 0);
        throw new Exception("Database query preparation failed.");
    }
    $stmt->bind_param("i", $applicationId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($application = $result->fetch_assoc()) {
        if (!isset($application['listing_id']) || empty($application['listing_id'])) {
            error_log("get_application_details.php: Listing ID is missing or empty for application ID: $applicationId", 0);
            throw new Exception("Listing ID is required but not found.");
        }
        // Ensure other required fields if needed
        echo json_encode([
            "success" => true,
            "application" => $application
        ]);
    } else {
        echo json_encode([
            "success" => false,
            "message" => "Application not found."
        ]);
    }
    $stmt->close();

} catch (Exception $e) {
    // Log the actual error message
    error_log("get_application_details.php: Caught exception: " . $e->getMessage(), 0);
    // Send a generic error message to the client for security
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "An internal server error occurred. Please check server logs."
    ]);
} finally {
    // Always close the database connection if it was opened and is still active
    if (isset($conn) && $conn instanceof mysqli && $conn->ping()) {
        $conn->close();
    }
}
