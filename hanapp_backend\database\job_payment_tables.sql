-- Create job payment invoices table
CREATE TABLE IF NOT EXISTS job_payment_invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    application_id INT NOT NULL,
    lister_id INT NOT NULL,
    doer_id INT NOT NULL,
    listing_id INT NOT NULL,
    xendit_invoice_id VARCHAR(255) NOT NULL,
    external_id VARCHAR(255) NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    doer_fee DECIMAL(10, 2) NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    status ENUM('pending', 'completed', 'failed', 'expired') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    failed_at TIMESTAMP NULL,
    
    INDEX idx_application_id (application_id),
    INDEX idx_lister_id (lister_id),
    INDEX idx_doer_id (doer_id),
    INDEX idx_listing_id (listing_id),
    INDEX idx_xendit_invoice_id (xendit_invoice_id),
    INDEX idx_external_id (external_id),
    INDEX idx_status (status),
    
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
    FOREIGN KEY (lister_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (doer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (listing_id) REFERENCES listings(id) ON DELETE CASCADE
);

-- Create payment failure logs table
CREATE TABLE IF NOT EXISTS payment_failure_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    application_id INT NOT NULL,
    failure_reason TEXT,
    failed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_application_id (application_id),
    INDEX idx_failed_at (failed_at),
    
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE
);
