<?php
// hanapp_backend/api/asap/search_doers.php
// Search for available doers for an ASAP listing

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../../config/db_connect.php';

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $listingId = $input['listing_id'] ?? null;
    $listerLatitude = $input['lister_latitude'] ?? null;
    $listerLongitude = $input['lister_longitude'] ?? null;
    $preferredDoerGender = $input['preferred_doer_gender'] ?? 'Any';
    $maxDistance = $input['max_distance'] ?? 10; // Default 10km
    $currentTime = date('Y-m-d H:i:s');
    
    if (!$listingId || !$listerLatitude || !$listerLongitude) {
        throw new Exception('Missing required parameters: listing_id, lister_latitude, lister_longitude');
    }
    
    // First, get the ASAP listing details
    $listingQuery = "SELECT * FROM asap_listings WHERE id = ? AND status = 'pending'";
    $listingStmt = $conn->prepare($listingQuery);
    $listingStmt->bind_param('i', $listingId);
    $listingStmt->execute();
    $listingResult = $listingStmt->get_result();
    
    if ($listingResult->num_rows === 0) {
        throw new Exception('ASAP listing not found or not in pending status');
    }
    
    $listing = $listingResult->fetch_assoc();
    
    // Modified doer search query to include acceptance status AND price offers
    $sql = "
        SELECT DISTINCT u.*, ala.status as acceptance_status, apo.offered_price, apo.status as offer_status,
               (6371 * acos(cos(radians(?)) * cos(radians(u.latitude)) * 
               cos(radians(u.longitude) - radians(?)) + sin(radians(?)) * 
               sin(radians(u.latitude)))) AS distance_km
        FROM users u
        LEFT JOIN asap_listing_acceptance ala ON u.id = ala.doer_id AND ala.listing_id = ?
        LEFT JOIN asap_price_offers apo ON u.id = apo.doer_id AND apo.listing_id = ? AND apo.status = 'pending'
        WHERE u.role = 'doer' 
        AND u.is_available = 1 
        AND u.id != ?
        AND (ala.status = 'accepted' OR apo.offered_price IS NOT NULL OR ala.status IS NULL)
    ";
    
    $params = [(float)$listerLatitude, (float)$listerLongitude, (float)$listerLatitude, $listingId, $listingId, $listing['lister_id']];
    $types = 'dddiii';
    
    // Add gender filter if specified
    if ($preferredDoerGender !== 'Any') {
        $sql .= " AND u.gender = ?";
        $params[] = $preferredDoerGender;
        $types .= 's';
    }
    
    // Show doers who have either accepted OR submitted price offers
    $sql .= " AND (ala.status = 'accepted' OR apo.offered_price IS NOT NULL)";
    
    // Add distance filter
    $sql .= " HAVING distance_km <= ?
            ORDER BY 
                CASE WHEN ala.status = 'accepted' THEN 1 ELSE 2 END,
                distance_km ASC, u.average_rating DESC, u.updated_at DESC
            LIMIT 10";
    $params[] = (float)$maxDistance;
    $types .= 'd';
    
    // Debug logging
    error_log('SQL: ' . $sql);
    error_log('Params: ' . json_encode($params));
    error_log('Types: ' . $types);
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception('SQL prepare failed: ' . $conn->error);
    }
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    error_log('Rows found: ' . $result->num_rows);
    $doers = [];
    while ($row = $result->fetch_assoc()) {
        $doers[] = [
            'id' => $row['id'],
            'full_name' => $row['full_name'],
            'profile_picture_url' => $row['profile_picture_url'],
            'latitude' => $row['latitude'],
            'longitude' => $row['longitude'],
            'address_details' => $row['address_details'],
            'average_rating' => (float)$row['average_rating'],
            'total_reviews' => $row['review_count'],
            'is_verified' => (bool)$row['is_verified'],
            'is_id_verified' => (bool)$row['id_verified'],
            'is_badge_acquired' => (bool)$row['badge_acquired'],
            'distance_km' => round($row['distance_km'], 2),
            'is_available' => true,
            'last_active' => $row['updated_at'] ?? null,
            'acceptance_status' => $row['acceptance_status'],
            'offered_price' => $row['offered_price'] ? (float)$row['offered_price'] : null,
            'offer_status' => $row['offer_status'],
        ];
    }
    
    // NEW: Send notifications to newly discovered doers
    if ($maxDistance > 10) { // Only send notifications if scanning beyond initial 10km
        error_log("=== RESCAN NOTIFICATION DEBUG START ===");
        error_log("Max distance: {$maxDistance}km");
        
        // Find doers within the new radius
        $newDoerQuery = "
            SELECT u.id, u.latitude, u.longitude, 
                   CONCAT(u.first_name, ' ', u.last_name) AS doer_name
            FROM users u
            WHERE u.role = 'doer' 
            AND u.is_available = 1
            AND u.id != ?
            AND u.latitude IS NOT NULL
            AND u.longitude IS NOT NULL
            AND (
                6371 * acos(
                    cos(radians(?)) * cos(radians(u.latitude)) * 
                    cos(radians(u.longitude) - radians(?)) + 
                    sin(radians(?)) * sin(radians(u.latitude))
                )
            ) <= ?
        ";
        
        // Add gender filter for new doers if specified
        if ($preferredDoerGender !== 'Any') {
            $newDoerQuery .= " AND u.gender = ?";
        }
        
        $newDoerStmt = $conn->prepare($newDoerQuery);
        if ($newDoerStmt) {
            if ($preferredDoerGender !== 'Any') {
                $newDoerStmt->bind_param("idddds", $listing['lister_id'], $listerLatitude, $listerLongitude, $listerLatitude, $maxDistance, $preferredDoerGender);
            } else {
                $newDoerStmt->bind_param("idddd", $listing['lister_id'], $listerLatitude, $listerLongitude, $listerLatitude, $maxDistance);
            }
            
            $newDoerStmt->execute();
            $newDoerResult = $newDoerStmt->get_result();
            
            $newDoersCount = $newDoerResult->num_rows;
            error_log("New doers to notify: $newDoersCount");
            
            if ($newDoersCount > 0) {
                // Get lister's name for notification
                $listerQuery = "SELECT CONCAT(first_name, ' ', last_name) AS lister_name FROM users WHERE id = ?";
                $listerStmt = $conn->prepare($listerQuery);
                $listerStmt->bind_param("i", $listing['lister_id']);
                $listerStmt->execute();
                $listerResult = $listerStmt->get_result();
                if ($listerRow = $listerResult->fetch_assoc()) {
                    $listerName = $listerRow['lister_name'];
                } else {
                    $listerName = 'Unknown Lister';
                }
                $listerStmt->close();
                
                // Define notification details
                $notificationType = 'asap_listing_available';
                $notificationTitle = "New ASAP Listing: {$listing['title']}";
                $notificationContent = "A new ASAP listing is available near you at {$listing['location_address']}. Fee: ₱{$listing['doer_fee']}. Tap to view details.";
                
                // Prepare insert statement for new notifications
                $insertSql = "INSERT INTO doer_notifications (
                    user_id, sender_id, type, title, content, associated_id,
                    related_listing_title, listing_id, listing_type, lister_id,
                    lister_name, is_read, created_at
                ) VALUES (
                    ?, ?, ?, ?, ?, ?,
                    ?, ?, 'asap', ?,
                    ?, 0, NOW()
                )";
                
                $insertStmt = $conn->prepare($insertSql);
                if ($insertStmt) {
                    $notificationsCreated = 0;
                    while ($newDoer = $newDoerResult->fetch_assoc()) {
                        $doerId = $newDoer['id'];
                        $doerName = $newDoer['doer_name'];
                        error_log("Creating notification for new doer $doerId ($doerName)");
                        
                        $insertStmt->bind_param(
                            "iissssiiss",
                            $doerId,
                            $listing['lister_id'],
                            $notificationType,
                            $notificationTitle,
                            $notificationContent,
                            $listingId,
                            $listing['title'],
                            $listingId,
                            $listing['lister_id'],
                            $listerName
                        );
                        
                        if ($insertStmt->execute()) {
                            $notificationsCreated++;
                            error_log("Successfully created notification for doer $doerId");
                        } else {
                            error_log("Failed to create notification for doer $doerId: " . $insertStmt->error);
                        }
                    }
                    $insertStmt->close();
                    error_log("Total notifications created: $notificationsCreated");
                } else {
                    error_log("Failed to prepare notification insert statement: " . $conn->error);
                }
            } else {
                error_log("No new doers found to notify in expanded radius");
            }
            $newDoerStmt->close();
        } else {
            error_log("Failed to prepare new doer query: " . $conn->error);
        }
        
        error_log("=== RESCAN NOTIFICATION DEBUG END ===");
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Doers found successfully',
        'doers' => $doers,
        'total_count' => count($doers),
        'listing' => [
            'id' => $listing['id'],
            'title' => $listing['title'],
            'price' => $listing['price'],
            'location_address' => $listing['location_address'],
        ]
    ]);
    
} catch (Exception $e) {
    error_log("search_doers.php error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>