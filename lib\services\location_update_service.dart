import 'package:http/http.dart' as http;
import 'dart:convert';
import '../utils/api_config.dart';
import '../utils/location_service.dart';
import '../utils/auth_service.dart';

class LocationUpdateService {
  static Future<bool> updateUserLocation({
    required int userId,
    required double latitude,
    required double longitude,
  }) async {
    try {
      print('LocationUpdateService: Updating location for user $userId');
      
      // Get current user data to include required fields
      final user = await AuthService.getUser();
      if (user == null) {
        print('LocationUpdateService: No user data found');
        return false;
      }
      
      // Get address from coordinates using reverse geocoding
      final locationService = LocationService();
      String? addressDetails = await locationService.getAddressFromCoordinates(latitude, longitude);
      
      print('LocationUpdateService: Sending request to ${ApiConfig.updateProfileEndpoint}');
      
      final requestBody = {
        'user_id': userId,
        'full_name': user.fullName, // Required by update_profile.php
        'email': user.email, // Required by update_profile.php
        'contact_number': user.contactNumber,
        'latitude': latitude,
        'longitude': longitude,
        'address_details': addressDetails ?? 'Location coordinates: $latitude, $longitude',
      };
      
      print('LocationUpdateService: Request body: $requestBody');
      
      final response = await http.post(
        Uri.parse(ApiConfig.updateProfileEndpoint),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(requestBody),
      );
      
      print('LocationUpdateService: Response status: ${response.statusCode}');
      print('LocationUpdateService: Response body: ${response.body}');
      
      final responseData = json.decode(response.body);
      final success = responseData['success'] ?? false;
      
      if (success) {
        print('LocationUpdateService: Location updated successfully in database');
      } else {
        print('LocationUpdateService: Failed to update location: ${responseData['message']}');
      }
      
      return success;
    } catch (e) {
      print('LocationUpdateService: Error updating user location: $e');
      return false;
    }
  }
}