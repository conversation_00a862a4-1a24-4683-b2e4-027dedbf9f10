class Conversation {
  final int conversationId;
  final int listingId;
  final String listingType;
  final int listerId;
  final int doerId;
  final int? applicationId;
  final String? applicationStatus;
  final bool? paymentConfirmed;
  final DateTime? projectStartDate;
  final String? listingLocationAddress;
  final String? listingTitle;
  final String? listerFullName;
  final String? listerProfilePictureUrl;
  final String? listerAddressDetails;
  final String? doerFullName;
  final String? doerProfilePictureUrl;
  final String? doerAddressDetails;
  // Add these new fields
  final double? listingLatitude;
  final double? listingLongitude;
  final String? preferredDoerGender;
  final double? maxDistance;
  // Add lister and doer location fields
  final double? listerLatitude;
  final double? listerLongitude;
  final double? doerLatitude;
  final double? doerLongitude;

  Conversation({
    required this.conversationId,
    required this.listingId,
    required this.listingType,
    required this.listerId,
    required this.doerId,
    this.applicationId,
    this.applicationStatus,
    this.paymentConfirmed,
    this.projectStartDate,
    this.listingLocationAddress,
    this.listingTitle,
    this.listerFullName,
    this.listerProfilePictureUrl,
    this.listerAddressDetails,
    this.doerFullName,
    this.doerProfilePictureUrl,
    this.doerAddressDetails,
    // Add these new parameters
    this.listingLatitude,
    this.listingLongitude,
    this.preferredDoerGender,
    this.maxDistance,
    // Add lister and doer location parameters
    this.listerLatitude,
    this.listerLongitude,
    this.doerLatitude,
    this.doerLongitude,
  });

  factory Conversation.fromJson(Map<String, dynamic> json, int currentUserId) {
    return Conversation(
      conversationId: int.parse(json['id']?.toString() ?? '0'),
      listingId: int.parse(json['listing_id']?.toString() ?? '0'),
      listingType: json['listing_type']?.toString() ?? 'PUBLIC',
      listerId: int.parse(json['lister_id']?.toString() ?? '0'),
      doerId: int.parse(json['doer_id']?.toString() ?? '0'),
      applicationId: json['application_id'] != null ? int.parse(json['application_id'].toString()) : null,
      applicationStatus: json['application_status']?.toString(),
      projectStartDate: json['project_start_date'] != null 
          ? DateTime.tryParse(json['project_start_date'].toString())?.toLocal() 
          : null,
      listingLocationAddress: json['listing_location_address']?.toString(),
      listingTitle: json['listing_title']?.toString(),
      listerFullName: json['lister_name']?.toString(),
      listerProfilePictureUrl: json['lister_profile_picture_url']?.toString(),
      listerAddressDetails: json['lister_address_details']?.toString(),
      doerFullName: json['doer_name']?.toString(),
      doerProfilePictureUrl: json['doer_profile_picture_url']?.toString(),
      doerAddressDetails: json['doer_address_details']?.toString(),
      // Add these new fields from JSON
      listingLatitude: json['listing_latitude'] != null ? double.tryParse(json['listing_latitude'].toString()) : null,
      listingLongitude: json['listing_longitude'] != null ? double.tryParse(json['listing_longitude'].toString()) : null,
      preferredDoerGender: json['preferred_doer_gender']?.toString(),
      maxDistance: json['max_distance'] != null ? double.tryParse(json['max_distance'].toString()) : null,
      // Add lister and doer location fields from JSON
      listerLatitude: json['lister_latitude'] != null ? double.tryParse(json['lister_latitude'].toString()) : null,
      listerLongitude: json['lister_longitude'] != null ? double.tryParse(json['lister_longitude'].toString()) : null,
      doerLatitude: json['doer_latitude'] != null ? double.tryParse(json['doer_latitude'].toString()) : null,
      doerLongitude: json['doer_longitude'] != null ? double.tryParse(json['doer_longitude'].toString()) : null,
    );
  }

  // Helper method to get the 'other' user's full name based on currentUserId
  String? getOtherUserFullName(int currentUserId) {
    if (currentUserId == listerId) {
      return doerFullName;
    } else if (currentUserId == doerId) {
      return listerFullName;
    }
    return null; // Should not happen if currentUserId is part of the conversation
  }

  // Helper method to get the 'other' user's address based on currentUserId
  String? getOtherUserAddress(int currentUserId) {
    if (currentUserId == listerId) {
      return doerAddressDetails;
    } else if (currentUserId == doerId) {
      return listerAddressDetails;
    }
    return null;
  }

  // Helper method to get the 'other' user's profile picture URL based on currentUserId
  String? getOtherUserProfilePictureUrl(int currentUserId) {
    if (currentUserId == listerId) {
      return doerProfilePictureUrl;
    } else if (currentUserId == doerId) {
      return listerProfilePictureUrl;
    }
    return null;
  }
}
