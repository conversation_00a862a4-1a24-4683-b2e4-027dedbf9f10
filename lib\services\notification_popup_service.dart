import 'package:flutter/material.dart';
import 'package:hanapp/models/notification_model.dart';
import 'package:hanapp/widgets/notification_popup.dart';
import 'package:hanapp/services/notification_service.dart';
import 'package:hanapp/utils/auth_service.dart';
import 'dart:async';
// Import the navigatorKey
import 'package:hanapp/main.dart';
import 'package:hanapp/widgets/asap_notification_popup.dart';
import 'package:hanapp/screens/notifications_screen.dart';
import 'package:hanapp/utils/constants.dart' as Constants;

class NotificationPopupService {
  final List<OverlayEntry> _activeOverlays = [];
  static final NotificationPopupService _instance = NotificationPopupService._internal();
  factory NotificationPopupService() => _instance;
  NotificationPopupService._internal();

  final List<NotificationModel> _recentNotifications = [];
  final int _maxRecentNotifications = 10;
  Timer? _pollingTimer;
  bool _isPolling = false;
  BuildContext? _currentContext;
  int? _lastNotificationId = 0;
  
  // Reference counting to handle multiple screens
  int _referenceCount = 0;
  
  // Callback function to navigate to notifications tab
  Function()? _navigateToNotificationsCallback;

  // Set the callback for navigating to notifications
  void setNavigateToNotificationsCallback(Function() callback) {
    _navigateToNotificationsCallback = callback;
    print('DEBUG: NotificationPopupService callback set');
  }

  // Start polling for new notifications
  void startPolling(BuildContext context) {
    _referenceCount++;
    print('NotificationPopupService: startPolling called, reference count: $_referenceCount');
    
    if (_isPolling) {
      // Update context to the latest one
      _currentContext = context;
      return;
    }
    
    _currentContext = context;
    _isPolling = true;
    
    // Poll immediately
    _pollForNotifications();
    
    // Then poll every 30 seconds
    _pollingTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _pollForNotifications();
    });
    
    print('NotificationPopupService: Started polling for notifications');
  }

  // Stop polling
  void stopPolling() {
    if (_referenceCount > 0) {
      _referenceCount--;
    }
    print('NotificationPopupService: stopPolling called, reference count: $_referenceCount');
    
    // Only actually stop polling when no screens are using it
    if (_referenceCount <= 0) {
      _pollingTimer?.cancel();
      _pollingTimer = null;
      _isPolling = false;
      _currentContext = null;
      _referenceCount = 0; // Ensure it doesn't go negative
      print('NotificationPopupService: Actually stopped polling for notifications');
    }
  }

  // Poll for new notifications
  // Add a set to track processed notifications
  final Set<int> _processedNotificationIds = <int>{};
  
  // Update the polling method
  // Move polling method BEFORE startPolling
  Future<void> _pollForNotifications() async {
    if (_currentContext == null || !_currentContext!.mounted) {
      stopPolling();
      return;
    }
  
    try {
      final user = await AuthService.getUser();
      if (user == null || user.id == null) {
        print('NotificationPopupService: No user found or user ID is null');
        return;
      }
      
      print('NotificationPopupService: Polling for notifications for user ${user.id} (${user.role})');
  
      List<NotificationModel> allNotifications = [];
      
      // Fetch regular notifications
      Map<String, dynamic> response;
      if (user.role == 'lister') {
        response = await NotificationService().getNotifications(userId: user.id!);
      } else {
        response = await NotificationService().getDoerNotifications(userId: user.id!);
      }
      
      if (response['success'] && response['notifications'] != null) {
        allNotifications.addAll(response['notifications']);
      }
      
      // Fetch ASAP notifications separately for doers (these are for popup display only)
      if (user.role == 'doer') {
        final asapResponse = await NotificationService().getAsapNotifications(userId: user.id!);
        if (asapResponse['success'] && asapResponse['notifications'] != null) {
          allNotifications.addAll(asapResponse['notifications']);
        }
      }
      
      print('NotificationPopupService: Total notifications fetched: ${allNotifications.length}');

      if (allNotifications.isNotEmpty) {
        final List<NotificationModel> notifications = allNotifications;
        
        // Log all notifications for debugging
        print('NotificationPopupService: All notifications:');
        for (final notification in notifications) {
          print('  - ID: ${notification.id}, Type: ${notification.type}, Read: ${notification.isRead}, Created: ${notification.createdAt}');
        }
        
        // Calculate 30 seconds ago - only show popups for notifications within this timeframe
        final thirtySecondsAgo = DateTime.now().subtract(const Duration(seconds: 30));
        print('NotificationPopupService: Looking for notifications after: $thirtySecondsAgo');
        
        // Find new unread notifications from the last 30 seconds only
        // This prevents repeated popups on app restart for older notifications
        final newNotifications = notifications.where((notification) {
          final isUnread = !notification.isRead;
          final isRecent = notification.createdAt.isAfter(thirtySecondsAgo);
          final isNew = notification.id > (_lastNotificationId ?? 0);
          final notProcessed = !_processedNotificationIds.contains(notification.id);
          
          print('NotificationPopupService: Checking notification ${notification.id}: unread=$isUnread, recent=$isRecent (created: ${notification.createdAt}), new=$isNew, notProcessed=$notProcessed');
          
          // Only show popup if notification is unread, recent (within 30 seconds), new, and not processed
          // This ensures no popups for notifications older than 30 seconds, preventing repeated popups on app restart
          return isUnread && isRecent && isNew && notProcessed;
        }).toList();
        
        print('NotificationPopupService: Found ${newNotifications.length} new notifications to show');
  
        // Show popup for each new notification
        for (final notification in newNotifications) {
          print('NotificationPopupService: Showing popup for notification ${notification.id} (${notification.type})');
          if (_currentContext != null && _currentContext!.mounted) {
            _showNotificationPopup(_currentContext!, notification);
            _lastNotificationId = notification.id;
            _processedNotificationIds.add(notification.id); // Mark as processed
          }
        }
  
        if (newNotifications.isNotEmpty) {
          print('NotificationPopupService: Found ${newNotifications.length} new notifications from last 30 seconds');
        }
        
        // Clean up old processed notification IDs to prevent memory leaks
        // Remove IDs for notifications older than 5 minutes
        final fiveMinutesAgo = DateTime.now().subtract(const Duration(minutes: 5));
        final oldNotificationIds = notifications
            .where((notification) => notification.createdAt.isBefore(fiveMinutesAgo))
            .map((notification) => notification.id)
            .toSet();
        _processedNotificationIds.removeAll(oldNotificationIds);
      } else {
        print('NotificationPopupService: API call failed or no notifications returned');
      }
    } catch (e) {
      print('NotificationPopupService: Error polling for notifications: $e');
    }
  }

  // Show a popup notification manually
  void showNotification(BuildContext context, NotificationModel notification) {
    _showNotificationPopup(context, notification);
  }

  // Show a popup notification
  void _showNotificationPopup(BuildContext context, NotificationModel notification) {
    if (!context.mounted) return;

    final overlay = Overlay.of(context);
    OverlayEntry? overlayEntry;
    
    // Check if this is an ASAP listing notification - be more specific
    final isAsapNotification = notification.type == 'asap_listing_available';
    
    // Ensure these specific notification types always use the original design
    final shouldUseOriginalDesign = [
      'review_submitted',
      'job_completed', 
      'job_completed_by_lister',
      'job_completed_by_doer',
      'new_review',
      'review_received',
      'project_completed',
      'project_started',
      'job_marked_complete'
    ].contains(notification.type);
    
    final useAsapPopup = isAsapNotification && !shouldUseOriginalDesign;
    
    overlayEntry = OverlayEntry(
      builder: (context) => Material(
        color: Colors.transparent,
        child: Stack(
          children: [
            // Clickable background
            Positioned.fill(
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  overlayEntry?.remove(); // Remove the specific overlay
                  _activeOverlays.remove(overlayEntry); // Remove from tracking list
                  if (useAsapPopup) {
                    // Navigate to ASAP acceptance screen
                    Future.microtask(() {
                      navigatorKey.currentState?.pushNamed(
                        '/asap_listing_acceptance',
                        arguments: {
                          'listing_id': notification.associatedId ?? 0,
                          'notification_id': notification.id.toString(),
                        },
                      );
                    });
                  } else {
                    Future.microtask(() {
                      print('DEBUG: Notification popup tapped, navigating to notifications');
                      _navigateToNotification(context, notification);
                    });
                  }
                },
              ),
            ),
            // Notification popup
            Positioned(
              top: MediaQuery.of(context).viewPadding.top + 16,
              left: 16,
              right: 16,
              child: useAsapPopup
                ? AsapNotificationPopup(
                    notification: notification,
                    onTap: () {
                      overlayEntry?.remove(); // Remove the specific overlay
                      _activeOverlays.remove(overlayEntry); // Remove from tracking list
                      Future.microtask(() {
                        navigatorKey.currentState?.pushNamed(
                          '/asap_listing_acceptance',
                          arguments: {
                            'listing_id': notification.associatedId ?? 0,
                            'notification_id': notification.id.toString(),
                          },
                        );
                      });
                    },
                    onDismiss: () {
                      overlayEntry?.remove(); // Remove the specific overlay
                      _activeOverlays.remove(overlayEntry); // Remove from tracking list
                    },
                  )
                : NotificationPopup(
                    notification: notification,
                    onTap: () {
                      overlayEntry?.remove(); // Remove the specific overlay
                      _activeOverlays.remove(overlayEntry); // Remove from tracking list
                      Future.microtask(() {
                        print('DEBUG: NotificationPopup widget tapped, navigating to notifications');
                        _navigateToNotification(context, notification);
                      });
                    },
                    onDismiss: () {
                      overlayEntry?.remove(); // Remove the specific overlay
                      _activeOverlays.remove(overlayEntry); // Remove from tracking list
                    },
                  ),
            ),
          ],
        ),
      ),
    );

    overlay.insert(overlayEntry);
    _activeOverlays.add(overlayEntry); // Add to tracking list
  
    // Auto-dismiss after duration (30 seconds for ASAP, 2 seconds for regular)
    final duration = useAsapPopup ? const Duration(seconds: 30) : const Duration(seconds: 2);
    Future.delayed(duration, () {
      if (_activeOverlays.contains(overlayEntry)) {
        overlayEntry?.remove();
        _activeOverlays.remove(overlayEntry);
      }
    });
  }

  // Handle notification tap
  void _handleNotificationTap(BuildContext context, NotificationModel notification) {
    // Mark notification as read
    _markNotificationAsRead(notification);
    
    // Navigate based on notification type
    _navigateToNotification(context, notification);
  }

  // Mark notification as read
  Future<void> _markNotificationAsRead(NotificationModel notification) async {
    try {
      final user = await AuthService.getUser();
      if (user == null) return;

      if (user.role == 'lister') {
        await NotificationService().markNotificationAsRead(notificationId: notification.id);
      } else {
        await NotificationService().markDoerNotificationAsRead(notificationId: notification.id);
      }
    } catch (e) {
      print('NotificationPopupService: Error marking notification as read: $e');
    }
  }

  // Navigate to appropriate screen based on notification type
  void _navigateToNotification(BuildContext context, NotificationModel notification) async {
    print('DEBUG: _navigateToNotification called, navigating to notifications screen');
    
    // Get current user to determine role
    final user = await AuthService.getUser();
    if (user == null) return;
    
    // Navigate to notifications screen with AppBar
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: const Text('Notifications'),
            backgroundColor: Constants.primaryColor,
            foregroundColor: Colors.white,
            elevation: 0,
          ),
          body: NotificationsScreen(userRole: user.role),
        ),
      ),
    );
  }

  // Clear recent notifications (useful for logout)
  void clearRecentNotifications() {
    _recentNotifications.clear();
  }

  // Get recent notifications
  List<NotificationModel> get recentNotifications => List.unmodifiable(_recentNotifications);

  void dismissPopup(String notificationId) {
    // Remove all active overlays
    for (final entry in _activeOverlays) {
      entry.remove();
    }
    _activeOverlays.clear();
    
    // Mark the notification as read to prevent re-showing
    _markNotificationAsReadById(int.parse(notificationId));
  }
  
  // Add this helper method
  Future<void> _markNotificationAsReadById(int notificationId) async {
    try {
      final user = await AuthService.getUser();
      if (user == null) return;
  
      if (user.role == 'lister') {
        await NotificationService().markNotificationAsRead(notificationId: notificationId);
      } else {
        await NotificationService().markDoerNotificationAsRead(notificationId: notificationId);
      }
    } catch (e) {
      print('NotificationPopupService: Error marking notification as read: $e');
    }
  }
}