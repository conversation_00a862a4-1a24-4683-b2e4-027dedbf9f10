import 'package:flutter/material.dart';
import 'dart:async';
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/utils/auth_service.dart';

class PasswordOtpDialog extends StatefulWidget {
  final String userId;
  final Function(bool) onOtpVerified;

  const PasswordOtpDialog({
    super.key,
    required this.userId,
    required this.onOtpVerified,
  });

  @override
  State<PasswordOtpDialog> createState() => _PasswordOtpDialogState();
}

class _PasswordOtpDialogState extends State<PasswordOtpDialog> {
  final TextEditingController _otpController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  final AuthService _authService = AuthService();
  bool _isLoading = false;
  int _resendTimerSeconds = 60;
  Timer? _timer;
  bool _canResend = false;

  @override
  void initState() {
    super.initState();
    _startResendTimer();
    _sendInitialOtp();
  }

  @override
  void dispose() {
    _otpController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  void _startResendTimer() {
    _resendTimerSeconds = 60;
    _canResend = false;
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_resendTimerSeconds == 0) {
        setState(() {
          _canResend = true;
          timer.cancel();
        });
      } else {
        setState(() {
          _resendTimerSeconds--;
        });
      }
    });
  }

  Future<void> _sendInitialOtp() async {
    setState(() { _isLoading = true; });
    
    final response = await _authService.requestPasswordOtp(userId: widget.userId);
    
    setState(() { _isLoading = false; });
    
    if (response['success']) {
      _showSnackBar('OTP sent to your registered email');
    } else {
      _showSnackBar('Failed to send OTP: ${response['message']}', isError: true);
    }
  }

  Future<void> _verifyOtp() async {
    if (_formKey.currentState!.validate()) {
      setState(() { _isLoading = true; });
      
      final response = await _authService.verifyPasswordOtp(
        userId: widget.userId,
        otp: _otpController.text.trim(),
      );
      
      setState(() { _isLoading = false; });
      
      if (response['success']) {
        // Remove this line: _showSnackBar('OTP verified successfully!');
        widget.onOtpVerified(true);
        Navigator.of(context).pop(true);
      } else {
        _showSnackBar('Invalid OTP: ${response['message']}', isError: true);
      }
    }
  }

  Future<void> _resendOtp() async {
    if (_canResend) {
      setState(() { _isLoading = true; _canResend = false; });
      
      final response = await _authService.requestPasswordOtp(userId: widget.userId);
      
      setState(() { _isLoading = false; });
      
      if (response['success']) {
        _showSnackBar('OTP resent successfully!');
        _startResendTimer();
      } else {
        _showSnackBar('Failed to resend OTP: ${response['message']}', isError: true);
        setState(() { _canResend = true; });
      }
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text(
        'Verify Your Identity',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Enter the OTP code sent to your registered email to proceed with password change.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 20),
            TextFormField(
              controller: _otpController,
              keyboardType: TextInputType.number,
              textAlign: TextAlign.center,
              maxLength: 6,
              decoration: InputDecoration(
                labelText: 'Enter OTP Code',
                hintText: 'XXXXXX',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                counterText: '',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter the OTP code';
                }
                if (value.length != 6 || !RegExp(r'^[0-9]+$').hasMatch(value)) {
                  return 'Please enter a valid 6-digit code';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextButton(
              onPressed: _canResend ? _resendOtp : null,
              child: Text(
                _canResend ? 'Resend OTP' : 'Resend in: $_resendTimerSeconds',
                style: TextStyle(
                  color: _canResend ? Constants.primaryColor : Colors.grey,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _verifyOtp,
          style: ElevatedButton.styleFrom(
            backgroundColor: Constants.primaryColor,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2),
                )
              : const Text('Verify'),
        ),
      ],
    );
  }
}