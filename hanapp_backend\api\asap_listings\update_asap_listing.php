<?php
// hanapp_backend/api/asap_listings/update_asap_listing.php
// Updates an existing ASAP listing with payment method and fee handling

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../../config/db_connect.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(["success" => false, "message" => "Only POST requests are allowed."]);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(["success" => false, "message" => "Invalid JSON input."]);
    exit();
}

// Validate required fields
$requiredFields = ['id', 'title', 'description', 'price'];
foreach ($requiredFields as $field) {
    if (!isset($input[$field]) || (is_string($input[$field]) && trim($input[$field]) === '')) {
        echo json_encode(["success" => false, "message" => "Missing required field: $field"]);
        exit();
    }
}

// Extract and validate input data
$listingId = intval($input['id']);
$title = trim($input['title']);
$description = trim($input['description']);
$price = floatval($input['price']);
$latitude = isset($input['latitude']) ? floatval($input['latitude']) : null;
$longitude = isset($input['longitude']) ? floatval($input['longitude']) : null;
$locationAddress = isset($input['locationAddress']) ? trim($input['locationAddress']) : null;
$preferredDoerGender = isset($input['preferredDoerGender']) ? trim($input['preferredDoerGender']) : 'Any';



// Handle pictures URLs - check both possible field names
$picturesUrls = isset($input['pictures_urls']) ? $input['pictures_urls'] :
                (isset($input['picturesUrls']) ? $input['picturesUrls'] : []);
$picturesUrlsJson = json_encode($picturesUrls);

error_log("ASAP listing update - Pictures URLs received: " . json_encode($picturesUrls));
error_log("ASAP listing update - Pictures URLs JSON: " . $picturesUrlsJson);

// Validate price
if ($price <= 0) {
    echo json_encode(["success" => false, "message" => "Price must be greater than zero."]);
    exit();
}

if ($price < 20) {
    echo json_encode(["success" => false, "message" => "Minimum price is ₱20.00."]);
    exit();
}



// Validate listing exists and get current data for comparison
$checkStmt = $conn->prepare("SELECT id, lister_id, title, description, price, latitude, longitude, location_address, preferred_doer_gender, pictures_urls FROM asap_listings WHERE id = ?");
if ($checkStmt === false) {
    error_log("Failed to prepare ASAP listing check statement: " . $conn->error);
    echo json_encode(["success" => false, "message" => "Internal server error."]);
    exit();
}

$checkStmt->bind_param("i", $listingId);
$checkStmt->execute();
$result = $checkStmt->get_result();

if ($result->num_rows === 0) {
    $checkStmt->close();
    echo json_encode(["success" => false, "message" => "ASAP listing not found."]);
    exit();
}

$existingListing = $result->fetch_assoc();
$checkStmt->close();

// Check if any changes were made
$hasChanges = false;
$changes = [];

if ($existingListing['title'] !== $title) {
    $hasChanges = true;
    $changes[] = 'title';
    error_log("ASAP listing update - Title changed: '{$existingListing['title']}' -> '$title'");
}
if ($existingListing['description'] !== $description) {
    $hasChanges = true;
    $changes[] = 'description';
    error_log("ASAP listing update - Description changed");
}
if (floatval($existingListing['price']) !== $price) {
    $hasChanges = true;
    $changes[] = 'price';
    error_log("ASAP listing update - Price changed: {$existingListing['price']} -> $price");
}
if (floatval($existingListing['latitude']) !== $latitude) {
    $hasChanges = true;
    $changes[] = 'latitude';
}
if (floatval($existingListing['longitude']) !== $longitude) {
    $hasChanges = true;
    $changes[] = 'longitude';
}
if ($existingListing['location_address'] !== $locationAddress) {
    $hasChanges = true;
    $changes[] = 'location_address';
}
if ($existingListing['preferred_doer_gender'] !== $preferredDoerGender) {
    $hasChanges = true;
    $changes[] = 'preferred_doer_gender';
}

// Special handling for pictures_urls comparison
$existingPicturesJson = $existingListing['pictures_urls'];
$existingPicturesArray = json_decode($existingPicturesJson, true) ?? [];
$newPicturesArray = $picturesUrls;

error_log("ASAP listing update - Raw existing pictures: " . $existingPicturesJson);
error_log("ASAP listing update - Parsed existing pictures: " . json_encode($existingPicturesArray));
error_log("ASAP listing update - Raw new pictures: " . json_encode($newPicturesArray));

// Normalize arrays for comparison (remove any null/empty values and placeholders)
$existingPicturesFiltered = array_filter($existingPicturesArray, function($url) {
    return !empty($url) &&
           strpos($url, 'placehold.co') === false &&
           strpos($url, 'example.com') === false &&
           trim($url) !== '';
});
$newPicturesFiltered = array_filter($newPicturesArray, function($url) {
    return !empty($url) &&
           strpos($url, 'placehold.co') === false &&
           strpos($url, 'example.com') === false &&
           trim($url) !== '';
});

// Reset array keys after filtering
$existingPicturesFiltered = array_values($existingPicturesFiltered);
$newPicturesFiltered = array_values($newPicturesFiltered);

// Sort arrays for consistent comparison
sort($existingPicturesFiltered);
sort($newPicturesFiltered);

error_log("ASAP listing update - Filtered existing pictures: " . json_encode($existingPicturesFiltered));
error_log("ASAP listing update - Filtered new pictures: " . json_encode($newPicturesFiltered));

// Compare the filtered arrays
if ($existingPicturesFiltered !== $newPicturesFiltered) {
    $hasChanges = true;
    $changes[] = 'pictures_urls';
    error_log("ASAP listing update - Pictures changed!");
    error_log("ASAP listing update - Count changed from " . count($existingPicturesFiltered) . " to " . count($newPicturesFiltered));
} else {
    error_log("ASAP listing update - Pictures unchanged (arrays are identical)");
}

// Always update pictures_urls with the new array (including new uploads)
// This ensures new images are saved even if other fields don't change
if (!empty($newPicturesArray) && $newPicturesArray !== $existingPicturesArray) {
    $hasChanges = true;
    if (!in_array('pictures_urls', $changes)) {
        $changes[] = 'pictures_urls';
    }
    error_log("ASAP listing update - Force pictures update due to new images");
}



error_log("ASAP listing update - Changes detected: " . ($hasChanges ? implode(', ', $changes) : 'none'));
error_log("ASAP listing update - Raw input pictures_urls: " . json_encode($input['pictures_urls'] ?? 'NOT SET'));
error_log("ASAP listing update - Processed pictures JSON: " . $picturesUrlsJson);

// Only update if there are actual changes
if ($hasChanges) {
    error_log("ASAP listing update - Executing database update for changes: " . implode(', ', $changes));

    // Update listing in the 'asap_listings' table
    $stmt = $conn->prepare("
        UPDATE asap_listings SET
            title = ?,
            description = ?,
            price = ?,
            latitude = ?,
            longitude = ?,
            location_address = ?,
            preferred_doer_gender = ?,
            pictures_urls = ?,
            updated_at = NOW()
        WHERE id = ?
    ");

    if ($stmt === false) {
        error_log("Failed to prepare ASAP listing update statement: " . $conn->error);
        echo json_encode(["success" => false, "message" => "Internal server error."]);
        exit();
    }

    $stmt->bind_param(
        "ssdddsssi",
        $title,
        $description,
        $price,
        $latitude,
        $longitude,
        $locationAddress,
        $preferredDoerGender,
        $picturesUrlsJson,
        $listingId
    );

    if ($stmt->execute()) {
        $stmt->close();
        echo json_encode([
            "success" => true,
            "message" => "ASAP listing updated successfully!",
            "listing_id" => $listingId,
            "changes_detected" => $changes,
            "updated_pictures" => $newPicturesArray,
            "pictures_count" => count($newPicturesArray)
        ]);
    } else {
        error_log("Error executing ASAP listing update statement: " . $stmt->error);
        echo json_encode(["success" => false, "message" => "Failed to update ASAP listing. Please try again."]);
    }
} else {
    error_log("ASAP listing update - No changes detected, skipping database update");
    echo json_encode([
        "success" => true,
        "message" => "No changes were made to the ASAP listing.",
        "listing_id" => $listingId,
        "changes_detected" => [],
        "existing_pictures" => $existingPicturesFiltered,
        "submitted_pictures" => $newPicturesFiltered,
        "raw_existing" => $existingPicturesArray,
        "raw_submitted" => $newPicturesArray
    ]);
}

$conn->close();
?>