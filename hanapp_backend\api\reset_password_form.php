<?php
// reset_password_form.php
// Handles GET requests from email links and provides a password reset form

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'config/db_connect.php';

header('Content-Type: text/html');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

function generateResponsePage($title, $message, $type = 'info', $showForm = false, $token = '') {
    $alertClass = '';
    $iconClass = '';
    
    switch ($type) {
        case 'success':
            $alertClass = 'alert-success';
            $iconClass = 'fa-check-circle';
            break;
        case 'error':
            $alertClass = 'alert-danger';
            $iconClass = 'fa-exclamation-triangle';
            break;
        case 'warning':
            $alertClass = 'alert-warning';
            $iconClass = 'fa-exclamation-circle';
            break;
        default:
            $alertClass = 'alert-info';
            $iconClass = 'fa-info-circle';
    }
    
    $formHtml = '';
    if ($showForm) {
        $formHtml = '
        <div class="card mt-4">
            <div class="card-body">
                <h5 class="card-title">Reset Your Password</h5>
                <form id="resetForm" onsubmit="resetPassword(event)">
                    <input type="hidden" id="token" value="' . htmlspecialchars($token) . '">
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="newPassword" required minlength="6">
                        <div class="form-text">Password must be at least 6 characters long.</div>
                    </div>
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">Confirm Password</label>
                        <input type="password" class="form-control" id="confirmPassword" required>
                    </div>
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <span id="submitText">Reset Password</span>
                        <span id="submitSpinner" class="spinner-border spinner-border-sm d-none" role="status"></span>
                    </button>
                </form>
            </div>
        </div>
        
        <script>
        async function resetPassword(event) {
            event.preventDefault();
            
            const newPassword = document.getElementById("newPassword").value;
            const confirmPassword = document.getElementById("confirmPassword").value;
            const token = document.getElementById("token").value;
            const submitBtn = document.getElementById("submitBtn");
            const submitText = document.getElementById("submitText");
            const submitSpinner = document.getElementById("submitSpinner");
            
            if (newPassword !== confirmPassword) {
                alert("Passwords do not match!");
                return;
            }
            
            // Show loading state
            submitBtn.disabled = true;
            submitText.classList.add("d-none");
            submitSpinner.classList.remove("d-none");
            
            try {
                const response = await fetch("reset_password_token.php", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify({
                        token: token,
                        new_password: newPassword
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.body.innerHTML = `
                        <div class="container mt-5">
                            <div class="row justify-content-center">
                                <div class="col-md-6">
                                    <div class="alert alert-success text-center">
                                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                                        <h4>Password Reset Successful!</h4>
                                        <p>Your password has been successfully updated. You can now log in with your new password.</p>
                                        <a href="#" class="btn btn-primary">Return to App</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    alert("Error: " + result.message);
                }
            } catch (error) {
                alert("An error occurred. Please try again.");
            } finally {
                // Reset loading state
                submitBtn.disabled = false;
                submitText.classList.remove("d-none");
                submitSpinner.classList.add("d-none");
            }
        }
        </script>
        ';
    }
    
    return '
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>' . htmlspecialchars($title) . ' - TAPP</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            }
            .card {
                border: none;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            }
            .alert {
                border-radius: 15px;
                border: none;
            }
            .btn {
                border-radius: 25px;
                padding: 10px 30px;
            }
        </style>
    </head>
    <body>
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="text-center mb-4">
                        <h2 class="text-white"><i class="fas fa-shield-alt"></i> TAPP Security</h2>
                    </div>
                    <div class="alert ' . $alertClass . ' text-center">
                        <i class="fas ' . $iconClass . ' fa-3x mb-3"></i>
                        <h4>' . htmlspecialchars($title) . '</h4>
                        <p>' . htmlspecialchars($message) . '</p>
                    </div>
                    ' . $formHtml . '
                </div>
            </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    ';
}

try {
    $token = $_GET['token'] ?? null;

    if (empty($token)) {
        echo generateResponsePage(
            "Invalid Request", 
            "No reset token provided. Please use the link from your email.",
            "error"
        );
        exit();
    }

    // Validate the token
    $stmt = $conn->prepare("SELECT user_id, expires_at, used FROM password_reset_codes_email WHERE token = ?");
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo generateResponsePage(
            "Invalid Token", 
            "The reset token is invalid or has been used. Please request a new password reset.",
            "error"
        );
        exit();
    }
    
    $tokenData = $result->fetch_assoc();
    $stmt->close();
    
    // Check if token is expired
    $expiresAt = new DateTime($tokenData['expires_at']);
    $now = new DateTime();
    
    if ($now > $expiresAt) {
        echo generateResponsePage(
            "Token Expired", 
            "The reset token has expired. Please request a new password reset.",
            "error"
        );
        exit();
    }
    
    // Check if token has been used
    if ($tokenData['used'] == 1) {
        echo generateResponsePage(
            "Token Already Used", 
            "This reset token has already been used. Please request a new password reset if needed.",
            "error"
        );
        exit();
    }
    
    // Token is valid, show the password reset form
    echo generateResponsePage(
        "Reset Your Password", 
        "Please enter your new password below. Make sure it's secure and at least 6 characters long.",
        "info",
        true,
        $token
    );
    
} catch (Exception $e) {
    error_log("Password reset form error: " . $e->getMessage());
    echo generateResponsePage(
        "Error", 
        "An error occurred while processing your request. Please try again later or contact support.",
        "error"
    );
}

$conn->close();
?>