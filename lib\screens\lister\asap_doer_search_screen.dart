import 'package:flutter/material.dart';
import 'dart:async';
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/models/user.dart';
import 'package:hanapp/models/asap_listing.dart';
import 'package:hanapp/services/asap_service.dart';
import 'package:hanapp/utils/asap_listing_service.dart';
import 'package:hanapp/utils/auth_service.dart';
import 'package:hanapp/utils/image_utils.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:math' as math;

class AsapDoerSearchScreen extends StatefulWidget {
  final int? listingId; // Made optional for new flow
  final Map<String, dynamic>? listingData; // New field for form data
  final double listingLatitude;
  final double listingLongitude;
  final String preferredDoerGender;
  final double maxDistance;

  const AsapDoerSearchScreen({
    super.key,
    this.listingId, // Made optional
    this.listingData, // New parameter
    required this.listingLatitude,
    required this.listingLongitude,
    required this.preferredDoerGender,
    required this.maxDistance,
  });

  @override
  State<AsapDoerSearchScreen> createState() => _AsapDoerSearchScreenState();
}

class _AsapDoerSearchScreenState extends State<AsapDoerSearchScreen> {
  final AsapService _asapService = AsapService();
  final AsapListingService _asapListingService = AsapListingService();
  List<User> _availableDoers = [];
  AsapListing? _listing;
  bool _isLoading = true;
  // Combined search state variables
  bool _isSearching = true;
  String _searchPhase = 'waiting'; // 'waiting', 'searching', 'completed'
  int _totalSearchSeconds = 33; // 30 seconds waiting + 3 seconds searching
  int _remainingSeconds = 33;
  Timer? _searchTimer;
  Timer? _countdownTimer;
  String? _errorMessage;
  int? _createdListingId; // Store the created listing ID

  // Map related variables
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {};
  LatLng? _listerLocation;
  LatLng? _doerLocation;

  // Filter variables
  double _maxDistance = 100.0; // Changed from 1.0 to 100.0 for testing
  String _preferredDoerGender = 'Any';
  
  // Variable for single doer cycling
  int _currentDoerIndex = 0;

  // NEW: GPS location variables
  double? _currentLatitude;
  double? _currentLongitude;
  bool _isGettingLocation = false;

  @override
  void initState() {
    super.initState();
    // Initialize filter variables with widget values, but default to 100km if not specified
    _maxDistance = widget.maxDistance > 0 ? widget.maxDistance : 100.0;
    _preferredDoerGender = widget.preferredDoerGender;

    // NEW: Get current GPS location first
    _getCurrentLocationAndInitialize();
  }

  // NEW: Get current GPS location before initializing
  Future<void> _getCurrentLocationAndInitialize() async {
    await _getCurrentLocation();
    _initializeMap();
    _fetchListingDetails();
    
    // Add debug print
    print('Doer Search: initState - widget.listingId: ${widget.listingId}');
    print('Doer Search: initState - widget.listingData: ${widget.listingData != null}');
    
    // Start timer immediately if we already have a listing ID
    if (widget.listingId != null) {
      print('Doer Search: Starting timer immediately with existing listing ID');
      _startUnifiedSearch();
    } else {
      // Create listing first, then start search
      print('Doer Search: Creating listing before starting timer');
      _createListingAndStartSearch();
    }
  }



  // NEW: Get current GPS location
  Future<void> _getCurrentLocation() async {
    try {
      setState(() {
        _isGettingLocation = true;
      });

      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw Exception('Location services are disabled');
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Location permissions are permanently denied');
      }

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      setState(() {
        _currentLatitude = position.latitude;
        _currentLongitude = position.longitude;
        _isGettingLocation = false;
      });

      print('Doer Search: Current GPS location obtained: $_currentLatitude, $_currentLongitude');
    } catch (e) {
      print('Doer Search: Error getting current location: $e');
      // Fallback to widget coordinates if GPS fails
      setState(() {
        _currentLatitude = widget.listingLatitude;
        _currentLongitude = widget.listingLongitude;
        _isGettingLocation = false;
      });
    }
  }

  Future<void> _createListingAndStartSearch() async {
    print('Doer Search: _createListingAndStartSearch called');
    
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
      
      final success = await _createListingIfNeeded();
      
      if (success) {
        print('Doer Search: Listing creation successful, starting search');
        setState(() {
          _isLoading = false;
        });
        _startUnifiedSearch();
      } else {
        print('Doer Search: Listing creation failed');
        setState(() {
          _errorMessage = 'Failed to create listing';
          _isLoading = false;
          _isSearching = false;
        });
      }
    } catch (error) {
      print('Doer Search: Exception during listing creation: $error');
      setState(() {
        _errorMessage = 'Error creating listing: $error';
        _isLoading = false;
        _isSearching = false;
      });
    }
  }

  @override
  void dispose() {
    _searchTimer?.cancel();
    _countdownTimer?.cancel();
    _mapController?.dispose();
    super.dispose();
  }

  void _initializeMap() {
    // Use current GPS location if available, otherwise fallback to widget coordinates
    double lat = _currentLatitude ?? widget.listingLatitude;
    double lng = _currentLongitude ?? widget.listingLongitude;
    _listerLocation = LatLng(lat, lng);
    _updateMarkers();
  }

  void _updateMarkers() {
    Set<Marker> markers = {};

    // Add lister marker
    if (_listerLocation != null) {
      markers.add(Marker(
        markerId: const MarkerId('lister_location'),
        position: _listerLocation!,
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
        infoWindow: const InfoWindow(
          title: 'Lister Location',
          snippet: 'Your location',
        ),
      ));
    }

    // Add doer marker if available
    if (_doerLocation != null && _availableDoers.isNotEmpty) {
      markers.add(Marker(
        markerId: const MarkerId('doer_location'),
        position: _doerLocation!,
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
        infoWindow: InfoWindow(
          title: 'Doer Location',
          snippet: '${_availableDoers.first.fullName}',
        ),
      ));
    }

    setState(() {
      _markers = markers;
    });
  }

  void _updateRoute() {
    if (_listerLocation != null && _doerLocation != null) {
      // Create a simple straight line route (in a real app, you'd use Google Directions API)
      Set<Polyline> polylines = {};
      polylines.add(Polyline(
        polylineId: const PolylineId('route'),
        points: [_doerLocation!, _listerLocation!],
        color: Colors.blue,
        width: 3,
      ));

      setState(() {
        _polylines = polylines;
      });

      // Fit bounds to show both markers
      _fitBounds();
    }
  }

  void _fitBounds() {
    if (_mapController != null && _listerLocation != null && _doerLocation != null) {
      double minLat = _listerLocation!.latitude < _doerLocation!.latitude
          ? _listerLocation!.latitude
          : _doerLocation!.latitude;
      double maxLat = _listerLocation!.latitude > _doerLocation!.latitude
          ? _listerLocation!.latitude
          : _doerLocation!.latitude;
      double minLng = _listerLocation!.longitude < _doerLocation!.longitude
          ? _listerLocation!.longitude
          : _doerLocation!.longitude;
      double maxLng = _listerLocation!.longitude > _doerLocation!.longitude
          ? _listerLocation!.longitude
          : _doerLocation!.longitude;

      _mapController!.animateCamera(CameraUpdate.newLatLngBounds(
        LatLngBounds(
          southwest: LatLng(minLat - 0.01, minLng - 0.01),
          northeast: LatLng(maxLat + 0.01, maxLng + 0.01),
        ),
        50.0,
      ));
    }
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    if (_listerLocation != null) {
      controller.animateCamera(CameraUpdate.newLatLngZoom(_listerLocation!, 15.0));
    }
  }

  Future<bool> _createListingIfNeeded() async {
    try {
      if (widget.listingData != null && widget.listingId == null && _createdListingId == null) {
        print('Doer Search: Creating listing for search...');
        final listingData = widget.listingData!;

        final createResponse = await _asapListingService.createAsapListing(
          title: listingData['title'],
          description: listingData['description'],
          price: listingData['price'],
          latitude: listingData['latitude'],
          longitude: listingData['longitude'],
          locationAddress: listingData['location_address'],
          preferredDoerGender: listingData['preferred_doer_gender'],
          picturesUrls: List<String>.from(listingData['pictures_urls'] ?? []),
          // paymentMethod: listingData['payment_method'],
          // cardType: listingData['card_type'],
          isOpenPrice: listingData['is_open_price'] ?? false, // NEW: Add this line
        );

        if (createResponse['success']) {
          _createdListingId = createResponse['listing_id'];
          print('Doer Search: Listing created successfully with ID: $_createdListingId');
          return true;
        } else {
          print('Doer Search: Failed to create listing: ${createResponse['message']}');
          return false;
        }
      }
      return true; // No creation needed
    } catch (e) {
      print('Doer Search: Exception in _createListingIfNeeded: $e');
      return false;
    }
  }

  Future<void> _fetchListingDetails() async {
    try {
      // Check if we have a created listing ID or widget listing ID
      final listingId = _createdListingId ?? widget.listingId;

      if (listingId != null) {
        final response = await _asapListingService.getAsapListingDetails(listingId);
        if (response['success'] && mounted) {
          setState(() {
            _listing = response['listing'];
          });
        }
      } else if (widget.listingData != null) {
        // If we have listing_data, create a temporary listing object for display
        final data = widget.listingData!;
        setState(() {
          final isOpenPrice = data['is_open_price'] ?? false;
          print('🔍 DEBUG: Creating temporary listing with isOpenPrice: $isOpenPrice');
          print('🔍 DEBUG: Raw data[is_open_price]: ${data['is_open_price']}');

          _listing = AsapListing(
            id: 0, // Temporary ID
            listerId: 0, // Will be set when created
            title: data['title'],
            description: data['description'],
            price: data['price'],
            latitude: data['latitude'],
            longitude: data['longitude'],
            locationAddress: data['location_address'],
            preferredDoerGender: data['preferred_doer_gender'],
            picturesUrls: List<String>.from(data['pictures_urls'] ?? []),
            paymentMethod: data['payment_method'],
            status: 'pending',
            isActive: true,
            isOpenPrice: isOpenPrice, // Add isOpenPrice field
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          print('🔍 DEBUG: Created listing with isOpenPrice: ${_listing!.isOpenPrice}');
        });
      }
    } catch (e) {
      print('Error fetching listing details: $e');
    }
  }

  void _startUnifiedSearch() {
    // Cancel any existing timers first
    _searchTimer?.cancel();
    _countdownTimer?.cancel();
    
    print('Doer Search: Starting unified search timer');
    print('Doer Search: Initial _remainingSeconds: $_remainingSeconds');
    print('Doer Search: _totalSearchSeconds: $_totalSearchSeconds');
    
    setState(() {
        _isSearching = true;
        _searchPhase = 'waiting';
        _remainingSeconds = _totalSearchSeconds;
    });
    
    // Start countdown timer
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        print('Doer Search: Timer tick - _remainingSeconds: $_remainingSeconds, mounted: $mounted');
        
        if (mounted) {
            if (_remainingSeconds > 0) {
                setState(() {
                    _remainingSeconds--;
                    print('Doer Search: Decremented _remainingSeconds to: $_remainingSeconds');
                    
                    // Update search phase based on remaining time
                    if (_remainingSeconds > 3) {
                        _searchPhase = 'waiting';
                    } else if (_remainingSeconds > 0) {
                        _searchPhase = 'searching';
                    } else {
                        _searchPhase = 'completed';
                    }
                });
            } else {
                print('Doer Search: Timer completed, cancelling and calling _performDoerSearch');
                timer.cancel();
                _countdownTimer = null;
                // Handle completion - only call _performDoerSearch once
                if (mounted && _isSearching) {
                    _performDoerSearch();
                }
            }
        } else {
            print('Doer Search: Widget not mounted, cancelling timer');
            timer.cancel();
            _countdownTimer = null;
        }
    });
  }

  Future<void> _performDoerSearch() async {
    print('Doer Search: _performDoerSearch called, _isSearching: $_isSearching');
    
    // Prevent multiple calls
    if (!_isSearching) {
      print('Doer Search: Already not searching, returning');
      return;
    }
    
    // Simulate searching time (3 seconds)
    await Future.delayed(const Duration(seconds: 3));

    if (mounted && _isSearching) {
      print('Doer Search: Setting search complete and calling _searchDoers');
      setState(() {
        _isSearching = false;
        _searchPhase = 'completed';
      });
      _searchDoers();
    }
  }

  // Method to refresh search results (called by rescan button)
  Future<void> _searchForDoers() async {
    // Cancel existing timers first
    _searchTimer?.cancel();
    _countdownTimer?.cancel();
    
    setState(() {
      _errorMessage = null;
      _availableDoers.clear(); // Clear existing doers for fresh search
      _currentDoerIndex = 0; // Reset index
    });
    
    // Call search immediately to send notifications right away
    await _searchDoers();
    
    // Then start the unified search with waiting phase for doers to accept
    _startUnifiedSearch();
  }

  Future<void> _searchDoers() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      if (!mounted) return;

      // Use current GPS coordinates if available, otherwise fallback to widget coordinates
      double searchLatitude = _currentLatitude ?? widget.listingLatitude;
      double searchLongitude = _currentLongitude ?? widget.listingLongitude;

      print('Doer Search: Starting search for nearest and available doers...');
      print('  listingId: ${widget.listingId}');
      print('  searchLatitude (GPS): $searchLatitude');
      print('  searchLongitude (GPS): $searchLongitude');
      print('  preferredDoerGender: $_preferredDoerGender');
      print('  maxDistance: ${_maxDistance}km');

      // Use pre-created listing ID
      int searchListingId = _createdListingId ?? widget.listingId ?? 0;

      if (searchListingId == 0) {
        setState(() {
          _errorMessage = 'No listing ID available for search';
          _isLoading = false;
        });
        return;
      }

      print('Doer Search: Using listing ID: $searchListingId');

      final response = await _asapService.searchDoers(
        listingId: searchListingId,
        listerLatitude: searchLatitude,  // CHANGED: Use GPS coordinates
        listerLongitude: searchLongitude, // CHANGED: Use GPS coordinates
        preferredDoerGender: _preferredDoerGender,
        maxDistance: _maxDistance,
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
          if (response['success']) {
            print('Doer Search: Backend response success, parsing doers...');
            print('Doer Search: Raw doers data: ${response['doers']}');

            _availableDoers = (response['doers'] as List)
                .map((doerData) {
              print('Doer Search: Parsing doer data: $doerData');
              return User.fromJson(doerData);
            })
                .toList();
            print('Doer Search: Successfully parsed ${_availableDoers.length} doers');

            // Update map with doer location if found
            if (_availableDoers.isNotEmpty) {
              _updateMapWithDoer();
            }
          } else {
            _errorMessage = response['message'] ?? 'Failed to search for doers';
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Error searching for doers: $e';
        });
      }
    }
  }

  void _updateMapWithDoer() {
    if (_availableDoers.isNotEmpty && _currentDoerIndex < _availableDoers.length) {
      // Use the current doer's location
      final doer = _availableDoers[_currentDoerIndex];
      if (doer.latitude != null && doer.longitude != null) {
        _doerLocation = LatLng(doer.latitude!, doer.longitude!);
        _updateMarkers();
        _updateRoute();
      }
    }
  }

  void _selectDoer(User doer) {
    print('Doer Selection: Showing selection dialog for doer: ${doer.fullName}');
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Doer'),
        content: Text('Are you sure you want to select ${doer.fullName} for this task?'),
        actions: [
          TextButton(
            onPressed: () {
              print('Doer Selection: User cancelled selection');
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              print('Doer Selection: User confirmed selection');
              Navigator.of(context).pop();
              _confirmDoerSelection(doer);
            },
            child: const Text('Select'),
          ),
        ],
      ),
    );
  }

  Future<void> _confirmDoerSelection(User doer) async {
    print('Doer Selection: Starting doer selection process...');
    print('Doer Selection: Selected doer ID: ${doer.id}');
    print('Doer Selection: Selected doer name: ${doer.fullName}');
    print('Doer Selection: Listing ID: ${widget.listingId}');
    print('Doer Selection: Has listing data: ${widget.listingData != null}');

    try {
      final currentUser = await AuthService.getUser();
      if (currentUser == null) {
        print('Doer Selection: ERROR - Current user is null');
        return;
      }

      print('Doer Selection: Current user ID: ${currentUser.id}');
      print('Doer Selection: Current user name: ${currentUser.fullName}');

      // Use the created listing ID if available, otherwise use widget's listing ID
      int listingId = _createdListingId ?? widget.listingId ?? 0;

      if (listingId == 0) {
        print('Doer Selection: ERROR - No listing ID available');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Error: No listing available for selection')),
          );
        }
        return;
      }

      print('Doer Selection: Calling selectDoer API...');
      final response = await _asapService.selectDoer(
        listingId: listingId,
        doerId: doer.id!,
        listerId: currentUser.id!,
      );

      print('Doer Selection: API response received:');
      print('Doer Selection: Response success: ${response['success']}');
      print('Doer Selection: Response message: ${response['message']}');
      print('Doer Selection: Full response: $response');

      if (response['success']) {
        print('Doer Selection: SUCCESS - Doer selected successfully');
        print('Doer Selection: Application ID: ${response['application_id']}');
        print('Doer Selection: Conversation ID: ${response['conversation_id']}');

        if (mounted) {
          print('Doer Selection: Navigating to chat screen...');
          Navigator.of(context).pushReplacementNamed(
            '/chat_screen',
            arguments: {
              'conversationId': response['conversation_id'],
              'otherUserId': doer.id!, // The doer's ID
              'listingTitle': response['listing']?['title'] ?? 'ASAP Task',
              'applicationId': response['application_id'],
              'isLister': true, // Current user is the lister
            },
          );
          print('Doer Selection: Navigation to chat completed');
        }
      } else {
        print('Doer Selection: ERROR - Failed to select doer');
        print('Doer Selection: Error message: ${response['message']}');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(response['message'] ?? 'Failed to select doer')),
          );
        }
      }
    } catch (e) {
      print('Doer Selection: EXCEPTION - Error selecting doer: $e');
      print('Doer Selection: Exception type: ${e.runtimeType}');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error selecting doer: $e')),
        );
      }
    }
  }

  void _convertToPublic() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Convert to Public Listing'),
        content: const Text(
          'No doers are accepting your ASAP offer. Would you like to make it publicly available?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('No, Exit'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _confirmConvertToPublic();
            },
            child: const Text('Yes, Make Public'),
          ),
        ],
      ),
    );
  }

  Future<void> _confirmConvertToPublic() async {
    try {
      final currentUser = await AuthService.getUser();
      if (currentUser == null) return;

      final listingId = _createdListingId ?? widget.listingId ?? 0;
      final response = await _asapService.convertToPublic(
        listingId: listingId,
        listerId: currentUser.id!,
      );

      if (response['success']) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(response['message'] ?? 'Converted to public listing')),
          );
          // Navigate to Jobs Listing page and clear all previous routes to prevent going back to Create ASAP Listing
          Navigator.of(context).pushNamedAndRemoveUntil(
            '/lister_dashboard',
            (route) => false, // Remove all previous routes from the stack
            arguments: {'selectedIndex': 1}, // Index 1 is the Jobs Listing tab
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(response['message'] ?? 'Failed to convert to public')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error converting to public: $e')),
        );
      }
    }
  }

  // Working map widget
  Widget _buildMapWidget() {
    if (_listerLocation == null) {
      return Container(
        height: 180,
        margin: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Colors.grey.shade200,
        ),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Container(
      height: 180,
      margin: const EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: GoogleMap(
          onMapCreated: _onMapCreated,
          initialCameraPosition: CameraPosition(
            target: _listerLocation!,
            zoom: 15.0,
          ),
          markers: _markers,
          polylines: _polylines,
          myLocationButtonEnabled: false,
          zoomControlsEnabled: false,
          mapToolbarEnabled: false,
        ),
      ),
    );
  }

  // Add a placeholder for the listing details (in a real app, fetch the details)
  Widget _buildListingHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Working map widget
        _buildMapWidget(),
        // Listing details (using actual data)
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                  _listing?.title ?? 'ASAP Listing',
                  style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold)
              ),
              const SizedBox(height: 4),
              if (_listing?.description != null && _listing!.description!.isNotEmpty) ...[
                Text(
                    'Description: ${_listing!.description}',
                    style: const TextStyle(fontSize: 13, color: Colors.grey)
                ),
                const SizedBox(height: 2),
              ],
              if (_listing?.locationAddress != null) ...[
                Text(
                    'Address: ${_listing!.locationAddress}',
                    style: const TextStyle(fontSize: 13, color: Colors.grey)
                ),
                const SizedBox(height: 2),
              ],
              if (_listing?.price != null) ...[
                Text(
                    'Price: Php ${_listing!.price.toStringAsFixed(2)}',
                    style: const TextStyle(fontSize: 13, color: Colors.grey)
                ),
                const SizedBox(height: 2),
              ],
              if (_listing?.preferredDoerGender != null && _listing!.preferredDoerGender != 'Any') ...[
                Text(
                    'Preferred: ${_listing!.preferredDoerGender}',
                    style: const TextStyle(fontSize: 13, color: Colors.grey)
                ),
              ],
            ],
          ),
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  // Carousel reminders widget
  Widget _buildRemindersCarousel() {
    final reminders = [
      {
        'icon': Icons.notifications_active,
        'title': 'Stay Alert',
        'description': 'Keep your phone nearby for doer responses'
      },
      {
        'icon': Icons.location_on,
        'title': 'Location Accuracy',
        'description': 'Ensure your location is precise for better matches'
      },
      {
        'icon': Icons.payment,
        'title': 'Payment Ready',
        'description': 'Have your payment method ready for quick transactions'
      },
    ];

    return SizedBox(
      height: 120, // Reduced from 140
      child: PageView.builder(
        itemCount: reminders.length,
        itemBuilder: (context, index) {
          final reminder = reminders[index];
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 8),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Constants.primaryColor.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Constants.primaryColor.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min, // Add this
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  reminder['icon'] as IconData,
                  color: Constants.primaryColor,
                  size: 28, // Reduced from 32
                ),
                const SizedBox(height: 8),
                Flexible( // Add Flexible wrapper
                  child: Text(
                    reminder['title'] as String,
                    style: const TextStyle(
                      fontSize: 14, // Reduced from 16
                      fontWeight: FontWeight.bold,
                      color: Constants.textColor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 4),
                Flexible( // Add Flexible wrapper
                  child: Text(
                    reminder['description'] as String,
                    style: TextStyle(
                      fontSize: 11, // Reduced from 12
                      color: Colors.grey[600],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // List of available doers
  Widget _buildDoersListView() {
    if (_availableDoers.isEmpty) {
      return _buildNoDoersView();
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Icon(Icons.people, color: Constants.primaryColor, size: 24),
              const SizedBox(width: 8),
              Text(
                'Doers Found: ${_availableDoers.length}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Range: ${_maxDistance.toInt()}km',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            itemCount: _availableDoers.length + 1, // +1 for rescan button
            itemBuilder: (context, index) {
              if (index == _availableDoers.length) {
                // Rescan button at the end
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _showRescanSliderDialog,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Constants.primaryColor,
                        foregroundColor: Colors.white,
                      ),
                      icon: const Icon(Icons.refresh),
                      label: const Text('Rescan for More Doers'),
                    ),
                  ),
                );
              }
              return Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: _buildDoerCard(_availableDoers[index]),
              );
            },
          ),
        ),
      ],
    );
  }

  // Individual doer card
  Widget _buildDoerCard(User doer) {
    final distance = _calculateDistance(doer.latitude ?? 0, doer.longitude ?? 0);
    
    // Check if this doer has a price offer (you'll need to add these fields to User model)
    final bool hasOffer = doer.offeredPrice != null;
    final bool isAccepted = doer.acceptanceStatus == 'accepted';

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundImage: doer.profilePictureUrl != null
                      ? CachedNetworkImageProvider(doer.profilePictureUrl!)
                      : null,
                  child: doer.profilePictureUrl == null
                      ? Text(
                    doer.fullName.isNotEmpty ? doer.fullName[0].toUpperCase() : '?',
                    style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  )
                      : null,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              doer.fullName,
                              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (doer.isVerified)
                            const Icon(Icons.verified, color: Colors.blue, size: 20),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(Icons.star, color: Colors.amber, size: 16),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              '${doer.averageRating?.toStringAsFixed(1) ?? '0.0'} (${doer.totalReviews ?? 0} reviews)',
                              style: const TextStyle(fontSize: 14, color: Colors.grey),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(Icons.location_on, color: Colors.grey, size: 16),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              '${distance.toStringAsFixed(1)}km away • ${doer.addressDetails?.split(',').first.trim() ?? 'Unknown location'}',
                              style: const TextStyle(fontSize: 14, color: Colors.grey),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            // Add price offer section
            if (hasOffer && _listing?.isOpenPrice == true) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.local_offer, color: Colors.orange.shade700, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'Offered: ₱${doer.offeredPrice!.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange.shade700,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade100,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'PRICE OFFER',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            // Status indicator
            const SizedBox(height: 12),
            Row(
              children: [
                Flexible(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: isAccepted ? Colors.green.shade100 : (hasOffer ? Colors.orange.shade100 : Colors.blue.shade100),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          isAccepted ? Icons.check_circle : (hasOffer ? Icons.local_offer : Icons.circle),
                          color: isAccepted ? Colors.green : (hasOffer ? Colors.orange : Colors.blue),
                          size: 8,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          isAccepted ? 'Accepted' : (hasOffer ? 'Price Offered' : 'Available Now'),
                          style: TextStyle(
                            fontSize: 12,
                            color: isAccepted ? Colors.green.shade700 : (hasOffer ? Colors.orange.shade700 : Colors.blue.shade700),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => _selectDoer(doer),
                style: ElevatedButton.styleFrom(
                  backgroundColor: hasOffer ? Colors.orange : Constants.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                ),
                child: Text(
                  hasOffer ? 'Accept Offer (₱${doer.offeredPrice!.toStringAsFixed(2)})' : 'Connect Now',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Doer found card (show only the first doer for now)
  Widget _buildDoerFoundCard(User doer) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircleAvatar(
              radius: 36,
              backgroundImage: ImageUtils.createProfileImageProvider(doer.profilePictureUrl),
              child: doer.profilePictureUrl == null
                  ? const Icon(Icons.person, size: 36)
                  : null,
            ),
            const SizedBox(height: 16),
            Text('Hi, this is ${doer.fullName}', style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Text('Your list was accepted', style: const TextStyle(fontSize: 15, color: Colors.grey)),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.location_on, color: Colors.red, size: 18),
                const SizedBox(width: 4),
                Text(doer.addressDetails ?? 'Unknown', style: const TextStyle(fontSize: 14, color: Colors.grey)),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ...List.generate(5, (i) => Icon(
                  i < (doer.averageRating ?? 0).round()
                      ? Icons.star
                      : Icons.star_border,
                  color: Colors.amber,
                  size: 18,
                )),
                const SizedBox(width: 6),
                Text('${(doer.averageRating ?? 0.0).toStringAsFixed(1)}/5.0', style: const TextStyle(fontSize: 14, color: Colors.grey)),
              ],
            ),
            const SizedBox(height: 8),
            // Show distance if available (this would need to be added to the User model or passed separately)
            if (doer.latitude != null && doer.longitude != null) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.directions_walk, color: Colors.blue, size: 18),
                  const SizedBox(width: 4),
                  Text(
                    '${_calculateDistance(doer.latitude!, doer.longitude!).toStringAsFixed(1)} km away',
                    style: const TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                ],
              ),
              const SizedBox(height: 8),
            ],
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => _selectDoer(doer),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Constants.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                ),
                child: const Text('Connect Now', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to calculate distance between two points
  double _calculateDistance(double lat1, double lon1) {
    if (_listerLocation == null) return 0.0;

    const double earthRadius = 6371; // Earth's radius in kilometers

    double lat1Rad = _listerLocation!.latitude * (math.pi / 180);
    double lat2Rad = lat1 * (math.pi / 180);
    double deltaLat = (lat1 - _listerLocation!.latitude) * (math.pi / 180);
    double deltaLon = (lon1 - _listerLocation!.longitude) * (math.pi / 180);

    double a = math.sin(deltaLat / 2) * math.sin(deltaLat / 2) +
        math.cos(lat1Rad) * math.cos(lat2Rad) * math.sin(deltaLon / 2) * math.sin(deltaLon / 2);
    double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));

    return earthRadius * c;
  }

  // Enhanced spinner with pulsing effect and rotating dots
  Widget _buildEnhancedSpinner() {
    return const _EnhancedSpinnerWidget();
  }

  // Typing effect text widget
  Widget _buildTypingText() {
    return _TypingTextWidget(
      text: 'Searching for a doer',
      style: const TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Constants.textColor,
      ),
    );
  }

  Widget _buildUnifiedSearchView() {
    String mainText;
    String subText;
    String timerText;
    
    switch (_searchPhase) {
      case 'waiting':
        mainText = 'Waiting for Doers to Accept';
        subText = 'Notifying nearby doers about your ASAP listing';
        timerText = 'Waiting: ${_remainingSeconds}s';
        break;
      case 'searching':
        mainText = 'Searching for Available Doers';
        subText = 'Finding the best doers in your area';
        timerText = 'Searching: ${_remainingSeconds}s';
        break;
      default:
        mainText = 'Search Complete';
        subText = 'Processing results...';
        timerText = '';
    }
    
    return SingleChildScrollView(
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Enhanced spinner
              _buildEnhancedSpinner(),
              const SizedBox(height: 32),
              
              // Main status text with typing effect
              _TypingTextWidget(
                text: mainText,
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Constants.primaryColor,
                ),
              ),
              const SizedBox(height: 16),
              
              // Sub text
              Text(
                subText,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(fontSize: 14, color: Colors.grey),
              ),
              const SizedBox(height: 24),
              
              // Timer display
              if (timerText.isNotEmpty) ...[
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Constants.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Constants.primaryColor.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _searchPhase == 'waiting' ? Icons.hourglass_empty : Icons.search,
                        color: Constants.primaryColor,
                        size: 16,
                      ),
                      const SizedBox(width: 6),
                      Flexible(
                        child: Text(
                          timerText,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Constants.primaryColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
              ],
              
              // Progress bar
              LinearProgressIndicator(
                value: _totalSearchSeconds > 0 
                  ? (_totalSearchSeconds - _remainingSeconds) / _totalSearchSeconds 
                  : 0.0,
                backgroundColor: Colors.grey.shade300,
                valueColor: AlwaysStoppedAnimation<Color>(Constants.primaryColor),
                minHeight: 4,
              ),
              const SizedBox(height: 16),
              
              // Progress text
              Text(
                _totalSearchSeconds > 0 
                  ? '${(((_totalSearchSeconds - _remainingSeconds) / _totalSearchSeconds) * 100).toInt()}% Complete'
                  : '0% Complete',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              
              const SizedBox(height: 32),
              _buildRemindersCarousel(),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildListingHeader(),
          Expanded(
            child: _isSearching
                ? _buildUnifiedSearchView() // Use unified search view
                : _errorMessage != null
                    ? _buildErrorView()
                    : (_availableDoers.isNotEmpty
                        ? _buildDoersListView()
                        : _buildNoDoersView()),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'Error',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.red.shade400,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16, color: Colors.grey),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _searchDoers,
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoDoersView() {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: SingleChildScrollView(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.people_outline,
                  size: 64,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 16),
                const Text(
                  'No Doers Available',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'No doers are accepting your ASAP offer at the moment.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
                const SizedBox(height: 24),
                // Add Tip Button - only show for fixed price listings, not open price
                Builder(
                  builder: (context) {
                    print('🔍 DEBUG: Checking tip button visibility');
                    print('🔍 DEBUG: _listing is null: ${_listing == null}');
                    print('🔍 DEBUG: _listing?.isOpenPrice: ${_listing?.isOpenPrice}');
                    print('🔍 DEBUG: Should show tip button: ${_listing?.isOpenPrice != true}');
                    return Container();
                  },
                ),
                if (_listing?.isOpenPrice != true) ...[
                  ElevatedButton.icon(
                    onPressed: _showTipDialog,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                    icon: const Icon(Icons.monetization_on),
                    label: const Text('Add Tip to Attract Doers'),
                  ),
                ],
                // Debug info for open price listings
                if (_listing?.isOpenPrice == true)
                  Container(
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(top: 8),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue.shade600, size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'This is an open price listing. Doers will offer their own prices.',
                            style: TextStyle(
                              color: Colors.blue.shade700,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _convertToPublic,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Constants.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Convert to Public Listing'),
                ),
                const SizedBox(height: 80), // Add space for bottom navigation
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _showRescanSliderDialog,
            style: ElevatedButton.styleFrom(
              backgroundColor: Constants.primaryColor,
              foregroundColor: Colors.white,
            ),
            icon: const Icon(Icons.refresh),
            label: const Text('Rescan'),
          ),
        ),
      ),
    );
  }

  void _showTipDialog() {
    double tipAmount = 0.0;
    final TextEditingController tipController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Add Tip'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Adding a tip can make your ASAP listing more attractive to doers and increase your chances of finding someone quickly.',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: tipController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Tip Amount (₱)',
                  border: OutlineInputBorder(),
                  prefixText: '₱ ',
                ),
                onChanged: (value) {
                  tipAmount = double.tryParse(value) ?? 0.0;
                },
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildQuickTipButton(50, tipController, (amount) {
                    setDialogState(() {
                      tipAmount = amount;
                    });
                  }),
                  _buildQuickTipButton(100, tipController, (amount) {
                    setDialogState(() {
                      tipAmount = amount;
                    });
                  }),
                  _buildQuickTipButton(200, tipController, (amount) {
                    setDialogState(() {
                      tipAmount = amount;
                    });
                  }),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (tipAmount > 0) {
                  Navigator.of(context).pop();
                  _addTipToListing(tipAmount);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: const Text('Add Tip'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickTipButton(double amount, TextEditingController controller, Function(double) onAmountSelected) {
    return ElevatedButton(
      onPressed: () {
        controller.text = amount.toString();
        onAmountSelected(amount); // Update the tipAmount variable when preset button is pressed
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.grey[200],
        foregroundColor: Colors.black,
        minimumSize: const Size(60, 36),
      ),
      child: Text('₱${amount.toInt()}'),
    );
  }

  Future<void> _addTipToListing(double tipAmount) async {
    try {
      final listingId = _createdListingId ?? widget.listingId;
      if (listingId == null) {
        throw Exception('No listing ID available');
      }
      
      // Update the listing with tip amount
      final response = await _asapListingService.addTipToListing(
        listingId: listingId,
        tipAmount: tipAmount,
      );
      
      if (response['success']) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Tip added successfully!'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );

        // Restart the unified search
        _startUnifiedSearch();
      } else {
        throw Exception(response['message']);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to add tip: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('Available Doers'),
      backgroundColor: Constants.primaryColor,
      foregroundColor: Colors.white,
    );
  }

  // Show filter dialog for doer search
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Doers'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Distance filter
            ListTile(
              title: const Text('Maximum Distance'),
              subtitle: Text('${_maxDistance.toInt()} km'),
              trailing: DropdownButton<double>(
                value: _maxDistance,
                items: [1.0, 2.0, 3.0, 4.0, 5.0, 10.0].map((distance) {
                  return DropdownMenuItem(
                    value: distance,
                    child: Text('${distance.toInt()} km'),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _maxDistance = value;
                    });
                    Navigator.of(context).pop();
                    _searchForDoers();
                  }
                },
              ),
            ),
            // Gender filter
            ListTile(
              title: const Text('Preferred Gender'),
              subtitle: Text(_preferredDoerGender),
              trailing: DropdownButton<String>(
                value: _preferredDoerGender,
                items: ['Any', 'Male', 'Female'].map((gender) {
                  return DropdownMenuItem(
                    value: gender,
                    child: Text(gender),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _preferredDoerGender = value;
                    });
                    Navigator.of(context).pop();
                    _searchForDoers();
                  }
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _searchForDoers();
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  // Show combined filter and rescan dialog with slider up to 100km
  void _showFilterAndRescanDialog() {
    double tempDistance = _maxDistance;
    String tempGender = _preferredDoerGender;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Rescan & Filter Doers'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Distance slider (up to 100km)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Maximum Distance: ${tempDistance.toInt()} km',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Slider(
                    value: tempDistance,
                    min: 1.0,
                    max: 100.0,
                    divisions: 99, // Creates 100 steps (1km to 100km)
                    label: '${tempDistance.toInt()} km',
                    onChanged: (value) {
                      setDialogState(() {
                        tempDistance = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                ],
              ),
              // Gender filter
              ListTile(
                title: const Text('Preferred Gender'),
                subtitle: Text(tempGender),
                trailing: DropdownButton<String>(
                  value: tempGender,
                  items: ['Any', 'Male', 'Female'].map((gender) {
                    return DropdownMenuItem(
                      value: gender,
                      child: Text(gender),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setDialogState(() {
                        tempGender = value;
                      });
                    }
                  },
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _maxDistance = tempDistance;
                  _preferredDoerGender = tempGender;
                });
                Navigator.of(context).pop();
                _searchForDoers();
              },
              child: const Text('Rescan & Apply'),
            ),
          ],
        ),
      ),
    );
  }

  // Show rescan dialog with slider for 1-100km
  void _showRescanSliderDialog() {
    double tempDistance = _maxDistance.clamp(1.0, 100.0);
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Rescan Doers'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Search Radius: ${tempDistance.toInt()} km',
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Slider(
                value: tempDistance,
                min: 1.0,
                max: 100.0,
                divisions: 99,
                label: '${tempDistance.toInt()} km',
                onChanged: (value) {
                  setDialogState(() {
                    tempDistance = value;
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _maxDistance = tempDistance;
                });
                Navigator.of(context).pop();
                _searchForDoers();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Constants.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Rescan'),
            ),
          ],
        ),
      ),
    );
  }
}

// Enhanced spinner widget with multiple animations
class _EnhancedSpinnerWidget extends StatefulWidget {
  const _EnhancedSpinnerWidget();

  @override
  State<_EnhancedSpinnerWidget> createState() => _EnhancedSpinnerWidgetState();
}

class _EnhancedSpinnerWidgetState extends State<_EnhancedSpinnerWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late AnimationController _dotsController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();

    // Pulse animation for outer circle
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Rotation animation for inner spinner
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(_rotationController);

    // Dots animation
    _dotsController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // Start animations
    _pulseController.repeat(reverse: true);
    _rotationController.repeat();
    _dotsController.repeat();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    _dotsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 100,
      height: 100,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Outer pulsing circle
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Constants.primaryColor.withOpacity(0.1),
                    border: Border.all(
                      color: Constants.primaryColor.withOpacity(0.3),
                      width: 2,
                    ),
                  ),
                ),
              );
            },
          ),

          // Rotating dots around the circle
          AnimatedBuilder(
            animation: _dotsController,
            builder: (context, child) {
              return SizedBox(
                width: 70,
                height: 70,
                child: Stack(
                  children: List.generate(8, (index) {
                    final angle = (index * math.pi * 2 / 8) + (_dotsController.value * math.pi * 2);
                    final x = 35 + 25 * math.cos(angle);
                    final y = 35 + 25 * math.sin(angle);

                    return Positioned(
                      left: x - 3,
                      top: y - 3,
                      child: Container(
                        width: 6,
                        height: 6,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Constants.primaryColor.withOpacity(
                            0.3 + 0.7 * math.sin(_dotsController.value * math.pi * 2 + index * math.pi / 4).abs(),
                          ),
                        ),
                      ),
                    );
                  }),
                ),
              );
            },
          ),

          // Inner rotating spinner with gradient
          AnimatedBuilder(
            animation: _rotationAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotationAnimation.value * math.pi * 2,
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: SweepGradient(
                      colors: [
                        Constants.primaryColor,
                        Constants.primaryColor.withOpacity(0.1),
                        Constants.primaryColor,
                      ],
                      stops: const [0.0, 0.5, 1.0],
                    ),
                  ),
                ),
              );
            },
          ),

          // Center search icon
          Container(
            width: 30,
            height: 30,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Constants.primaryColor,
              boxShadow: [
                BoxShadow(
                  color: Constants.primaryColor.withOpacity(0.3),
                  blurRadius: 8,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: const Icon(
              Icons.search,
              color: Colors.white,
              size: 16,
            ),
          ),
        ],
      ),
    );
  }
}

// Typing text effect widget with two phases
class _TypingTextWidget extends StatefulWidget {
  final String text;
  final TextStyle style;
  final Duration typingSpeed;

  const _TypingTextWidget({
    required this.text,
    required this.style,
    this.typingSpeed = const Duration(milliseconds: 100),
  });

  @override
  State<_TypingTextWidget> createState() => _TypingTextWidgetState();
}

class _TypingTextWidgetState extends State<_TypingTextWidget> {
  String _displayedText = '';
  Timer? _typingTimer;
  Timer? _dotsTimer;
  int _currentIndex = 0;
  bool _isMainTextComplete = false;
  int _dotsCount = 0;

  @override
  void initState() {
    super.initState();
    _startMainTextTyping();
  }

  @override
  void dispose() {
    _typingTimer?.cancel();
    _dotsTimer?.cancel();
    super.dispose();
  }

  void _startMainTextTyping() {
    _typingTimer = Timer.periodic(widget.typingSpeed, (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      setState(() {
        if (_currentIndex < widget.text.length) {
          _displayedText = widget.text.substring(0, _currentIndex + 1);
          _currentIndex++;
        } else {
          // Main text is complete, start dots animation
          _isMainTextComplete = true;
          timer.cancel();
          _startDotsAnimation();
        }
      });
    });
  }

  void _startDotsAnimation() {
    _dotsTimer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      setState(() {
        _dotsCount = (_dotsCount + 1) % 4; // Cycle through 0, 1, 2, 3
      });
    });
  }

  String _getDotsText() {
    switch (_dotsCount) {
      case 0:
        return '';
      case 1:
        return '.';
      case 2:
        return '..';
      case 3:
        return '...';
      default:
        return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          _displayedText,
          style: widget.style,
        ),
        if (_isMainTextComplete) ...[
          // Show animated dots after main text is complete
          Text(
            _getDotsText(),
            style: widget.style.copyWith(
              color: widget.style.color,
            ),
          ),
        ],
      ],
    );
  }
}