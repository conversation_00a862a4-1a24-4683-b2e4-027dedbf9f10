import 'package:flutter/material.dart';
import 'dart:async'; // For Timer
import 'package:hanapp/utils/auth_service.dart'; // Ensure correct path
import 'package:hanapp/screens/auth/login_screen.dart'; // Navigate to login after verification
import 'package:hanapp/utils/constants.dart' as Constants; // For consistent styling

class EmailVerificationScreen extends StatefulWidget {
  final String email;
  // NEW: Callback for when verification is successful, e.g., for EditProfileScreen
  final Function(String verifiedEmail)? onVerificationSuccess;
  // NEW: Flag to indicate if this is for initial signup or email change
  final bool isForEmailChange;
  final int? userId; // Required if isForEmailChange is true

  const EmailVerificationScreen({
    super.key,
    required this.email,
    this.onVerificationSuccess,
    this.isForEmailChange = false,
    this.userId,
  });

  @override
  State<EmailVerificationScreen> createState() => _EmailVerificationScreenState();
}

class _EmailVerificationScreenState extends State<EmailVerificationScreen> {
  final TextEditingController _otpController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  int _resendTimerSeconds = 60; // Initial timer value
  Timer? _timer;
  bool _canResend = false;

  @override
  void initState() {
    super.initState();
    _startResendTimer();
    _sendInitialCode(); // Send OTP when screen initializes
  }

  // Method to send the initial OTP code
  Future<void> _sendInitialCode() async {
    setState(() {
      _isLoading = true; // Show loading while sending code
    });

    // Determine the user ID to send to the backend
    // Use real userId if available, else fallback to '0'
    String userIdToSend = widget.userId != null
        ? widget.userId.toString()
        : '0';

    // DEBUG PRINT
    print('DEBUG: Sending OTP with userId: ' + userIdToSend + ', email: ' + widget.email);

    final response = await AuthService().requestEmailOtp(
      userId: userIdToSend,
      newEmail: widget.email,
    );

    setState(() {
      _isLoading = false; // Hide loading
    });

    if (response['success']) {
      _showSnackBar('Verification code sent to ${widget.email}');
    } else {
      _showSnackBar('Failed to send verification code: ${response['message']}', isError: true);
    }
  }

  @override
  void dispose() {
    _otpController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  void _startResendTimer() {
    _resendTimerSeconds = 60;
    _canResend = false;
    _timer?.cancel(); // Cancel any existing timer
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_resendTimerSeconds == 0) {
        setState(() {
          _canResend = true;
          timer.cancel();
        });
      } else {
        setState(() {
          _resendTimerSeconds--;
        });
      }
    });
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return; // Ensure widget is still in tree
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
      ),
    );
  }

  Future<void> _verifyEmail() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      // Determine the user ID to send to the backend for verification
      String userIdToSend = widget.isForEmailChange && widget.userId != null
          ? widget.userId.toString()
          : '0'; // Placeholder for initial signup

      // DEBUG PRINT
      print('DEBUG VERIFY: email=' + widget.email + ', verificationCode=' + _otpController.text.trim());

      final response = await AuthService().verifyEmailOtp(
        email: widget.email,
        verificationCode: _otpController.text.trim(),
      );

      setState(() {
        _isLoading = false;
      });

      if (response['success']) {
        _showSnackBar(response['message']);
        if (widget.onVerificationSuccess != null) {
          // If a callback is provided (e.g., from EditProfileScreen), use it
          widget.onVerificationSuccess!.call(widget.email);
          if (mounted) Navigator.of(context).pop(true); // Pop this screen, indicating success
        } else {
          // Otherwise, navigate to login screen (typical for initial signup)
          if (mounted) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const LoginScreen()),
            );
          }
        }
      } else {
        _showSnackBar('Verification failed: ${response['message']}', isError: true);
      }
    }
  }

  Future<void> _resendCode() async {
    if (_canResend) {
      setState(() {
        _isLoading = true;
        _canResend = false; // Disable resend button immediately
      });

      // Determine the user ID to send to the backend for resending
      String userIdToSend = widget.userId != null
          ? widget.userId.toString()
          : '0';

      // DEBUG PRINT
      print('DEBUG: Resending OTP with userId: ' + userIdToSend + ', email: ' + widget.email);

      final response = await AuthService().requestEmailOtp(
        userId: userIdToSend,
        newEmail: widget.email,
      );

      setState(() {
        _isLoading = false;
      });

      if (response['success']) {
        _showSnackBar(response['message']);
        _startResendTimer(); // Restart timer
      } else {
        _showSnackBar('Resend failed: ${response['message']}', isError: true);
        setState(() {
          _canResend = true; // Re-enable resend if there was an error
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Email Verification'),
        backgroundColor: Constants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Replaced Image.asset with a more robust Icon fallback
                Image.asset(
                  'assets/2.png', // Your app logo
                  height: 80,
                  errorBuilder: (context, error, stackTrace) {
                    return const Icon(Icons.email, size: 80, color: Constants.primaryColor); // Fallback icon
                  },
                ),
                const SizedBox(height: 32),
                const Text(
                  'Email Verification',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Constants.primaryColor),
                ),
                const SizedBox(height: 16),
                Text(
                  'Enter the OTP code sent to your email (${widget.email}).',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16, color: Colors.grey.shade700),
                ),
                const SizedBox(height: 32),
                TextFormField(
                  controller: _otpController,
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.center,
                  maxLength: 6,
                  decoration: InputDecoration(
                    labelText: 'Enter OTP Code',
                    hintText: 'XXXXXX',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                    counterText: "", // Hide the default counter
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter the verification code';
                    }
                    if (value.length != 6 || !RegExp(r'^[0-9]+$').hasMatch(value)) {
                      return 'Please enter a valid 6-digit code';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),
                Align(
                  alignment: Alignment.center,
                  child: TextButton(
                    onPressed: _canResend ? _resendCode : null,
                    child: Text(
                      _canResend ? 'Resend Code' : 'Resend code in: $_resendTimerSeconds',
                      style: TextStyle(
                        color: _canResend ? Constants.primaryColor : Colors.grey,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 32),
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : ElevatedButton(
                  onPressed: _verifyEmail,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Constants.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Verify & Continue',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ),
                const SizedBox(height: 16),
                // Only show "Back to Login" if not for email change
                if (!widget.isForEmailChange)
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pushReplacement(
                        MaterialPageRoute(builder: (context) => const LoginScreen()),
                      );
                    },
                    child: Text(
                      'Back to Login',
                      style: TextStyle(color: Colors.grey.shade700),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
