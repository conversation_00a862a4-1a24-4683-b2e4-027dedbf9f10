<?php
// hanapp_backend/api/wallet/get_hanapp_balance.php
// Separate endpoint to get user's HanApp Balance (wallet balance)
// This is specifically for HanApp Balance cash-in functionality

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Start output buffering to prevent any unexpected output
ob_start();

require_once '../../config/db_connect.php';

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    ob_clean();
    http_response_code(200);
    exit();
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    ob_clean();
    echo json_encode(["success" => false, "message" => "Only GET method is allowed."]);
    exit();
}

try {
    $userId = $_GET['user_id'] ?? null;

    if (empty($userId) || !is_numeric($userId)) {
        ob_clean();
        echo json_encode(["success" => false, "message" => "User ID is required and must be numeric."]);
        exit();
    }

    // Check if user exists and get their balance
    $stmt = $conn->prepare("SELECT id, balance FROM users WHERE id = ?");
    if ($stmt === false) {
        error_log("get_hanapp_balance.php: Failed to prepare statement: " . $conn->error);
        throw new Exception("Failed to prepare database statement.");
    }

    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        $stmt->close();
        ob_clean();
        echo json_encode(["success" => false, "message" => "User not found."]);
        exit();
    }

    $user = $result->fetch_assoc();
    $stmt->close();

    // Return the balance
    $balance = floatval($user['balance'] ?? 0.0);

    ob_clean();
    echo json_encode([
        "success" => true,
        "balance" => number_format($balance, 2, '.', ''),
        "user_id" => intval($userId),
        "message" => "Balance retrieved successfully."
    ]);

} catch (Exception $e) {
    error_log("get_hanapp_balance.php: Exception occurred: " . $e->getMessage());
    ob_clean();
    echo json_encode([
        "success" => false,
        "message" => "An error occurred while retrieving balance.",
        "error" => $e->getMessage()
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
