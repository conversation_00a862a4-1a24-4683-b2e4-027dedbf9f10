<?php
// hanapp_backend/request_email_otp.php

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");


// Handle OPTIONS requests for CORS preflight
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'config/db_connect.php';
require_once __DIR__ . '/utils/email_sender.php';

$input = json_decode(file_get_contents('php://input'), true);

$user_id = $input['user_id'] ?? null; // Can be '0' for initial signup
$email_to_verify = $input['new_email'] ?? null; // Using new_email for consistency with Flutter

if (empty($user_id) || empty($email_to_verify)) {
    echo json_encode(['success' => false, 'message' => 'Missing user ID or email.']);
    exit();
}

// Validate email format
if (!filter_var($email_to_verify, FILTER_VALIDATE_EMAIL)) {
    echo json_encode(['success' => false, 'message' => 'Invalid email format.']);
    exit();
}

try {
    if (!isset($conn) || $conn->connect_error) {
        echo json_encode(['success' => false, 'message' => 'Database connection failed.']);
        exit();
    }

    // Check if the email is already taken by another *active* user (excluding the current user if updating)
    // For initial signup (user_id = '0'), this checks if the email exists at all.
    // For email change, it checks if the new email is already used by someone else.
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
    $stmt->bind_param("ss", $email_to_verify, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->fetch_assoc()) {
        echo json_encode(['success' => false, 'message' => 'This email is already registered to another account.']);
        exit();
    }

    // Get user's full name for the email (if user_id is not '0')
    $fullName = "User"; // Default for initial signup
    if ($user_id != '0') {
        $stmt = $conn->prepare("SELECT full_name FROM users WHERE id = ?");
        $stmt->bind_param("s", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
        if ($user) {
            $fullName = htmlspecialchars($user['full_name']);
        }
    }

    // Generate a 6-digit OTP
    $otp_code = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
    $otp_expiry = date('Y-m-d H:i:s', strtotime('+10 minutes')); // OTP expires in 10 minutes
    
    // Generate unique request ID for idempotency
    $requestId = uniqid('email_otp_', true);

    // Store/Update OTP in users table
    $stmt = $conn->prepare("UPDATE users SET verification_code = ? WHERE id = ?");
    $stmt->bind_param("ss", $otp_code, $user_id);
    $stmt->execute();

    // Prepare email content
    $emailSubject = 'TAPP: Your Verification Code';
    $emailBodyHtml = '
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background-color: #f9f9f9; }
                .header { background-color: #141CC9; color: white; padding: 15px; text-align: center; border-radius: 8px 8px 0 0; }
                .content { padding: 20px 0; }
                .code { font-size: 24px; font-weight: bold; color: #141CC9; text-align: center; padding: 15px; border: 1px dashed #141CC9; border-radius: 5px; margin: 20px 0; display: inline-block; }
                .footer { text-align: center; font-size: 12px; color: #777; margin-top: 20px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>TAPP Email Verification</h2>
                </div>
                <div class="content">
                    <p>Hi ' . $fullName . ',</p>
                    <p>Please use the following code to verify your email:</p>
                    <div class="code">' . htmlspecialchars($otp_code) . '</div>
                    <p>This code is valid for 10 minutes. If you did not request this, please ignore this email.</p>
                    <p>Best regards,<br>The TAPP Team</p>
                </div>
                <div class="footer">
                    <p>&copy; ' . date('Y') . ' TAPP. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
    ';
    $emailBodyText = 'Your TAPP verification code is: ' . $otp_code . '. This code is valid for 10 minutes. Do not share this code with anyone.';
    
    // Generate idempotency key and send verification email
    $idempotencyKey = hash('sha256', $requestId);
    $mailResult = sendEmailViaSendGrid(
        $email_to_verify, 
        $fullName, 
        $emailSubject, 
        $emailBodyHtml, 
        $emailBodyText,
        null,
        [],
        $idempotencyKey
    );
    
    if ($mailResult['success']) {
        echo json_encode(["success" => true, "message" => "Verification code has been sent to your email."]);
    } else {
        error_log("Failed to send verification email via SendGrid: " . $mailResult['message']);
        echo json_encode(["success" => false, "message" => "Failed to send verification email. Error: " . $mailResult['message']]);
    }

} catch (mysqli_sql_exception $e) {
    error_log("Database Error in request_email_otp.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error.']);
} catch (Exception $e) {
    error_log("General Error in request_email_otp.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An unexpected error occurred.']);
}
?>
