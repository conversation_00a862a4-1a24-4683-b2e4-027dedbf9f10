<?php
// hanapp_backend/api/wallet/xendit_disbursement_webhook.php
// Handle Xendit webhook notifications for disbursement status updates

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Start output buffering to prevent any unexpected output
ob_start();

require_once '../../config/db_connect.php';

header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ob_clean();
    http_response_code(405);
    echo json_encode(["error" => "Method not allowed"]);
    exit();
}

try {
    // Get webhook payload
    $payload = file_get_contents('php://input');
    $webhookData = json_decode($payload, true);

    if (!$webhookData) {
        throw new Exception("Invalid webhook payload");
    }

    // Log webhook for debugging
    error_log("=== XENDIT DISBURSEMENT WEBHOOK RECEIVED ===");
    error_log("Webhook payload: " . $payload);

    // Verify webhook signature (optional but recommended for production)
    $xenditWebhookToken = 'ibwb3mKaoLK41jzM8145jUOQSAy9714RFRATVW270sAr';
    $receivedSignature = $_SERVER['HTTP_X_CALLBACK_TOKEN'] ?? '';

    // Optional: Enable signature verification for production
    // if ($receivedSignature !== $xenditWebhookToken) {
    //     throw new Exception("Invalid webhook signature");
    // }

    // Extract webhook data
    $disbursementId = $webhookData['id'] ?? null;
    $externalId = $webhookData['external_id'] ?? null;
    $status = $webhookData['status'] ?? null;
    $amount = $webhookData['amount'] ?? null;
    $currency = $webhookData['currency'] ?? 'PHP';
    $failureReason = $webhookData['failure_reason'] ?? null;

    if (!$disbursementId || !$externalId || !$status) {
        throw new Exception("Missing required webhook fields");
    }

    error_log("Disbursement webhook - ID: $disbursementId, External: $externalId, Status: $status");

    // Find the disbursement record
    $stmt = $conn->prepare("
        SELECT xd.*, wr.user_id, wr.amount as withdrawal_amount
        FROM xendit_disbursements xd
        JOIN withdrawal_requests wr ON xd.withdrawal_request_id = wr.id
        WHERE xd.xendit_disbursement_id = ? OR xd.xendit_external_id = ?
    ");

    if (!$stmt) {
        throw new Exception("Failed to prepare disbursement lookup statement");
    }

    $stmt->bind_param("ss", $disbursementId, $externalId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        $stmt->close();
        error_log("Disbursement not found for ID: $disbursementId, External: $externalId");
        // Return success to prevent Xendit retries
        ob_clean();
        echo json_encode(["status" => "ok", "message" => "Disbursement not found"]);
        exit();
    }

    $disbursement = $result->fetch_assoc();
    $stmt->close();

    $withdrawalRequestId = $disbursement['withdrawal_request_id'];
    $userId = $disbursement['user_id'];

    error_log("Found disbursement - Withdrawal ID: $withdrawalRequestId, User ID: $userId");

    // Start transaction for atomic updates
    $conn->autocommit(false);

    try {
        // Update xendit_disbursements table
        $stmt = $conn->prepare("
            UPDATE xendit_disbursements
            SET disbursement_status = ?,
                disbursement_completed_at = ?,
                disbursement_error_message = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE xendit_disbursement_id = ?
        ");

        $completedAt = ($status === 'COMPLETED') ? date('Y-m-d H:i:s') : null;
        $stmt->bind_param("ssss", $status, $completedAt, $failureReason, $disbursementId);
        $stmt->execute();
        $stmt->close();

        // Update withdrawal_requests table based on status
        $withdrawalStatus = 'processing'; // Default

        switch (strtoupper($status)) {
            case 'COMPLETED':
                $withdrawalStatus = 'approved';
                error_log("Disbursement completed successfully - Withdrawal ID: $withdrawalRequestId");
                break;

            case 'FAILED':
                $withdrawalStatus = 'failed';
                error_log("Disbursement failed - Withdrawal ID: $withdrawalRequestId, Reason: $failureReason");
                break;

            case 'PENDING':
                $withdrawalStatus = 'processing';
                error_log("Disbursement pending - Withdrawal ID: $withdrawalRequestId");
                break;
        }

        $stmt = $conn->prepare("
            UPDATE withdrawal_requests
            SET status = ?,
                admin_notes = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");

        $adminNotes = "Xendit disbursement status: $status" . ($failureReason ? " - $failureReason" : "");
        $stmt->bind_param("ssi", $withdrawalStatus, $adminNotes, $withdrawalRequestId);
        $stmt->execute();
        $stmt->close();

        // Update transactions table
        $transactionStatus = ($status === 'COMPLETED') ? 'completed' : (($status === 'FAILED') ? 'failed' : 'processing');

        $stmt = $conn->prepare("
            UPDATE transactions
            SET status = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE user_id = ? AND type = 'cash_out' AND xendit_external_id = ?
        ");

        $stmt->bind_param("sis", $transactionStatus, $userId, $externalId);
        $stmt->execute();
        $stmt->close();

        // Commit transaction
        $conn->commit();

        error_log("Disbursement webhook processed successfully - Status: $status, Withdrawal: $withdrawalRequestId");

        // Return success response
        ob_clean();
        echo json_encode([
            "status" => "ok",
            "message" => "Disbursement webhook processed successfully",
            "disbursement_id" => $disbursementId,
            "withdrawal_status" => $withdrawalStatus
        ]);

    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        throw $e;
    }

} catch (Exception $e) {
    error_log("Disbursement webhook error: " . $e->getMessage());

    // Return error response
    ob_clean();
    http_response_code(500);
    echo json_encode([
        "status" => "error",
        "message" => $e->getMessage()
    ]);

} finally {
    if (isset($conn)) {
        $conn->autocommit(true);
        $conn->close();
    }
}
?>
