<?php
// hanapp_backend/api/applications/update_application_status.php
// Updates the status of a specific application and creates notifications for both doers and listers

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

require_once '../config/db_connect.php';

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $input = file_get_contents("php://input");
    $data = json_decode($input, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("update_application_status.php: JSON decode error: " . json_last_error_msg() . ". Raw input: " . $input, 0);
        throw new Exception("Invalid JSON payload.");
    }

    // Debug: Log the received data
    error_log("update_application_status.php: Received data: " . json_encode($data), 0);

    $applicationId = $data['application_id'] ?? null;
    $newStatus = $data['new_status'] ?? null;
    $initiatorId = $data['current_user_id'] ?? null;

    // Debug: Log the extracted values with types
    error_log("update_application_status.php: Extracted values - applicationId: $applicationId (" . gettype($applicationId) . "), newStatus: $newStatus (" . gettype($newStatus) . "), initiatorId: $initiatorId (" . gettype($initiatorId) . ")", 0);

    // More detailed validation with specific error messages
    if (empty($applicationId)) {
        error_log("update_application_status.php: Validation failed - Application ID is empty or null", 0);
        throw new Exception("Application ID is required and cannot be empty.");
    }

    if (!is_numeric($applicationId)) {
        error_log("update_application_status.php: Validation failed - Application ID is not numeric: $applicationId", 0);
        throw new Exception("Application ID must be a valid number.");
    }

    if (empty($newStatus)) {
        error_log("update_application_status.php: Validation failed - New status is empty or null", 0);
        throw new Exception("New status is required and cannot be empty.");
    }

    if (empty($initiatorId)) {
        error_log("update_application_status.php: Validation failed - Current user ID is empty or null", 0);
        throw new Exception("Current user ID is required and cannot be empty.");
    }

    if (!is_numeric($initiatorId)) {
        error_log("update_application_status.php: Validation failed - Current user ID is not numeric: $initiatorId", 0);
        throw new Exception("Current user ID must be a valid number.");
    }

    $allowedStatuses = ['pending', 'accepted', 'rejected', 'in_progress', 'completed', 'cancelled'];
    if (!in_array($newStatus, $allowedStatuses)) {
        throw new Exception("Invalid status provided. Allowed statuses: " . implode(', ', $allowedStatuses));
    }

    $conn->begin_transaction();

    // Debug: Log the application ID being searched
    error_log("update_application_status.php: Searching for application ID: $applicationId", 0);

    // Get application details and verify authorization
    $getAppStmt = $conn->prepare("
        SELECT a.*, 
               COALESCE(pl.title, al.title) as listing_title,
               COALESCE(pl.lister_id, al.lister_id) as lister_id,
               COALESCE(pl.price, al.price) as price,
               u1.full_name as lister_name, 
               u2.full_name as doer_name
        FROM applicationsv2 a
        LEFT JOIN listingsv2 pl ON a.listing_id = pl.id AND a.listing_type = 'PUBLIC'
        LEFT JOIN asap_listings al ON a.listing_id = al.id AND a.listing_type = 'ASAP'
        JOIN users u1 ON COALESCE(pl.lister_id, al.lister_id) = u1.id
        JOIN users u2 ON a.doer_id = u2.id
        WHERE a.id = ?
    ");
    if ($getAppStmt === false) {
        throw new Exception("Failed to prepare application query: " . $conn->error);
    }
    $getAppStmt->bind_param("i", $applicationId);
    $getAppStmt->execute();
    $appResult = $getAppStmt->get_result();
    
    // Debug: Log the number of rows found
    error_log("update_application_status.php: Found " . $appResult->num_rows . " rows for application ID: $applicationId", 0);
    
    if ($appResult->num_rows === 0) {
        // Debug: Check if the application exists without joins
        $checkAppStmt = $conn->prepare("SELECT id, listing_id, listing_type, lister_id, doer_id, status FROM applicationsv2 WHERE id = ?");
        if ($checkAppStmt === false) {
            error_log("update_application_status.php: Failed to prepare check application statement: " . $conn->error, 0);
        } else {
            $checkAppStmt->bind_param("i", $applicationId);
            $checkAppStmt->execute();
            $checkResult = $checkAppStmt->get_result();
            error_log("update_application_status.php: Application exists in applicationsv2: " . ($checkResult->num_rows > 0 ? "YES" : "NO"), 0);
            
            if ($checkResult->num_rows > 0) {
                $appData = $checkResult->fetch_assoc();
                error_log("update_application_status.php: Application data: " . json_encode($appData), 0);
                
                // Check if listing exists
                $checkListingStmt = $conn->prepare("SELECT id, title FROM listingsv2 WHERE id = ?");
                if ($checkListingStmt === false) {
                    error_log("update_application_status.php: Failed to prepare check listing statement: " . $conn->error, 0);
                } else {
                    $checkListingStmt->bind_param("i", $appData['listing_id']);
                    $checkListingStmt->execute();
                    $listingResult = $checkListingStmt->get_result();
                    error_log("update_application_status.php: Listing exists: " . ($listingResult->num_rows > 0 ? "YES" : "NO"), 0);
                    
                    if ($listingResult->num_rows > 0) {
                        $listingData = $listingResult->fetch_assoc();
                        error_log("update_application_status.php: Listing data: " . json_encode($listingData), 0);
                    }
                    $checkListingStmt->close();
                }
            }
            $checkAppStmt->close();
        }
        
        throw new Exception("Application not found.");
    }
    
    $application = $appResult->fetch_assoc();
    $getAppStmt->close();

    $listerId = $application['lister_id'];
    $doerId = $application['doer_id'];
    $currentApplicationStatus = $application['status'];

    $isLister = ($initiatorId == $listerId);
    $isDoer = ($initiatorId == $doerId);

    $authorized = false;
    $message = "";

    switch ($newStatus) {
        case 'accepted':
            if ($isLister && $currentApplicationStatus == 'pending') {
                $authorized = true;
            } else {
                $message = "Only the lister can accept a pending application.";
            }
            break;
        case 'rejected':
            if ($isLister && ($currentApplicationStatus == 'pending' || $currentApplicationStatus == 'accepted' || $currentApplicationStatus == 'in_progress')) {
                $authorized = true;
            } else {
                $message = "Only the lister can reject an application from pending/accepted/in-progress status.";
            }
            break;
        case 'in_progress':
            if ($isLister && ($currentApplicationStatus == 'pending' || $currentApplicationStatus == 'accepted')) {
                $authorized = true;
            } else {
                $message = "Only the lister can set status to 'in_progress' from pending or accepted status.";
            }
            break;
        case 'completed':
            if ($isLister && $currentApplicationStatus == 'in_progress') {
                $authorized = true;
            } else {
                $message = "Only the lister can mark an 'in_progress' project as completed.";
            }
            break;
        case 'cancelled':
            if (($isLister || $isDoer) && ($currentApplicationStatus == 'pending' || $currentApplicationStatus == 'accepted' || $currentApplicationStatus == 'in_progress')) {
                $authorized = true;
            } else {
                $message = "Only the lister or doer can cancel an active application/project.";
            }
            break;
        case 'pending':
            $message = "Cannot set status directly to 'pending' via this endpoint.";
            break;
        default:
            $message = "Invalid status update requested.";
            break;
    }

    if (!$authorized) {
        throw new Exception("Unauthorized or invalid status transition: " . $message);
    }

    // Update application status
    $updateSql = "UPDATE applicationsv2 SET status = ?";
    $params = [$newStatus];
    $types = "s";

    if ($newStatus === 'in_progress' && ($currentApplicationStatus == 'pending' || $currentApplicationStatus == 'accepted')) {
        $updateSql .= ", project_start_date = NOW()";
    }

    if ($newStatus === 'completed' && $currentApplicationStatus === 'in_progress') {
        $updateSql .= ", project_end_date = NOW()";
    }

    $updateSql .= " WHERE id = ?";
    $params[] = $applicationId;
    $types .= "i";

    $updateStmt = $conn->prepare($updateSql);
    if ($updateStmt === false) {
        throw new Exception("Failed to prepare update statement: " . $conn->error);
    }

    call_user_func_array([$updateStmt, 'bind_param'], array_merge([$types], $params));

    if (!$updateStmt->execute()) {
        throw new Exception("Failed to update application status: " . $updateStmt->error);
    }

    if ($updateStmt->affected_rows === 0) {
        error_log("update_application_status.php: No rows affected for application $applicationId to $newStatus. Current status: $currentApplicationStatus", 0);
    }

    $updateStmt->close();

    // NEW: Update listing status to 'completed' when application status is set to 'completed'
    if ($newStatus === 'completed') {
        if ($application['listing_type'] === 'PUBLIC') {
            $updateListingStmt = $conn->prepare("UPDATE listingsv2 SET status = 'completed' WHERE id = ?");
            if ($updateListingStmt === false) {
                error_log("update_application_status.php: Failed to prepare PUBLIC listing status update: " . $conn->error, 0);
            } else {
                $updateListingStmt->bind_param("i", $application['listing_id']);
                if (!$updateListingStmt->execute()) {
                    error_log("update_application_status.php: Failed to update PUBLIC listing status: " . $updateListingStmt->error, 0);
                } else {
                    error_log("update_application_status.php: Successfully updated PUBLIC listing {$application['listing_id']} status to 'completed'", 0);
                }
                $updateListingStmt->close();
            }
        } elseif ($application['listing_type'] === 'ASAP') {
            $updateAsapStmt = $conn->prepare("UPDATE asap_listings SET status = 'completed' WHERE id = ?");
            if ($updateAsapStmt === false) {
                error_log("update_application_status.php: Failed to prepare ASAP listing status update: " . $conn->error, 0);
            } else {
                $updateAsapStmt->bind_param("i", $application['listing_id']);
                if (!$updateAsapStmt->execute()) {
                    error_log("update_application_status.php: Failed to update ASAP listing status: " . $updateAsapStmt->error, 0);
                } else {
                    error_log("update_application_status.php: Successfully updated ASAP listing {$application['listing_id']} status to 'completed'", 0);
                }
                $updateAsapStmt->close();
            }
        }
    }

    // NEW: Revert ASAP listing status to 'pending' when application is rejected
    if ($newStatus === 'rejected' && $application['listing_type'] === 'ASAP') {
        $revertAsapSql = "UPDATE asap_listings SET status = 'pending' WHERE id = ?";
        $revertAsapStmt = $conn->prepare($revertAsapSql);
        if ($revertAsapStmt === false) {
            error_log("update_application_status.php: Failed to prepare ASAP listing revert statement: " . $conn->error, 0);
        } else {
            $revertAsapStmt->bind_param("i", $application['listing_id']);
            if (!$revertAsapStmt->execute()) {
                error_log("update_application_status.php: Failed to revert ASAP listing status: " . $revertAsapStmt->error, 0);
            } else {
                error_log("update_application_status.php: Successfully reverted ASAP listing {$application['listing_id']} status to 'pending'", 0);
            }
            $revertAsapStmt->close();
        }
    }

    // Create notifications based on status change
    $doerNotificationTitle = '';
    $doerNotificationContent = '';
    $doerNotificationType = '';
    $listerNotificationTitle = '';
    $listerNotificationContent = '';
    $listerNotificationType = '';

    switch ($newStatus) {
        case 'accepted':
            $doerNotificationTitle = 'Application Accepted';
            $doerNotificationContent = "Your application for '{$application['listing_title']}' has been accepted!";
            $doerNotificationType = 'application_accepted';
            break;
            
        case 'rejected':
            $doerNotificationTitle = 'Application Rejected';
            $doerNotificationContent = "Your application for '{$application['listing_title']}' was not selected.";
            $doerNotificationType = 'application_rejected';
            break;
            
        case 'in_progress':
            $doerNotificationTitle = 'Project Started';
            $doerNotificationContent = "The project '{$application['listing_title']}' has started. Good luck!";
            $doerNotificationType = 'project_started';
            break;
            
        case 'completed':
            $doerNotificationTitle = 'Job Completed';
            $doerNotificationContent = "Congratulations! The job '{$application['listing_title']}' has been completed.";
            $doerNotificationType = 'job_completed';
            break;
            
        case 'cancelled':
            if ($isLister) {
                $doerNotificationTitle = 'Application Cancelled by Lister';
                $doerNotificationContent = "Your application for '{$application['listing_title']}' has been cancelled by the lister.";
                $doerNotificationType = 'application_cancelled_by_lister';
            } else {
                $doerNotificationTitle = 'Application Cancelled';
                $doerNotificationContent = "Your application for '{$application['listing_title']}' has been cancelled.";
                $doerNotificationType = 'application_cancelled';
                
                $listerNotificationTitle = 'Application Cancelled by Doer';
                $listerNotificationContent = "A doer has cancelled their application for '{$application['listing_title']}'.";
                $listerNotificationType = 'application_cancelled_by_doer';
            }
            break;
    }

    // Insert doer notification if we have one to send
    if ($doerNotificationTitle && $doerId) {
        $doerNotificationSql = "
            INSERT INTO doer_notifications (
                user_id, sender_id, type, title, content, associated_id,
                conversation_id_for_chat_nav, conversation_lister_id, conversation_doer_id,
                related_listing_title, listing_id, listing_type, lister_id, lister_name, is_read
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0)
        ";
        
        $doerNotificationStmt = $conn->prepare($doerNotificationSql);
        if ($doerNotificationStmt === false) {
            error_log("update_application_status.php: Failed to prepare doer notification insert: " . $conn->error, 0);
        } else {
            $doerNotificationStmt->bind_param("iisssiiissiiss",
                $doerId,                    // user_id (doer)
                $listerId,                   // sender_id (lister)
                $doerNotificationType,       // type
                $doerNotificationTitle,      // title
                $doerNotificationContent,    // content
                $applicationId,              // associated_id
                $application['conversation_id'], // conversation_id_for_chat_nav
                $application['lister_id'],       // conversation_lister_id
                $application['doer_id'],         // conversation_doer_id
                $application['listing_title'],   // related_listing_title
                $application['listing_id'],      // listing_id
                $application['listing_type'],    // listing_type (from applicationsv2)
                $application['lister_id'],       // lister_id
                $application['lister_name']      // lister_name
            );
            
            if (!$doerNotificationStmt->execute()) {
                error_log("update_application_status.php: Failed to insert doer notification: " . $doerNotificationStmt->error, 0);
            } else {
                error_log("update_application_status.php: Doer notification created successfully for status: $newStatus", 0);
            }
            $doerNotificationStmt->close();
        }
    }

    // Insert lister notification if we have one to send
    if ($listerNotificationTitle && $listerId) {
        $listerNotificationSql = "
            INSERT INTO notificationsv2 (
                user_id, sender_id, type, title, content, associated_id,
                conversation_id_for_chat_nav, conversation_lister_id, conversation_doer_id,
                related_listing_title, is_read
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0)
        ";
        
        $listerNotificationStmt = $conn->prepare($listerNotificationSql);
        if ($listerNotificationStmt === false) {
            error_log("update_application_status.php: Failed to prepare lister notification insert: " . $conn->error, 0);
        } else {
            $listerNotificationStmt->bind_param("iisssiiiss",
                $listerId,                   // user_id (lister)
                $doerId,                      // sender_id (doer)
                $listerNotificationType,      // type
                $listerNotificationTitle,     // title
                $listerNotificationContent,   // content
                $applicationId,               // associated_id
                $application['conversation_id'], // conversation_id_for_chat_nav
                $application['lister_id'],       // conversation_lister_id
                $application['doer_id'],         // conversation_doer_id
                $application['listing_title']    // related_listing_title
            );
            
            if (!$listerNotificationStmt->execute()) {
                error_log("update_application_status.php: Failed to insert lister notification: " . $listerNotificationStmt->error, 0);
            } else {
                error_log("update_application_status.php: Lister notification created successfully for status: $newStatus", 0);
            }
            $listerNotificationStmt->close();
        }
    }

    // Add additional lister notifications for other status changes
    if (!$listerNotificationTitle && $listerId) {
        switch ($newStatus) {
            case 'accepted':
                $listerNotificationTitle = 'Application Accepted';
                $listerNotificationContent = "You have accepted an application for '{$application['listing_title']}'.";
                $listerNotificationType = 'application_accepted_by_lister';
                break;
                
            case 'rejected':
                $listerNotificationTitle = 'Application Rejected';
                $listerNotificationContent = "You have rejected an application for '{$application['listing_title']}'.";
                $listerNotificationType = 'application_rejected_by_lister';
                break;
                
            case 'in_progress':
                $listerNotificationTitle = 'Project Started';
                $listerNotificationContent = "The project '{$application['listing_title']}' has been started.";
                $listerNotificationType = 'project_started_by_lister';
                break;
                
            case 'completed':
                $listerNotificationTitle = 'Job Completed';
                $listerNotificationContent = "The job '{$application['listing_title']}' has been marked as completed.";
                $listerNotificationType = 'job_completed_by_lister';
                break;
        }

        if ($listerNotificationTitle) {
            $listerNotificationSql = "
                INSERT INTO notificationsv2 (
                    user_id, sender_id, type, title, content, associated_id,
                    conversation_id_for_chat_nav, conversation_lister_id, conversation_doer_id,
                    related_listing_title, is_read
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0)
            ";
            
            $listerNotificationStmt = $conn->prepare($listerNotificationSql);
            if ($listerNotificationStmt === false) {
                error_log("update_application_status.php: Failed to prepare additional lister notification insert: " . $conn->error, 0);
            } else {
                $listerNotificationStmt->bind_param("iisssiiiss",
                    $listerId,                   // user_id (lister)
                    $doerId,                      // sender_id (doer)
                    $listerNotificationType,      // type
                    $listerNotificationTitle,     // title
                    $listerNotificationContent,   // content
                    $applicationId,               // associated_id
                    $application['conversation_id'], // conversation_id_for_chat_nav
                    $application['lister_id'],       // conversation_lister_id
                    $application['doer_id'],         // conversation_doer_id
                    $application['listing_title']    // related_listing_title
                );
                
                if (!$listerNotificationStmt->execute()) {
                    error_log("update_application_status.php: Failed to insert additional lister notification: " . $listerNotificationStmt->error, 0);
                } else {
                    error_log("update_application_status.php: Additional lister notification created successfully for status: $newStatus", 0);
                }
                $listerNotificationStmt->close();
            }
        }
    }

    // NEW: Update doer's total_profit when status changes to 'completed'
    if ($newStatus === 'completed' && $currentApplicationStatus === 'in_progress') {
        $actualDoerFee = 0;
        
        // Check if this is an ASAP listing with open price
        if ($application['listing_type'] === 'ASAP') {
            // First check if there's an accepted price offer for ASAP open price listings
            $checkOfferStmt = $conn->prepare("
                SELECT apo.offered_price, apo.status, al.is_open_price
                FROM asap_price_offers apo
                JOIN asap_listings al ON apo.listing_id = al.id
                WHERE apo.listing_id = ? AND apo.doer_id = ? AND apo.status = 'accepted'
                ORDER BY apo.updated_at DESC
                LIMIT 1
            ");
            
            if ($checkOfferStmt === false) {
                error_log("update_application_status.php: Failed to prepare price offer query: " . $conn->error, 0);
            } else {
                $checkOfferStmt->bind_param("ii", $application['listing_id'], $doerId);
                $checkOfferStmt->execute();
                $offerResult = $checkOfferStmt->get_result();
                
                if ($offerResult->num_rows > 0) {
                    $offerData = $offerResult->fetch_assoc();
                    $isOpenPrice = (bool)$offerData['is_open_price'];
                    
                    if ($isOpenPrice && $offerData['status'] === 'accepted') {
                        // For ASAP open price listings, use the accepted offered price
                        $actualDoerFee = floatval($offerData['offered_price']);
                        error_log("update_application_status.php: Using accepted price offer ₱$actualDoerFee for ASAP open price listing", 0);
                    }
                }
                $checkOfferStmt->close();
            }
        }
        
        // If no price offer found or not an ASAP open price listing, get from transaction record
        if ($actualDoerFee == 0) {
            $getDoerFeeStmt = $conn->prepare("
                SELECT amount
                FROM transactions
                WHERE user_id = ?
                AND type = 'credit'
                AND description = ?
                AND method = 'job_payment'
                AND status = 'completed'
                ORDER BY transaction_date DESC
                LIMIT 1
            ");

            if ($getDoerFeeStmt === false) {
                error_log("update_application_status.php: Failed to prepare doer fee query: " . $conn->error, 0);
            } else {
                $transactionDescription = "Job earning from application #$applicationId";
                $getDoerFeeStmt->bind_param("is", $doerId, $transactionDescription);
                $getDoerFeeStmt->execute();
                $doerFeeResult = $getDoerFeeStmt->get_result();

                if ($doerFeeResult->num_rows > 0) {
                    $doerFeeData = $doerFeeResult->fetch_assoc();
                    $actualDoerFee = floatval($doerFeeData['amount']);
                    error_log("update_application_status.php: Using transaction record doer fee ₱$actualDoerFee", 0);
                } else {
                    error_log("update_application_status.php: No transaction record found for doer $doerId and application #$applicationId", 0);
                }
                $getDoerFeeStmt->close();
            }
        }
        
        // Update both doer's total_profit and application's earned_amount with the actual doer fee
        if ($actualDoerFee > 0) {
            // Update doer's total_profit
            $updateProfitStmt = $conn->prepare("UPDATE users SET total_profit = total_profit + ? WHERE id = ?");
            if ($updateProfitStmt === false) {
                error_log("update_application_status.php: Failed to prepare profit update statement: " . $conn->error, 0);
            } else {
                $updateProfitStmt->bind_param("di", $actualDoerFee, $doerId);
                if (!$updateProfitStmt->execute()) {
                    error_log("update_application_status.php: Failed to update doer profit: " . $updateProfitStmt->error, 0);
                } else {
                    $affectedRows = $updateProfitStmt->affected_rows;
                    error_log("update_application_status.php: Successfully added ₱$actualDoerFee to doer's total_profit (ID: $doerId) for application #$applicationId. Affected rows: $affectedRows", 0);
                }
                $updateProfitStmt->close();
            }
            
            // Update application's earned_amount
            $updateEarnedStmt = $conn->prepare("UPDATE applicationsv2 SET earned_amount = ? WHERE id = ?");
            if ($updateEarnedStmt === false) {
                error_log("update_application_status.php: Failed to prepare earned_amount update statement: " . $conn->error, 0);
            } else {
                $updateEarnedStmt->bind_param("di", $actualDoerFee, $applicationId);
                if (!$updateEarnedStmt->execute()) {
                    error_log("update_application_status.php: Failed to update earned_amount: " . $updateEarnedStmt->error, 0);
                } else {
                    $affectedRows = $updateEarnedStmt->affected_rows;
                    error_log("update_application_status.php: Successfully set earned_amount to ₱$actualDoerFee for application #$applicationId. Affected rows: $affectedRows", 0);
                }
                $updateEarnedStmt->close();
            }
        } else {
            error_log("update_application_status.php: No valid doer fee found to add to total_profit for doer $doerId and application #$applicationId", 0);
        }
    }

    $conn->commit();

    echo json_encode([
        "success" => true,
        "message" => "Application status updated to '$newStatus' successfully."
    ]);

} catch (Exception $e) {
    if (isset($conn) && $conn instanceof mysqli && $conn->in_transaction) {
        $conn->rollback();
    }
    http_response_code(500);
    error_log("update_application_status.php: Caught exception: " . $e->getMessage(), 0);
    echo json_encode([
        "success" => false,
        "message" => "An error occurred: " . $e->getMessage()
    ]);
} finally {
    if (isset($conn) && $conn instanceof mysqli && $conn->ping()) {
        $conn->close();
    }
}
?>