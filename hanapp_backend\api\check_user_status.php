<?php
// hanapp_backend/api/check_user_status.php
// Checks user status and prevents multiple device usage using is_logged_in column

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
ini_set('log_errors', 1);

require_once 'config/db_connect.php';

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $input = file_get_contents("php://input");
    $data = json_decode($input, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("Invalid JSON payload.");
    }

    $userId = $data['user_id'] ?? null;
    $deviceInfo = $data['device_info'] ?? null;
    $action = $data['action'] ?? 'check'; // 'check' or 'login'

    if (empty($userId)) {
        echo json_encode([
            "success" => false, 
            "message" => "User ID is required.",
            "error_type" => "missing_user_id"
        ]);
        exit();
    }

    // Get user information including login status
    $stmt = $conn->prepare("
        SELECT
            id, full_name, email, role, is_verified, profile_picture_url,
            address_details, contact_number, latitude, longitude, is_available,
            banned_until, is_deleted, is_logged_in, created_at, updated_at
        FROM users
        WHERE id = ?
    ");

    if ($stmt === false) {
        throw new Exception("Failed to prepare database statement.");
    }

    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    $stmt->close();

    if (!$user) {
        echo json_encode([
            "success" => false, 
            "message" => "User not found.",
            "error_type" => "user_not_found"
        ]);
        exit();
    }

    // Check if user is deleted
    if ($user['is_deleted'] == 1 || $user['is_deleted'] === '1' || $user['is_deleted'] === true) {
        echo json_encode([
            "success" => false, 
            "message" => "Account has been deleted.",
            "error_type" => "account_deleted"
        ]);
        exit();
    }

    // Check if user is banned
    if ($user['banned_until'] !== null) {
        $banned_until = new DateTime($user['banned_until']);
        $current_time = new DateTime();
        
        if ($banned_until > $current_time) {
            $formatted_date = $banned_until->format('F j, Y \a\t g:i A');
            echo json_encode([
                "success" => false, 
                "message" => "Your account has been banned until $formatted_date.",
                "error_type" => "account_banned",
                "banned_until" => $user['banned_until']
            ]);
            exit();
        }
    }

    // Handle login action
    if ($action === 'login') {
        if ($user['is_logged_in'] == 1) {
            // User is already logged in - send email notification for returning login
            try {
                    // Get device and location info
                    $ip_address = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
                    if (strpos($ip_address, ',') !== false) {
                        $ip_address = trim(explode(',', $ip_address)[0]);
                    }
                    
                    $latitude = $data['latitude'] ?? null;
                    $longitude = $data['longitude'] ?? null;
                    $location = 'Unknown';
                    
                    // Try to get location from coordinates if available
                    if ($latitude && $longitude) {
                        $location = "Lat: $latitude, Lng: $longitude";
                    }
                    
                    // Prepare data for login notification
                    $notificationData = [
                        'user_id' => $userId,
                        'device_info' => $deviceInfo ?? 'Unknown Device',
                        'ip_address' => $ip_address,
                        'location' => $location,
                        'login_time' => date('Y-m-d H:i:s')
                    ];
                    
                    // Call the send login notification API
                    $notificationUrl = 'https://autosell.io//api/send_login_notification.php';
                    $postData = json_encode($notificationData);
                    
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $notificationUrl);
                    curl_setopt($ch, CURLOPT_POST, true);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
                    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                    
                    $response = curl_exec($ch);
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);
                    
                    if ($httpCode === 200) {
                        $responseData = json_decode($response, true);
                        if ($responseData && $responseData['success']) {
                            // Email sent successfully, allow login but inform user
                            echo json_encode([
                                "success" => true,
                                "message" => "Login successful. A security notification has been sent to your email.",
                                "security_notification_sent" => true,
                                "confirmation_token" => $responseData['confirmation_token'] ?? null
                            ]);
                            exit();
                        } else {
                            // Email failed, but still allow login
                            echo json_encode([
                                "success" => true,
                                "message" => "Login successful. Note: Security notification email could not be sent.",
                                "security_notification_sent" => false
                            ]);
                            exit();
                        }
                    } else {
                        // Email service failed, but still allow login
                        echo json_encode([
                            "success" => true,
                            "message" => "Login successful. Note: Security notification service is temporarily unavailable.",
                            "security_notification_sent" => false
                        ]);
                        exit();
                    }
                    
                } catch (Exception $e) {
                    error_log("Login notification error: " . $e->getMessage());
                    // If notification fails, still allow login
                    echo json_encode([
                        "success" => true,
                        "message" => "Login successful. Security notification could not be sent.",
                        "security_notification_sent" => false
                    ]);
                    exit();
            }
        } else {
            // First-time login - just update login status without sending notification
            $updateStmt = $conn->prepare("UPDATE users SET is_logged_in = 1 WHERE id = ?");
            if ($updateStmt) {
                $updateStmt->bind_param("i", $userId);
                $updateStmt->execute();
                $updateStmt->close();
            }
        }
    }

    // Clean user data before sending
    unset($user['banned_until']);
    unset($user['is_deleted']);
    unset($user['is_logged_in']); // Don't expose login status to frontend

    // Ensure numeric values are cast to correct types and strings are properly cast
    $user_data = [
        'id' => intval($user['id']),
        'full_name' => (string)($user['full_name'] ?? ''),
        'email' => (string)($user['email'] ?? ''),
        'role' => (string)($user['role'] ?? 'user'),
        'is_verified' => (bool)$user['is_verified'],
        'profile_picture_url' => $user['profile_picture_url'] ? (string)$user['profile_picture_url'] : null,
        'address_details' => $user['address_details'] ? (string)$user['address_details'] : null,
        'latitude' => $user['latitude'] !== null ? floatval($user['latitude']) : null,
        'longitude' => $user['longitude'] !== null ? floatval($user['longitude']) : null,
        'is_available' => $user['is_available'] !== null ? (bool)$user['is_available'] : null,
        'contact_number' => $user['contact_number'] ? (string)$user['contact_number'] : null,
        'created_at' => $user['created_at'],
        'updated_at' => $user['updated_at'],
    ];

    echo json_encode([
        "success" => true,
        "message" => "User status verified successfully.",
        "user" => $user_data,
        "timestamp" => time()
    ]);

} catch (Exception $e) {
    http_response_code(500);
    error_log("check_user_status.php: Caught exception: " . $e->getMessage(), 0);
    echo json_encode([
        "success" => false,
        "message" => "An error occurred: " . $e->getMessage(),
        "error_type" => "server_error"
    ]);
} finally {
    if (isset($conn) && $conn instanceof mysqli && $conn->ping()) {
        $conn->close();
    }
}
?>