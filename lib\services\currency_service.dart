import 'dart:convert';
import 'package:http/http.dart' as http;
import '../utils/api_config.dart';

class CurrencyService {
  static double? _cachedUsdToPhpRate;
  static DateTime? _lastFetchTime;
  static const Duration _cacheExpiry = Duration(hours: 1);



  /// Get current USD to PHP exchange rate from database
  /// Returns cached rate if still valid, otherwise fetches from database endpoint
  static Future<double> getUsdToPhpRate() async {
    // Return cached rate if still valid
    if (_cachedUsdToPhpRate != null &&
        _lastFetchTime != null &&
        DateTime.now().difference(_lastFetchTime!) < _cacheExpiry) {
      print('CurrencyService: Using cached USD/PHP rate: $_cachedUsdToPhpRate');
      return _cachedUsdToPhpRate!;
    }

    print('CurrencyService: Fetching USD/PHP exchange rate from database...');

    // Fetch rate from database endpoint
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.exchangeRateEndpoint),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['success'] == true && data['data'] != null) {
          final rate = data['data']['usd_to_php_rate']?.toDouble();

          if (rate != null && rate > 0 && rate < 100) { // Sanity check: rate should be reasonable
            // Cache the rate
            _cachedUsdToPhpRate = rate;
            _lastFetchTime = DateTime.now();

            print('CurrencyService: Successfully fetched USD/PHP rate from database: $rate');
            print('CurrencyService: Last updated: ${data['data']['last_updated']}');
            return rate;
          } else {
            print('CurrencyService: Invalid rate from database: $rate');
          }
        } else {
          print('CurrencyService: Database API returned error: ${data['message']}');
        }
      } else {
        print('CurrencyService: HTTP ${response.statusCode} from database endpoint');
      }
    } catch (e) {
      print('CurrencyService: Database endpoint failed: $e');
    }

    // Database endpoint failed, use cached rate if available
    if (_cachedUsdToPhpRate != null) {
      print('CurrencyService: Database endpoint failed, using stale cached rate: $_cachedUsdToPhpRate');
      return _cachedUsdToPhpRate!;
    }

    // Final fallback rate
    const fallbackRate = 56.0;
    print('CurrencyService: Database endpoint failed and no cache available, using fallback rate: $fallbackRate');
    return fallbackRate;
  }

  /// Convert USD amount to PHP using current exchange rate
  static Future<double> convertUsdToPhp(double usdAmount) async {
    final rate = await getUsdToPhpRate();
    return usdAmount * rate;
  }



  /// Get exchange rate information for display from database
  static Future<Map<String, dynamic>> getExchangeRateInfo() async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.exchangeRateEndpoint),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['success'] == true && data['data'] != null) {
          // Return the data directly from the server
          return {
            'success': true,
            'rate': data['data']['usd_to_php_rate'],
            'formatted_rate': data['data']['formatted_rate'],
            'last_updated': data['data']['last_updated'],
            'hours_since_update': data['data']['hours_since_update'],
            'is_recent': data['data']['is_recent'],
            'auto_updated': data['data']['auto_updated'],
            'source': data['data']['source'],
          };
        }
      }

      // Fallback if server request fails
      return {
        'success': false,
        'rate': 56.0,
        'formatted_rate': '56.00',
        'last_updated': 'Fallback rate',
        'hours_since_update': 999,
        'is_recent': false,
      };
    } catch (e) {
      print('CurrencyService: Error getting exchange rate info: $e');

      // Fallback
      return {
        'success': false,
        'rate': 56.0,
        'formatted_rate': '56.00',
        'last_updated': 'Error: $e',
        'hours_since_update': 999,
        'is_recent': false,
      };
    }
  }

  /// Clear cached exchange rate (force refresh on next call)
  static void clearCache() {
    _cachedUsdToPhpRate = null;
    _lastFetchTime = null;
    print('CurrencyService: Cache cleared');
  }

  /// Get cached rate without making API call
  static double? getCachedRate() {
    if (_cachedUsdToPhpRate != null &&
        _lastFetchTime != null &&
        DateTime.now().difference(_lastFetchTime!) < _cacheExpiry) {
      return _cachedUsdToPhpRate;
    }
    return null;
  }

  /// Check if cached rate is still valid
  static bool isCacheValid() {
    return _cachedUsdToPhpRate != null &&
        _lastFetchTime != null &&
        DateTime.now().difference(_lastFetchTime!) < _cacheExpiry;
  }

  /// Get time until cache expires
  static Duration? getCacheExpiryTime() {
    if (_lastFetchTime == null) return null;

    final expiryTime = _lastFetchTime!.add(_cacheExpiry);
    final now = DateTime.now();

    if (now.isBefore(expiryTime)) {
      return expiryTime.difference(now);
    }

    return Duration.zero;
  }
}
