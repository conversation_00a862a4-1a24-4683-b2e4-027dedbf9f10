<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Failed - HanApp</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        .error-icon {
            font-size: 64px;
            color: #ff6b6b;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        .btn {
            background: #667eea;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn-retry {
            background: #ff6b6b;
        }
        .btn-retry:hover {
            background: #ff5252;
        }
        .info {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-size: 14px;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .reasons {
            text-align: left;
            margin: 20px 0;
        }
        .reasons ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .reasons li {
            margin: 5px 0;
            color: #666;
        }
        .instruction {
            margin-top: 30px;
            padding: 25px;
            background: #ffe8e8;
            border-radius: 12px;
            text-align: center;
            border: 2px solid #f44336;
        }
        .instruction h3 {
            margin: 0 0 15px 0;
            color: #d32f2f;
            font-size: 20px;
        }
        .instruction p {
            margin: 10px 0;
            font-size: 16px;
            color: #b71c1c;
            line-height: 1.5;
        }
        .instruction p:first-of-type {
            font-weight: bold;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-icon">❌</div>
        <h1>Payment Failed</h1>
        <p id="message">We couldn't process your payment. Don't worry, no money was charged.</p>
        
        <div class="reasons">
            <strong>Common reasons for payment failure:</strong>
            <ul>
                <li>Insufficient balance in your account</li>
                <li>Network connection issues</li>
                <li>Payment method temporarily unavailable</li>
                <li>Transaction timeout</li>
            </ul>
        </div>

        <div class="info">
            <strong>What to do next?</strong><br>
            • Check your account balance<br>
            • Try a different payment method<br>
            • Contact support if the problem persists
        </div>

        <div class="instruction">
            <h3>❌ Payment Could Not Be Processed</h3>
            <p><strong>Please return to the HanApp mobile application to try again.</strong></p>
            <p>No money was charged to your account.</p>
            <p>You can close this page and go back to the app to retry the payment.</p>
        </div>
    </div>

    <script>
        // Simple script to show the page is loaded
        console.log('Badge payment failed page loaded');
    </script>
</body>
</html>
