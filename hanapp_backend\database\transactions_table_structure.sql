-- transactions_table_structure.sql
-- Complete structure for transactions table to support Xendit invoice tracking

CREATE TABLE IF NOT EXISTS `transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` varchar(50) NOT NULL COMMENT 'cash_in, cash_out, payment, refund',
  `method` varchar(50) DEFAULT NULL COMMENT 'gcash, paymaya, bank_transfer, card',
  `amount` decimal(10,2) NOT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'pending' COMMENT 'pending, completed, failed, cancelled, expired',
  `description` text DEFAULT NULL,
  `transaction_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `xendit_invoice_id` varchar(255) DEFAULT NULL COMMENT 'Xendit invoice ID for tracking',
  `xendit_external_id` varchar(255) DEFAULT NULL COMMENT 'External ID sent to Xendit',
  `xendit_payment_method` varchar(100) DEFAULT NULL COMMENT 'Actual payment method used (from Xendit)',
  `xendit_payment_channel` varchar(100) DEFAULT NULL COMMENT 'Payment channel (from Xendit)',
  `xendit_paid_amount` decimal(10,2) DEFAULT NULL COMMENT 'Amount actually paid (from Xendit)',
  `xendit_fees` decimal(10,2) DEFAULT NULL COMMENT 'Xendit fees charged',
  `xendit_settlement_status` varchar(50) DEFAULT NULL COMMENT 'Settlement status from Xendit',
  `xendit_paid_at` datetime DEFAULT NULL COMMENT 'When payment was completed on Xendit',
  `xendit_expired_at` datetime DEFAULT NULL COMMENT 'When invoice expires',
  `reference_id` varchar(255) DEFAULT NULL COMMENT 'Reference for related transactions',
  `notes` text DEFAULT NULL COMMENT 'Additional notes or error messages',
  `created_by` int(11) DEFAULT NULL COMMENT 'Admin user who created (if manual)',
  `processed_by` varchar(100) DEFAULT NULL COMMENT 'System/webhook that processed the transaction',
  
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_type` (`type`),
  KEY `idx_xendit_invoice_id` (`xendit_invoice_id`),
  KEY `idx_transaction_date` (`transaction_date`),
  KEY `idx_user_status` (`user_id`, `status`),
  KEY `idx_user_type` (`user_id`, `type`),
  
  CONSTRAINT `fk_transactions_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_xendit_tracking` ON `transactions` (`xendit_invoice_id`, `status`);
CREATE INDEX IF NOT EXISTS `idx_pending_invoices` ON `transactions` (`status`, `xendit_invoice_id`) WHERE `status` = 'pending';
CREATE INDEX IF NOT EXISTS `idx_user_transactions` ON `transactions` (`user_id`, `transaction_date` DESC);

-- Sample data structure for reference
/*
Example records:

1. Cash-in transaction (pending):
INSERT INTO transactions VALUES (
  1,                                    -- id
  87,                                   -- user_id
  'cash_in',                           -- type
  'gcash',                             -- method
  100.00,                              -- amount
  'pending',                           -- status
  'HanApp Balance Cash-in ₱100 via GCash', -- description
  '2025-07-07 10:30:00',              -- transaction_date
  NULL,                                -- updated_at
  '686bad2fe0ca0a092cd7d720',         -- xendit_invoice_id
  'hanapp_cashin_87_1720345800',      -- xendit_external_id
  NULL,                                -- xendit_payment_method (filled when paid)
  NULL,                                -- xendit_payment_channel (filled when paid)
  NULL,                                -- xendit_paid_amount (filled when paid)
  NULL,                                -- xendit_fees (filled when paid)
  NULL,                                -- xendit_settlement_status (filled when paid)
  NULL,                                -- xendit_paid_at (filled when paid)
  '2025-07-08 10:30:00',              -- xendit_expired_at
  NULL,                                -- reference_id
  NULL,                                -- notes
  NULL,                                -- created_by
  'system'                             -- processed_by
);

2. Cash-in transaction (completed):
UPDATE transactions SET
  status = 'completed',
  updated_at = '2025-07-07 10:35:00',
  xendit_payment_method = 'EWALLET',
  xendit_payment_channel = 'GCASH',
  xendit_paid_amount = 100.00,
  xendit_fees = 3.50,
  xendit_settlement_status = 'PENDING',
  xendit_paid_at = '2025-07-07 10:34:30',
  processed_by = 'invoice_listener'
WHERE xendit_invoice_id = '686bad2fe0ca0a092cd7d720';

3. Cash-out transaction:
INSERT INTO transactions VALUES (
  2,                                    -- id
  87,                                   -- user_id
  'cash_out',                          -- type
  'bank_transfer',                     -- method
  500.00,                              -- amount
  'pending',                           -- status
  'Withdrawal to bank account',        -- description
  '2025-07-07 11:00:00',              -- transaction_date
  NULL,                                -- updated_at
  NULL,                                -- xendit_invoice_id (not applicable for withdrawals)
  'hanapp_withdrawal_87_1720347600',  -- xendit_external_id
  NULL,                                -- xendit_payment_method
  NULL,                                -- xendit_payment_channel
  NULL,                                -- xendit_paid_amount
  NULL,                                -- xendit_fees
  NULL,                                -- xendit_settlement_status
  NULL,                                -- xendit_paid_at
  NULL,                                -- xendit_expired_at
  NULL,                                -- reference_id
  'Pending bank transfer processing',  -- notes
  NULL,                                -- created_by
  'system'                             -- processed_by
);
*/
