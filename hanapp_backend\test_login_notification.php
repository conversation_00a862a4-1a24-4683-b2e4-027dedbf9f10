<?php
// Test script for the new login notification system
// This script simulates a login attempt that triggers an email notification

require_once 'api/config/db_connect.php';
require_once 'api/utils/email_sender.php';

header('Content-Type: application/json');

echo "<h1>Testing Login Notification System</h1>";

try {
    // First, create the necessary tables
    echo "<h2>1. Creating database tables...</h2>";
    
    // Create login_notifications table
    $createLoginNotificationsTable = "
        CREATE TABLE IF NOT EXISTS login_notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            device_info TEXT,
            ip_address VARCHAR(45),
            location VARCHAR(255),
            login_time DATETIME NOT NULL,
            confirmation_token VARCHAR(64) NOT NULL UNIQUE,
            status ENUM('pending', 'confirmed', 'denied') DEFAULT 'pending',
            confirmed_at DATETIME NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            expires_at DATETIME NOT NULL,
            
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user_id (user_id),
            INDEX idx_confirmation_token (confirmation_token),
            INDEX idx_status (status),
            INDEX idx_expires_at (expires_at)
        )
    ";
    
    if ($conn->query($createLoginNotificationsTable)) {
        echo "✅ login_notifications table created/verified<br>";
    } else {
        echo "❌ Error creating login_notifications table: " . $conn->error . "<br>";
    }
    
    // Test with a sample user (assuming user ID 1 exists)
    echo "<h2>2. Testing login notification...</h2>";
    
    // Check if we have any users
    $userCheck = $conn->query("SELECT id, full_name, email FROM users LIMIT 1");
    if ($userCheck->num_rows === 0) {
        echo "❌ No users found in database. Please create a user first.<br>";
        exit;
    }
    
    $testUser = $userCheck->fetch_assoc();
    echo "📧 Testing with user: {$testUser['full_name']} ({$testUser['email']})<br>";
    
    // Simulate login notification data
    $testData = [
        'user_id' => $testUser['id'],
        'device_info' => 'Test Device - Chrome Browser on Windows 11',
        'ip_address' => '*************',
        'location' => 'Test Location - Manila, Philippines',
        'login_time' => date('Y-m-d H:i:s')
    ];
    
    // Call the send login notification API
    $notificationUrl = 'http://localhost/hanapp3/hanapp_backend/api/send_login_notification.php';
    $postData = json_encode($testData);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $notificationUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    echo "<h3>API Response:</h3>";
    echo "HTTP Code: $httpCode<br>";
    
    if ($curlError) {
        echo "❌ cURL Error: $curlError<br>";
    } else {
        echo "Response: <pre>" . htmlspecialchars($response) . "</pre>";
        
        $responseData = json_decode($response, true);
        if ($responseData && $responseData['success']) {
            echo "✅ Login notification sent successfully!<br>";
            echo "📧 Confirmation token: {$responseData['confirmation_token']}<br>";
            
            // Generate test links
            $confirmUrl = "http://localhost/hanapp3/hanapp_backend/api/confirm_login.php?token={$responseData['confirmation_token']}";
            $denyUrl = "http://localhost/hanapp3/hanapp_backend/api/deny_login.php?token={$responseData['confirmation_token']}";
            
            echo "<h3>Test Links:</h3>";
            echo "<a href='$confirmUrl' target='_blank'>✅ Confirm Login</a><br>";
            echo "<a href='$denyUrl' target='_blank'>❌ Deny Login (Secure Account)</a><br>";
        } else {
            echo "❌ Login notification failed: " . ($responseData['message'] ?? 'Unknown error') . "<br>";
        }
    }
    
    // Check database records
    echo "<h2>3. Database Records:</h2>";
    $stmt = $conn->prepare("SELECT * FROM login_notifications WHERE user_id = ? ORDER BY created_at DESC LIMIT 5");
    $stmt->bind_param("i", $testUser['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Device Info</th><th>IP Address</th><th>Location</th><th>Status</th><th>Created At</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>" . htmlspecialchars($row['device_info']) . "</td>";
            echo "<td>{$row['ip_address']}</td>";
            echo "<td>" . htmlspecialchars($row['location']) . "</td>";
            echo "<td>{$row['status']}</td>";
            echo "<td>{$row['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No login notification records found.<br>";
    }
    
    echo "<h2>4. System Status:</h2>";
    echo "✅ Login notification system is ready!<br>";
    echo "📧 Email notifications will be sent when users log in from multiple devices<br>";
    echo "🔒 Users can confirm or deny login attempts via email links<br>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
}

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}
h1, h2, h3 {
    color: #333;
}
table {
    margin: 10px 0;
}
th, td {
    padding: 8px;
    text-align: left;
}
th {
    background-color: #f2f2f2;
}
a {
    display: inline-block;
    margin: 5px 10px 5px 0;
    padding: 10px 15px;
    background-color: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 5px;
}
a:hover {
    background-color: #0056b3;
}
</style>