<?php
// hanapp_backend/api/public_listing/get_listing_details.php
// Gets details for a specific public listing from listingsv2 table

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../../config/db_connect.php';

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode(["success" => false, "message" => "Only GET requests are allowed."]);
    exit();
}

// Get listing ID from query parameter
$listingId = $_GET['listing_id'] ?? null;

if (!$listingId || !is_numeric($listingId)) {
    echo json_encode(["success" => false, "message" => "Valid listing ID is required."]);
    exit();
}

// Query the listingsv2 table for the specific listing
$stmt = $conn->prepare("
    SELECT 
        id,
        lister_id,
        title,
        description,
        price,
        category,
        latitude,
        longitude,
        location_address,
        preferred_doer_gender,
        pictures_urls,
        doer_fee,
        transaction_fee,
        total_amount,
        payment_method,
        status,
        created_at,
        updated_at,
        is_active,
        tags,
        views,
        applicants
    FROM listingsv2 
    WHERE id = ?
");

if ($stmt === false) {
    error_log("Failed to prepare get listing details statement: " . $conn->error);
    echo json_encode(["success" => false, "message" => "Internal server error."]);
    exit();
}

$stmt->bind_param("i", $listingId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $stmt->close();
    echo json_encode(["success" => false, "message" => "Listing not found."]);
    exit();
}

$listing = $result->fetch_assoc();
$stmt->close();

// Parse pictures_urls JSON if it exists
if ($listing['pictures_urls']) {
    $picturesArray = json_decode($listing['pictures_urls'], true);
    $listing['pictures_urls'] = $picturesArray ?: [];
} else {
    $listing['pictures_urls'] = [];
}

// Ensure numeric fields are properly typed
$listing['id'] = intval($listing['id']);
$listing['lister_id'] = intval($listing['lister_id']);
$listing['price'] = $listing['price'] ? floatval($listing['price']) : null;
$listing['latitude'] = $listing['latitude'] ? floatval($listing['latitude']) : null;
$listing['longitude'] = $listing['longitude'] ? floatval($listing['longitude']) : null;
$listing['doer_fee'] = $listing['doer_fee'] ? floatval($listing['doer_fee']) : null;
$listing['transaction_fee'] = $listing['transaction_fee'] ? floatval($listing['transaction_fee']) : null;
$listing['total_amount'] = $listing['total_amount'] ? floatval($listing['total_amount']) : null;
$listing['is_active'] = intval($listing['is_active']) === 1;
$listing['views'] = intval($listing['views']);
$listing['applicants'] = $listing['applicants'] ? intval($listing['applicants']) : 0;

echo json_encode([
    "success" => true,
    "listing" => $listing,
    "message" => "Listing details retrieved successfully."
]);

$conn->close();
?>
