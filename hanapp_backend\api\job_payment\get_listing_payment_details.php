<?php
// hanapp_backend/api/job_payment/get_listing_payment_details.php
// Gets listing payment details for chat screen pay button - Hostinger compatible

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

require_once '../../config/db_connect.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // Get listing ID from query parameter
    $listingId = $_GET['listing_id'] ?? null;
    
    if (!$listingId) {
        throw new Exception('Missing listing ID parameter');
    }
    
    // First, try to get from asap_listings table (ASAP listings have doer_fee, total_amount, payment_method)
    $stmt = $conn->prepare("
        SELECT
            id,
            doer_fee,
            total_amount,
            payment_method,
            status,
            'ASAP' as listing_type
        FROM asap_listings
        WHERE id = ?
    ");
    $stmt->bind_param("i", $listingId);
    $stmt->execute();
    $result = $stmt->get_result();
    $listing = $result->fetch_assoc();
    $stmt->close();

    // If not found in asap_listings, try regular listings table
    if (!$listing) {
        $stmt = $conn->prepare("
            SELECT
                id,
                COALESCE(doer_fee, price) as doer_fee,
                COALESCE(total_amount, price) as total_amount,
                COALESCE(payment_method, 'tapp_balance') as payment_method,
                status,
                'REGULAR' as listing_type
            FROM listingsv2
            WHERE id = ?
        ");
        $stmt->bind_param("i", $listingId);
        $stmt->execute();
        $result = $stmt->get_result();
        $listing = $result->fetch_assoc();
        $stmt->close();
    }

    if (!$listing) {
        throw new Exception('Listing not found in both asap_listings and listingsv2 tables');
    }
    
    // Ensure numeric values are properly formatted
    $listing['doer_fee'] = floatval($listing['doer_fee'] ?? 0);
    $listing['total_amount'] = floatval($listing['total_amount'] ?? 0);

    // For ASAP listings, total_amount is already calculated and stored in database
    // For regular listings, check if we have stored total_amount, otherwise calculate it
    if ($listing['listing_type'] === 'REGULAR') {
        // If total_amount is not stored or is 0, calculate it based on payment method
        if ($listing['total_amount'] <= 0 && $listing['doer_fee'] > 0) {
            $serviceFee = 0;
            $doerFee = $listing['doer_fee'];

            switch (strtolower($listing['payment_method'])) {
                case 'gcash':
                    $serviceFee = ($doerFee * 0.023) + 20; // 2.3% + ₱20
                    break;
                case 'credit/debit card':
                    $serviceFee = ($doerFee * 0.032) + 30; // 3.2% + ₱30 (default Philippine)
                    break;
                case 'philippine_card':
                    $serviceFee = ($doerFee * 0.032) + 30; // 3.2% + ₱30 for Philippine cards
                    break;
                case 'international_card':
                    $serviceFee = ($doerFee * 0.042) + 30; // 4.2% + ₱30 for International cards
                    break;
                case 'bpi':
                case 'chinabank':
                case 'rcbc':
                case 'unionbank':
                case 'bank transfer':
                    $serviceFee = ($doerFee * 0.01) + 20; // 1% + ₱20
                    break;
                case 'hanapp_balance':
                case 'hanapp balance':
                case 'tapp_balance':
                case 'tapp balance':
                default:
                    $serviceFee = 20; // Fixed ₱20 fee for TAPP Balance
                    break;
            }

            $listing['total_amount'] = $doerFee + $serviceFee;
        }
        // If total_amount is already stored (from new public listings), use it directly
    }

    // For ASAP listings, use the total_amount directly from database (already includes service fees)
    
    // Add debug information
    $debugInfo = [
        'listing_type' => $listing['listing_type'],
        'doer_fee_from_db' => $listing['doer_fee'],
        'total_amount_from_db' => $listing['total_amount'],
        'payment_method' => $listing['payment_method']
    ];

    echo json_encode([
        'success' => true,
        'listing' => $listing,
        'debug' => $debugInfo,
        'message' => 'Listing payment details retrieved successfully'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
