<?php
// hanapp_backend/api/verification/get_badge_subscription_info.php
// Get user's badge subscription information

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Start output buffering to prevent any unexpected output
ob_start();

// Set up error handler to catch fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        ob_clean();
        http_response_code(500);
        echo json_encode([
            "success" => false,
            "message" => "Server error: " . $error['message'],
            "error_code" => "FATAL_ERROR"
        ]);
    }
});

// Test database connection first
try {
    require_once '../../config/db_connect.php';
    if (!isset($conn) || $conn->connect_error) {
        throw new Exception("Database connection failed: " . ($conn->connect_error ?? 'Unknown error'));
    }
} catch (Exception $e) {
    ob_clean();
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Database connection error: " . $e->getMessage(),
        "error_code" => "DB_CONNECTION_ERROR"
    ]);
    exit();
}

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    ob_clean();
    http_response_code(200);
    exit();
}

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("Only POST method is allowed.");
    }

    // Get and validate input
    $userId = $_POST['user_id'] ?? null;

    if (empty($userId) || !is_numeric($userId)) {
        throw new Exception("User ID is required and must be numeric.");
    }

    $userId = intval($userId);

    // Get user's badge subscription info
    $stmt = $conn->prepare("
        SELECT 
            id,
            full_name,
            email,
            badge_status,
            badge_acquired,
            badge_subscription_status,
            badge_subscription_start,
            badge_subscription_end,
            badge_next_payment_due,
            badge_last_payment_date,
            badge_payment_amount
        FROM users 
        WHERE id = ?
    ");

    if ($stmt === false) {
        throw new Exception("Failed to prepare user query statement.");
    }

    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        $stmt->close();
        throw new Exception("User not found.");
    }

    $user = $result->fetch_assoc();
    $stmt->close();

    // Check if subscription is expired
    $isExpired = false;
    $daysUntilExpiry = null;
    
    if ($user['badge_subscription_end']) {
        $expiryDate = strtotime($user['badge_subscription_end']);
        $currentDate = time();
        
        if ($expiryDate < $currentDate) {
            $isExpired = true;
            // Update user status if expired
            $updateStmt = $conn->prepare("
                UPDATE users 
                SET 
                    badge_subscription_status = 'expired',
                    badge_status = 'none',
                    badge_acquired = 0,
                    updated_at = NOW()
                WHERE id = ? AND badge_subscription_status = 'active'
            ");
            
            if ($updateStmt) {
                $updateStmt->bind_param("i", $userId);
                $updateStmt->execute();
                $updateStmt->close();
                
                // Update local data
                $user['badge_subscription_status'] = 'expired';
                $user['badge_status'] = 'none';
                $user['badge_acquired'] = 0;
            }
        } else {
            $daysUntilExpiry = ceil(($expiryDate - $currentDate) / (60 * 60 * 24));
        }
    }

    // Get recent payment history
    $paymentStmt = $conn->prepare("
        SELECT 
            id,
            amount,
            payment_method,
            status,
            created_at,
            paid_at
        FROM badge_payments 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 5
    ");

    $paymentHistory = [];
    if ($paymentStmt) {
        $paymentStmt->bind_param("i", $userId);
        $paymentStmt->execute();
        $paymentResult = $paymentStmt->get_result();
        
        while ($payment = $paymentResult->fetch_assoc()) {
            $paymentHistory[] = $payment;
        }
        
        $paymentStmt->close();
    }

    // Determine if user can subscribe
    $canSubscribe = ($user['badge_subscription_status'] !== 'active' || $isExpired);

    // Clean output buffer and send response
    ob_clean();
    echo json_encode([
        "success" => true,
        "message" => "Badge subscription info retrieved successfully",
        "data" => [
            "user_id" => $user['id'],
            "full_name" => $user['full_name'],
            "email" => $user['email'],
            "badge_status" => $user['badge_status'],
            "badge_acquired" => (bool)$user['badge_acquired'],
            "subscription_status" => $user['badge_subscription_status'],
            "subscription_start" => $user['badge_subscription_start'],
            "subscription_end" => $user['badge_subscription_end'],
            "next_payment_due" => $user['badge_next_payment_due'],
            "last_payment_date" => $user['badge_last_payment_date'],
            "monthly_amount" => (float)($user['badge_payment_amount'] ?? 1.00),
            "is_expired" => $isExpired,
            "days_until_expiry" => $daysUntilExpiry,
            "can_subscribe" => $canSubscribe,
            "payment_history" => $paymentHistory
        ]
    ]);

} catch (Exception $e) {
    ob_clean();
    http_response_code(400);
    echo json_encode([
        "success" => false,
        "message" => $e->getMessage(),
        "error_code" => "SUBSCRIPTION_INFO_ERROR"
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
