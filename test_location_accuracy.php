<?php
// Test script to verify enhanced location accuracy
// This script tests the new robust location handling functions

require_once 'hanapp_backend/api/log_login_history.php';

// Test coordinates for different locations in Philippines
$test_coordinates = [
    // Naic, Cavite
    ['lat' => 14.3167, 'lng' => 120.7667, 'expected' => 'Naic'],
    // General <PERSON>, C<PERSON><PERSON>  
    ['lat' => 14.3856, 'lng' => 120.8806, 'expected' => '<PERSON>'],
    // Mandaluyong, Metro Manila
    ['lat' => 14.5794, 'lng' => 121.0359, 'expected' => 'Mandaluyong'],
    // Makati, Metro Manila
    ['lat' => 14.5547, 'lng' => 121.0244, 'expected' => 'Makati'],
    // Quezon City, Metro Manila
    ['lat' => 14.6760, 'lng' => 121.0437, 'expected' => 'Quezon City']
];

echo "=== Enhanced Location Accuracy Test ===\n\n";

foreach ($test_coordinates as $index => $coord) {
    echo "Test " . ($index + 1) . ": {$coord['expected']} Area\n";
    echo "Coordinates: {$coord['lat']}, {$coord['lng']}\n";
    
    // Test the robust location function
    $location = getRobustLocationFromCoordinates($coord['lat'], $coord['lng']);
    
    echo "Result: {$location}\n";
    
    // Check if the expected location name is found in the result
    $accuracy_check = stripos($location, $coord['expected']) !== false ? '✓ ACCURATE' : '✗ NEEDS IMPROVEMENT';
    echo "Accuracy: {$accuracy_check}\n";
    
    echo "---\n\n";
    
    // Add delay to respect API rate limits
    sleep(1);
}

echo "=== Test Complete ===\n";
echo "Note: This test verifies that the enhanced geocoding provides more specific and accurate location names.\n";
echo "The system now uses multiple geocoding strategies for better reliability.\n";
?>