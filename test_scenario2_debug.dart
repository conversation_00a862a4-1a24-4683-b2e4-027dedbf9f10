import 'package:flutter/material.dart';
import 'package:hanapp/services/app_lifecycle_service.dart';

void main() {
  runApp(Scenario2DebugApp());
}

class Scenario2DebugApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Scenario 2 Debug',
      home: Scenario2DebugScreen(),
    );
  }
}

class Scenario2DebugScreen extends StatefulWidget {
  @override
  _Scenario2DebugScreenState createState() => _Scenario2DebugScreenState();
}

class _Scenario2DebugScreenState extends State<Scenario2DebugScreen> {
  String _status = 'Unknown';
  bool _isOnline = false;
  String _debugInfo = '';

  @override
  void initState() {
    super.initState();
    _initializeService();
  }

  Future<void> _initializeService() async {
    try {
      await AppLifecycleService.instance.initialize();
      setState(() {
        _isOnline = AppLifecycleService.instance.isOnline;
        _status = 'Service initialized';
      });
    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
    }
  }

  Future<void> _testScenario2() async {
    setState(() {
      _debugInfo = 'Starting Scenario 2 test...\n';
    });

    try {
      // Step 1: Set user to online
      setState(() {
        _debugInfo += 'Step 1: Setting user to online...\n';
      });
      await AppLifecycleService.instance.testUpdateStatus(true);
      setState(() {
        _isOnline = AppLifecycleService.instance.isOnline;
        _debugInfo += 'User is now: ${_isOnline ? "online" : "offline"}\n';
      });

      // Step 2: Simulate app going to background
      setState(() {
        _debugInfo += 'Step 2: Simulating app pause (background)...\n';
      });
      await AppLifecycleService.instance.handleAppLifecycleState(AppLifecycleState.paused);
      setState(() {
        _isOnline = AppLifecycleService.instance.isOnline;
        _debugInfo += 'After pause, user is: ${_isOnline ? "online" : "offline"}\n';
        _debugInfo += '_wasInBackground: ${AppLifecycleService.instance.wasInBackground}\n';
      });

      // Step 3: Simulate app resuming
      setState(() {
        _debugInfo += 'Step 3: Simulating app resume...\n';
      });
      await AppLifecycleService.instance.handleAppLifecycleState(AppLifecycleState.resumed);
      setState(() {
        _isOnline = AppLifecycleService.instance.isOnline;
        _debugInfo += 'After resume, user is: ${_isOnline ? "online" : "offline"}\n';
        _debugInfo += '_wasInBackground: ${AppLifecycleService.instance.wasInBackground}\n';
      });

      // Check the saved status
      final wasOnline = await AppLifecycleService.instance.getSavedOnlineStatus();
      setState(() {
        _debugInfo += 'Saved status before background: ${wasOnline ? "online" : "offline"}\n';
        _debugInfo += 'Test completed!\n';
      });

    } catch (e) {
      setState(() {
        _debugInfo += 'Error: $e\n';
      });
    }
  }

  Future<void> _resetTest() async {
    setState(() {
      _debugInfo = 'Test reset\n';
      _status = 'Ready for test';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Scenario 2 Debug'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Status:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text(_status),
                    SizedBox(height: 16),
                    Text(
                      'Online Status: ${_isOnline ? "Online" : "Offline"}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: _isOnline ? Colors.green : Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Test Controls:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _testScenario2,
                            child: Text('Test Scenario 2'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                            ),
                          ),
                        ),
                        SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _resetTest,
                            child: Text('Reset Test'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.grey,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            Expanded(
              child: Card(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Debug Info:',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            _debugInfo,
                            style: TextStyle(fontFamily: 'monospace', fontSize: 12),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 