-- Create password_reset_codes table for secure password resets
-- This table stores temporary tokens for password reset functionality

CREATE TABLE IF NOT EXISTS password_reset_codes_email (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(64) NOT NULL UNIQUE,
    expires_at DATETIME NOT NULL,
    used TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_token (token),
    INDEX idx_expires_at (expires_at),
    INDEX idx_used (used)
);

-- Optional: Clean up expired tokens (can be run periodically)
-- DELETE FROM password_reset_codes WHERE expires_at < NOW() OR used = 1;