<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - HanApp</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        .success-icon {
            font-size: 64px;
            color: #4CAF50;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        .amount {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
            margin: 20px 0;
        }
        .btn {
            background: #667eea;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-size: 14px;
            color: #666;
        }
        .instruction {
            margin-top: 30px;
            padding: 25px;
            background: #e8f5e8;
            border-radius: 12px;
            text-align: center;
            border: 2px solid #4CAF50;
        }
        .instruction h3 {
            margin: 0 0 15px 0;
            color: #2e7d32;
            font-size: 20px;
        }
        .instruction p {
            margin: 10px 0;
            font-size: 16px;
            color: #1b5e20;
            line-height: 1.5;
        }
        .instruction p:first-of-type {
            font-weight: bold;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">✅</div>
        <h1>Payment Successful!</h1>
        <p id="message">Your HanApp Verified Badge subscription has been activated!</p>

        <div class="info">
            <strong>What happens next?</strong><br>
            • Your verified badge will be activated within 1-2 minutes<br>
            • You'll receive an email confirmation<br>
            • Your profile will show the verified badge<br>
            • You'll get priority in search results
        </div>

        <div class="instruction">
            <h3>🎉 Your Verified Badge is Being Activated!</h3>
            <p><strong>Please return to the HanApp mobile application to see your verified badge.</strong></p>
            <p>Your badge status will be updated automatically within 1-2 minutes.</p>
            <p>You can close this page and go back to the app.</p>
        </div>
    </div>

    <script>
        // Simple script to show the page is loaded
        console.log('Badge payment success page loaded');
    </script>
</body>
</html>
