import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hanapp/services/app_lifecycle_service.dart';

void main() {
  group('Background Status Update Tests', () {
    test('should set user offline when app goes to background', () async {
      // This test verifies the logic flow
      final appLifecycleService = AppLifecycleService.instance;
      
      // Simulate app going to background
      await appLifecycleService.handleAppLifecycleState(AppLifecycleState.paused);
      
      // Verify that the service marks app as in background
      expect(appLifecycleService.wasInBackground, true);
    });

    test('should set user offline when app is hidden', () async {
      final appLifecycleService = AppLifecycleService.instance;
      
      // Simulate app being hidden
      await appLifecycleService.handleAppLifecycleState(AppLifecycleState.hidden);
      
      // Verify that the service marks app as in background
      expect(appLifecycleService.wasInBackground, true);
    });

    test('should restore status when app resumes', () async {
      final appLifecycleService = AppLifecycleService.instance;
      
      // First simulate app going to background
      await appLifecycleService.handleAppLifecycleState(AppLifecycleState.paused);
      expect(appLifecycleService.wasInBackground, true);
      
      // Then simulate app resuming
      await appLifecycleService.handleAppLifecycleState(AppLifecycleState.resumed);
      expect(appLifecycleService.wasInBackground, false);
    });
  });
}

// Manual test widget for background status updates
class BackgroundStatusTestWidget extends StatefulWidget {
  @override
  _BackgroundStatusTestWidgetState createState() => _BackgroundStatusTestWidgetState();
}

class _BackgroundStatusTestWidgetState extends State<BackgroundStatusTestWidget> with WidgetsBindingObserver {
  String _status = 'Unknown';
  bool _isOnline = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeService();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    AppLifecycleService.instance.handleAppLifecycleState(state);
    
    setState(() {
      switch (state) {
        case AppLifecycleState.resumed:
          _status = 'App Resumed';
          break;
        case AppLifecycleState.paused:
          _status = 'App Paused (Background)';
          break;
        case AppLifecycleState.hidden:
          _status = 'App Hidden (Background)';
          break;
        case AppLifecycleState.detached:
          _status = 'App Detached (Closed)';
          break;
        case AppLifecycleState.inactive:
          _status = 'App Inactive';
          break;
      }
    });
  }

  Future<void> _initializeService() async {
    try {
      await AppLifecycleService.instance.initialize();
      setState(() {
        _isOnline = AppLifecycleService.instance.isOnline;
        _status = 'Service initialized';
      });
    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Background Status Test'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Status:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text(_status),
                    SizedBox(height: 16),
                    Text(
                      'Online Status: ${_isOnline ? "Online" : "Offline"}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: _isOnline ? Colors.green : Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Instructions:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text('1. Start with app open (should be online)'),
                    Text('2. Press home button or switch apps (should go offline)'),
                    Text('3. Return to app (should go back online)'),
                    Text('4. Check database for status changes'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 