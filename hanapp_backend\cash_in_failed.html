<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cash-in Failed - HanApp</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        .error-icon {
            font-size: 64px;
            color: #f44336;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        .amount-display {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-size: 20px;
            font-weight: bold;
        }
        .breakdown {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: left;
        }
        .breakdown-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            font-size: 14px;
        }
        .breakdown-total {
            border-top: 1px solid #ddd;
            padding-top: 8px;
            margin-top: 8px;
            font-weight: bold;
        }
        .info {
            background: #ffebee;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #f44336;
            text-align: left;
        }
        .instruction {
            background: #f0f8ff;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #2196F3;
        }
        .instruction h3 {
            margin-top: 0;
            color: #1976D2;
        }
        .instruction p:first-of-type {
            font-weight: bold;
            font-size: 18px;
        }
        .retry-info {
            background: #fff3e0;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ff9800;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-icon">❌</div>
        <h1>Cash-in Failed</h1>
        <p id="message">Your cash-in payment could not be processed at this time.</p>

        <div class="amount-display">
            <div>Attempted Cash-in Amount</div>
            <div id="amount-attempted">₱0.00</div>
        </div>

        <div class="breakdown">
            <div class="breakdown-row">
                <span>Cash-in Amount:</span>
                <span id="base-amount">₱0.00</span>
            </div>
            <div class="breakdown-row">
                <span>Transaction Fee:</span>
                <span id="transaction-fee">₱20.00</span>
            </div>
            <div class="breakdown-row breakdown-total">
                <span>Total (Not Charged):</span>
                <span id="total-amount">₱0.00</span>
            </div>
        </div>

        <div class="info">
            <strong>What to do next?</strong><br>
            • Check your account balance<br>
            • Try a different payment method<br>
            • Ensure you have sufficient funds<br>
            • Contact support if the problem persists
        </div>

        <div class="retry-info">
            <strong>💡 Common Solutions:</strong><br>
            • Check your internet connection<br>
            • Verify your payment details<br>
            • Try using a different payment method<br>
            • Wait a few minutes and try again
        </div>

        <div class="instruction">
            <h3>❌ Payment Could Not Be Processed</h3>
            <p><strong>Please return to the HanApp mobile application to try again.</strong></p>
            <p>No money was charged to your account.</p>
            <p>You can close this page and go back to the app to retry the payment.</p>
        </div>
    </div>

    <script>
        // Get URL parameters to display transaction details
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        // Display transaction details if available
        window.onload = function() {
            const baseAmount = getUrlParameter('base_amount');
            const transactionFee = getUrlParameter('transaction_fee') || '20.00';
            const totalAmount = getUrlParameter('total_amount');
            const reason = getUrlParameter('reason');
            
            if (baseAmount) {
                document.getElementById('amount-attempted').textContent = '₱' + parseFloat(baseAmount).toFixed(2);
                document.getElementById('base-amount').textContent = '₱' + parseFloat(baseAmount).toFixed(2);
            }
            
            if (totalAmount) {
                document.getElementById('total-amount').textContent = '₱' + parseFloat(totalAmount).toFixed(2);
            }
            
            document.getElementById('transaction-fee').textContent = '₱' + parseFloat(transactionFee).toFixed(2);
            
            // Update message if reason is provided
            if (reason) {
                const messageElement = document.getElementById('message');
                switch(reason.toLowerCase()) {
                    case 'expired':
                        messageElement.textContent = 'Your payment session has expired. Please try again.';
                        break;
                    case 'cancelled':
                        messageElement.textContent = 'Payment was cancelled. You can try again anytime.';
                        break;
                    case 'insufficient_funds':
                        messageElement.textContent = 'Insufficient funds in your account. Please check your balance.';
                        break;
                    default:
                        messageElement.textContent = 'Your cash-in payment could not be processed at this time.';
                }
            }
        };

        // Simple script to show the page is loaded
        console.log('Cash-in failed page loaded');
    </script>
</body>
</html>
