<?php
// hanapp_backend/api/wallet/get_hanapp_transactions.php
// Separate endpoint to get HanApp Balance transaction history
// Filters for hanapp_balance_cashin transactions specifically

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Start output buffering to prevent any unexpected output
ob_start();

require_once '../../config/db_connect.php';

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    ob_clean();
    http_response_code(200);
    exit();
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    ob_clean();
    echo json_encode(["success" => false, "message" => "Only GET method is allowed."]);
    exit();
}

try {
    $userId = $_GET['user_id'] ?? null;
    $limit = $_GET['limit'] ?? 50; // Default limit of 50 transactions
    $offset = $_GET['offset'] ?? 0; // For pagination

    if (empty($userId) || !is_numeric($userId)) {
        ob_clean();
        echo json_encode(["success" => false, "message" => "User ID is required and must be numeric."]);
        exit();
    }

    // Validate limit and offset
    $limit = max(1, min(100, intval($limit))); // Between 1 and 100
    $offset = max(0, intval($offset));

    // Verify user exists
    $stmt_user = $conn->prepare("SELECT id FROM users WHERE id = ?");
    if ($stmt_user === false) {
        throw new Exception("Failed to prepare user verification statement: " . $conn->error);
    }

    $stmt_user->bind_param("i", $userId);
    $stmt_user->execute();
    $result_user = $stmt_user->get_result();

    if ($result_user->num_rows === 0) {
        $stmt_user->close();
        ob_clean();
        echo json_encode(["success" => false, "message" => "User not found."]);
        exit();
    }
    $stmt_user->close();

    // Get HanApp Balance transactions (both cash_in and cash_out)
    $stmt = $conn->prepare("
        SELECT
            id,
            user_id,
            type,
            method,
            amount,
            status,
            description,
            transaction_date,
            xendit_invoice_id
        FROM transactions
        WHERE user_id = ? AND type IN ('cash_in', 'cash_out')
        ORDER BY transaction_date DESC
        LIMIT ? OFFSET ?
    ");

    if ($stmt === false) {
        throw new Exception("Failed to prepare transaction statement: " . $conn->error);
    }

    $stmt->bind_param("iii", $userId, $limit, $offset);
    $stmt->execute();
    $result = $stmt->get_result();

    $transactions = [];
    while ($row = $result->fetch_assoc()) {
        $transactions[] = [
            'id' => intval($row['id']),
            'user_id' => intval($row['user_id']),
            'type' => $row['type'],
            'method' => $row['method'],
            'amount' => floatval($row['amount']),
            'status' => $row['status'],
            'description' => $row['description'],
            'transaction_date' => $row['transaction_date'],
            'xendit_invoice_id' => $row['xendit_invoice_id'],
            'formatted_amount' => '₱' . number_format(floatval($row['amount']), 2),
            'formatted_date' => date('M j, Y g:i A', strtotime($row['transaction_date']))
        ];
    }

    $stmt->close();

    // Get total count for pagination
    $stmt_count = $conn->prepare("
        SELECT COUNT(*) as total
        FROM transactions
        WHERE user_id = ? AND type IN ('cash_in', 'cash_out')
    ");

    if ($stmt_count === false) {
        throw new Exception("Failed to prepare count statement: " . $conn->error);
    }

    $stmt_count->bind_param("i", $userId);
    $stmt_count->execute();
    $result_count = $stmt_count->get_result();
    $count_row = $result_count->fetch_assoc();
    $total_transactions = intval($count_row['total']);
    $stmt_count->close();

    ob_clean();
    echo json_encode([
        "success" => true,
        "transactions" => $transactions,
        "total_count" => $total_transactions,
        "limit" => $limit,
        "offset" => $offset,
        "has_more" => ($offset + $limit) < $total_transactions,
        "message" => "HanApp Balance transactions retrieved successfully."
    ]);

} catch (Exception $e) {
    error_log("get_hanapp_transactions.php: Exception occurred: " . $e->getMessage());
    ob_clean();
    echo json_encode([
        "success" => false,
        "message" => "An error occurred while retrieving transactions.",
        "error" => $e->getMessage()
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
