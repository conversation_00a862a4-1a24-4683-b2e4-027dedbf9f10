<?php
// hanapp_backend/api/send_login_notification.php
// Sends email notification when a new device login is detected

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'config/db_connect.php';
require_once 'utils/email_sender.php';

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $input = file_get_contents("php://input");
    $data = json_decode($input, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("Invalid JSON payload.");
    }

    $userId = $data['user_id'] ?? null;
    $deviceInfo = $data['device_info'] ?? 'Unknown Device';
    $ipAddress = $data['ip_address'] ?? 'Unknown IP';
    $location = $data['location'] ?? 'Unknown Location';
    $loginTime = $data['login_time'] ?? date('Y-m-d H:i:s');
    $confirmationToken = $data['confirmation_token'] ?? null;

    if (empty($userId)) {
        echo json_encode([
            "success" => false, 
            "message" => "User ID is required.",
            "error_type" => "missing_user_id"
        ]);
        exit();
    }

    // Get user information
    $stmt = $conn->prepare("SELECT id, full_name, email FROM users WHERE id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode([
            "success" => false, 
            "message" => "User not found.",
            "error_type" => "user_not_found"
        ]);
        exit();
    }
    
    $user = $result->fetch_assoc();
    $stmt->close();

    // Generate confirmation token if not provided
    if (!$confirmationToken) {
        $confirmationToken = bin2hex(random_bytes(32));
    }

    // Store login notification record
    $insertStmt = $conn->prepare("
        INSERT INTO login_notifications (user_id, device_info, ip_address, location, login_time, confirmation_token, status, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, 'pending', NOW())
    ");
    $insertStmt->bind_param("isssss", $userId, $deviceInfo, $ipAddress, $location, $loginTime, $confirmationToken);
    
    if (!$insertStmt->execute()) {
        throw new Exception("Failed to store login notification record.");
    }
    $insertStmt->close();

    // Format login time for display
    $formattedTime = date('F j, Y \\a\\t g:i A', strtotime($loginTime));
    
    // Prepare email content
    $emailSubject = 'New Device Login Detected - TAPP';
    $confirmUrl = "https://autosell.io//api/confirm_login.php?token=$confirmationToken";
    $denyUrl = "https://autosell.io//api/deny_login.php?token=$confirmationToken";
    
    $emailBodyHtml = '
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; background-color: #f4f4f4; margin: 0; padding: 20px; }
                .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
                .header { text-align: center; margin-bottom: 30px; }
                .logo { font-size: 24px; font-weight: bold; color: #141CC9; }
                .alert-box { background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }
                .info-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                .info-table td { padding: 10px; border-bottom: 1px solid #eee; }
                .info-table td:first-child { font-weight: bold; width: 30%; }
                .button-container { text-align: center; margin: 30px 0; }
                .btn { display: inline-block; padding: 12px 30px; margin: 0 10px; text-decoration: none; border-radius: 5px; font-weight: bold; }
                .btn-confirm { background-color: #28a745; color: white; }
                .btn-deny { background-color: #dc3545; color: white; }
                .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="logo">TAPP</div>
                    <h2>New Device Login Detected</h2>
                </div>
                
                <div class="alert-box">
                    <strong>Security Alert:</strong> We detected a login to your TAPP account from a new device.
                </div>
                
                <p>Hello ' . htmlspecialchars($user['full_name']) . ',</p>
                
                <p>We noticed a login to your TAPP account from a device we haven\'t seen before. If this was you, you can safely ignore this email. If this wasn\'t you, please take action immediately.</p>
                
                <table class="info-table">
                    <tr>
                        <td>Device:</td>
                        <td>' . htmlspecialchars($deviceInfo) . '</td>
                    </tr>
                    <tr>
                        <td>IP Address:</td>
                        <td>' . htmlspecialchars($ipAddress) . '</td>
                    </tr>
                    <tr>
                        <td>Location:</td>
                        <td>' . htmlspecialchars($location) . '</td>
                    </tr>
                    <tr>
                        <td>Time:</td>
                        <td>' . $formattedTime . '</td>
                    </tr>
                </table>
                
                <div class="button-container">
                    <a href="' . $confirmUrl . '" class="btn btn-confirm">Yes, this was me</a>
                    <a href="' . $denyUrl . '" class="btn btn-deny">No, secure my account</a>
                </div>
                
                <p><strong>What happens next?</strong></p>
                <ul>
                    <li>If you click "Yes, this was me" - No further action needed</li>
                    <li>If you click "No, secure my account" - We\'ll immediately log out all devices and require you to change your password</li>
                </ul>
                
                <p>If you didn\'t click either button within 24 hours, we\'ll automatically secure your account as a precaution.</p>
                
                <div class="footer">
                    <p>This is an automated security notification from TAPP.</p>
                    <p>If you have any questions, please contact our support team.</p>
                    <p>&copy; ' . date('Y') . ' TAPP. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
    ';
    
    $emailBodyText = "TAPP Security Alert\n\n" .
                     "Hello {$user['full_name']},\n\n" .
                     "We detected a login to your TAPP account from a new device:\n\n" .
                     "Device: $deviceInfo\n" .
                     "IP Address: $ipAddress\n" .
                     "Location: $location\n" .
                     "Time: $formattedTime\n\n" .
                     "If this was you, click here to confirm: $confirmUrl\n\n" .
                     "If this wasn't you, click here to secure your account: $denyUrl\n\n" .
                     "If you don't respond within 24 hours, we'll automatically secure your account.\n\n" .
                     "Best regards,\nTAPP Security Team";

    // Send email notification
    $mailResult = sendEmailViaSendGrid(
        $user['email'], 
        $user['full_name'], 
        $emailSubject, 
        $emailBodyHtml, 
        $emailBodyText
    );

    if ($mailResult['success']) {
        echo json_encode([
            "success" => true, 
            "message" => "Login notification email sent successfully.",
            "confirmation_token" => $confirmationToken
        ]);
    } else {
        echo json_encode([
            "success" => false, 
            "message" => "Failed to send login notification email: " . $mailResult['message']
        ]);
    }

} catch (Exception $e) {
    error_log("Login notification error: " . $e->getMessage());
    echo json_encode([
        "success" => false, 
        "message" => "Server error occurred: " . $e->getMessage()
    ]);
}

$conn->close();
?>