// lib/utils/cache_cleaner.dart
// Utility to clean corrupted cache data

import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class CacheCleaner {
  static const String _userKey = 'user_data';
  
  /// Clear all user-related cache data
  static Future<void> clearUserCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Clear user data
      await prefs.remove(_userKey);
      
      // Clear any other user-related keys
      final keys = prefs.getKeys();
      for (String key in keys) {
        if (key.startsWith('user_') || 
            key.startsWith('auth_') || 
            key.startsWith('verification_')) {
          await prefs.remove(key);
        }
      }
      
      print('CacheCleaner: All user cache data cleared successfully');
    } catch (e) {
      print('CacheCleaner: Error clearing cache: $e');
    }
  }
  
  /// Check if user cache data is corrupted
  static Future<bool> isUserCacheCorrupted() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userData = prefs.getString(_userKey);
      
      if (userData == null) return false;
      
      // Try to parse the cached data
      final jsonData = jsonDecode(userData);
      
      // Check for common corruption indicators
      if (jsonData['verification_status'] != null && 
          jsonData['verification_status'] is int) {
        print('CacheCleaner: Found corrupted verification_status (int instead of string)');
        return true;
      }
      
      if (jsonData['badge_status'] != null && 
          jsonData['badge_status'] is int) {
        print('CacheCleaner: Found corrupted badge_status (int instead of string)');
        return true;
      }
      
      return false;
    } catch (e) {
      print('CacheCleaner: Error checking cache corruption: $e');
      return true; // If we can't parse it, consider it corrupted
    }
  }
  
  /// Clean corrupted cache if detected
  static Future<void> cleanIfCorrupted() async {
    if (await isUserCacheCorrupted()) {
      print('CacheCleaner: Corrupted cache detected, cleaning...');
      await clearUserCache();
    }
  }
}
