<?php
// hanapp_backend/api/job_payment/process_hanapp_balance_payment.php
// Processes TAPP Balance payments for job completion - Hostinger compatible

ini_set('display_errors', 0); // Disable display_errors to prevent HTML output in JSON responses
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);
ini_set('log_errors', 1);

// Force immediate log to test if logging works
error_log("=== PROCESS TAPP BALANCE PAYMENT FILE LOADED ===");
error_log("Current time: " . date('Y-m-d H:i:s'));
error_log("File path: " . __FILE__);

require_once '../../config/db_connect.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Test output to verify file execution
error_log("=== HEADERS SET, PROCESSING REQUEST ===");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    error_log("OPTIONS request received, exiting");
    exit(0);
}

try {
    // Get JSON input
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    error_log("=== TAPP Balance Payment Processing Started ===");
    error_log("File being executed: " . __FILE__);
    error_log("Request URI: " . $_SERVER['REQUEST_URI']);
    error_log("Request method: " . $_SERVER['REQUEST_METHOD']);
    error_log("Raw input: " . $rawInput);
    error_log("Parsed input: " . json_encode($input));

    if (!$input) {
        error_log("ERROR: Invalid JSON input");
        throw new Exception('Invalid JSON input');
    }

    $applicationId = $input['application_id'] ?? null;
    $listerId = $input['lister_id'] ?? null;
    $doerFee = $input['doer_fee'] ?? null;
    $totalAmount = $input['total_amount'] ?? null;

    error_log("Parameters extracted:");
    error_log("- Application ID: $applicationId");
    error_log("- Lister ID: $listerId");
    error_log("- Doer Fee: $doerFee");
    error_log("- Total Amount: $totalAmount");

    if (!$applicationId || !$listerId || !$doerFee || !$totalAmount) {
        error_log("ERROR: Missing required parameters");
        throw new Exception('Missing required parameters');
    }
    
    // Start transaction
    error_log("Starting database transaction...");
    $conn->autocommit(false);

    // Get application details to find doer_id (using correct table: applicationsv2)
    error_log("Preparing application query...");
    $query = "
        SELECT a.id, a.doer_id, a.lister_id, a.listing_id, a.listing_type, a.status,
               COALESCE(pl.title, al.title) as listing_title,
               COALESCE(pl.price, al.price) as listing_price,
               u.full_name as doer_name, u.email as doer_email
        FROM applicationsv2 a
        LEFT JOIN listingsv2 pl ON a.listing_id = pl.id AND a.listing_type = 'PUBLIC'
        LEFT JOIN asap_listings al ON a.listing_id = al.id AND a.listing_type = 'ASAP'
        JOIN users u ON a.doer_id = u.id
        WHERE a.id = ? AND a.lister_id = ? AND a.status = 'accepted'
    ";

    error_log("SQL Query: " . $query);
    error_log("Query parameters: applicationId=$applicationId, listerId=$listerId");

    $stmt = $conn->prepare($query);
    if ($stmt === false) {
        error_log("ERROR: Failed to prepare statement: " . $conn->error);
        throw new Exception('Failed to prepare application query: ' . $conn->error);
    }

    error_log("Binding parameters and executing query...");
    $stmt->bind_param("ii", $applicationId, $listerId);

    if (!$stmt->execute()) {
        error_log("ERROR: Failed to execute query: " . $stmt->error);
        throw new Exception('Failed to execute application query: ' . $stmt->error);
    }

    $result = $stmt->get_result();
    $application = $result->fetch_assoc();
    $stmt->close();

    error_log("Query executed successfully. Result: " . json_encode($application));

    if (!$application) {
        error_log("ERROR: Application not found or not accepted");
        throw new Exception('Application not found or not accepted');
    }

    $doerId = $application['doer_id'];
    $listingId = $application['listing_id'];
    $listingType = $application['listing_type'];
    $listingTitle = $application['listing_title'];
    $doerName = $application['doer_name'];

    error_log("Application details extracted:");
    error_log("- Doer ID: $doerId");
    error_log("- Listing ID: $listingId");
    error_log("- Listing Type: $listingType");
    error_log("- Listing Title: '$listingTitle' (type: " . gettype($listingTitle) . ")");
    error_log("- Doer Name: $doerName");

    // Also write to a custom debug file for easier access
    file_put_contents(__DIR__ . '/tapp_balance_debug.log',
        date('Y-m-d H:i:s') . " - TAPP Balance Payment Debug\n" .
        "Application ID: $applicationId\n" .
        "Listing Title Retrieved: '$listingTitle' (type: " . gettype($listingTitle) . ")\n" .
        "Listing ID: $listingId, Type: $listingType\n" .
        "Raw Application Data: " . json_encode($application) . "\n\n",
        FILE_APPEND | LOCK_EX
    );
    
    // Check lister's TAPP Balance (using correct column: balance)
    error_log("Checking lister's TAPP Balance...");
    $stmt = $conn->prepare("SELECT balance FROM users WHERE id = ?");
    if ($stmt === false) {
        error_log("ERROR: Failed to prepare lister balance query: " . $conn->error);
        throw new Exception('Failed to prepare lister balance query: ' . $conn->error);
    }

    $stmt->bind_param("i", $listerId);
    if (!$stmt->execute()) {
        error_log("ERROR: Failed to execute lister balance query: " . $stmt->error);
        throw new Exception('Failed to execute lister balance query: ' . $stmt->error);
    }

    $result = $stmt->get_result();
    $lister = $result->fetch_assoc();
    $stmt->close();

    error_log("Lister balance query result: " . json_encode($lister));

    if (!$lister) {
        error_log("ERROR: Lister not found");
        throw new Exception('Lister not found');
    }

    $currentBalance = floatval($lister['balance']);
    error_log("Current lister balance: ₱" . number_format($currentBalance, 2));
    error_log("Required total amount: ₱" . number_format($totalAmount, 2));

    if ($currentBalance < $totalAmount) {
        error_log("ERROR: Insufficient balance");
        throw new Exception('Insufficient TAPP Balance. Current balance: ₱' . number_format($currentBalance, 2));
    }
    
    // Deduct total amount from lister's balance (using correct column: balance)
    $newListerBalance = $currentBalance - $totalAmount;
    $stmt = $conn->prepare("UPDATE users SET balance = ? WHERE id = ?");
    $stmt->bind_param("di", $newListerBalance, $listerId);
    $stmt->execute();
    $stmt->close();
    
    // Get doer's name for logging (don't update total_profit yet - will be updated on completion)
    $stmt = $conn->prepare("SELECT full_name FROM users WHERE id = ?");
    $stmt->bind_param("i", $doerId);
    $stmt->execute();
    $result = $stmt->get_result();
    $doerInfo = $result->fetch_assoc();
    $stmt->close();

    $doerName = $doerInfo['full_name'] ?? 'Unknown';
    error_log("Doer info: ID=$doerId, Name=$doerName");
    error_log("Doer fee of ₱$doerFee will be added to total_profit when lister confirms completion");
    
    // Update application status to in_progress (payment confirmed, project starts)
    $stmt = $conn->prepare("UPDATE applicationsv2 SET status = 'in_progress', payment_confirmed = 1, payment_confirmed_at = CURRENT_TIMESTAMP, project_start_date = CURRENT_TIMESTAMP WHERE id = ?");
    $stmt->bind_param("i", $applicationId);
    $stmt->execute();
    $stmt->close();

    // Don't update listing status - keep it active until project is actually completed by doer
    
    // Create transaction record for lister (deduction)
    $stmt = $conn->prepare("
        INSERT INTO transactions (user_id, type, amount, description, transaction_date, method, status) 
        VALUES (?, 'debit', ?, ?, NOW(), 'balance', 'completed')
    ");
    $description = "Job payment for application #$applicationId";
    $stmt->bind_param("ids", $listerId, $totalAmount, $description);
    $stmt->execute();
    $stmt->close();
    
    // Create transaction record for doer (earning)
    $stmt = $conn->prepare("
        INSERT INTO transactions (user_id, type, amount, description, transaction_date, method, status) 
        VALUES (?, 'credit', ?, ?, NOW(), 'job_payment', 'completed')
    ");
    $description = "Job earning from application #$applicationId";
    $stmt->bind_param("ids", $doerId, $doerFee, $description);
    $stmt->execute();
    $stmt->close();

    // Create notification for doer (project started after payment) with chat navigation
    $stmt = $conn->prepare("
        INSERT INTO doer_notifications (
            user_id, sender_id, type, title, content, associated_id,
            conversation_id_for_chat_nav, conversation_lister_id, conversation_doer_id,
            listing_id, listing_type, lister_id, lister_name, is_read, created_at
        ) VALUES (?, ?, 'project_started', 'Project Started', ?, ?, ?, ?, ?, ?, ?, ?, ?, 0, CURRENT_TIMESTAMP)
    ");

    if ($stmt !== false) {
        $notificationContent = "Payment confirmed! The project has started. You will receive ₱" . number_format($doerFee, 2) . " when the lister confirms completion. Good luck!";

        // Get lister's full name for the notification
        $getListerNameStmt = $conn->prepare("SELECT full_name FROM users WHERE id = ?");
        $listerFullName = 'Lister'; // Default fallback
        if ($getListerNameStmt !== false) {
            $getListerNameStmt->bind_param("i", $listerId);
            $getListerNameStmt->execute();
            $listerNameResult = $getListerNameStmt->get_result();
            if ($listerNameResult->num_rows > 0) {
                $listerFullName = $listerNameResult->fetch_assoc()['full_name'];
            }
            $getListerNameStmt->close();
        }

        // We'll get the conversation ID in the next section, so let's set it to null for now
        // and update it after we find the conversation
        $conversationIdForNotification = null;

        error_log("Creating notification with listing title: '$listingTitle' (type: " . gettype($listingTitle) . ")");

        $stmt->bind_param("iisiiiisiss",
            $doerId,                    // user_id
            $listerId,                  // sender_id (lister who made the payment)
            $notificationContent,       // content
            $applicationId,             // associated_id
            $conversationIdForNotification, // conversation_id_for_chat_nav (will update later)
            $listerId,                  // conversation_lister_id
            $doerId,                    // conversation_doer_id
            $listingId,                 // listing_id
            $listingType,               // listing_type
            $listerId,                  // lister_id
            $listerFullName             // lister_name
        );

        $stmt->execute();
        $notificationId = $conn->insert_id;
        $stmt->close();
        error_log("Doer notification created successfully with ID: $notificationId");
    } else {
        error_log("Failed to prepare doer notification statement");
    }

    // Send system message to conversation indicating project has started
    error_log("=== SENDING SYSTEM MESSAGE TO CONVERSATION ===");

    // Get conversation ID for this application
    $getConvStmt = $conn->prepare("
        SELECT id FROM conversationsv2
        WHERE listing_id = ? AND listing_type = ? AND lister_id = ? AND doer_id = ?
    ");

    if ($getConvStmt !== false) {
        $getConvStmt->bind_param("isii", $listingId, $listingType, $listerId, $doerId);
        $getConvStmt->execute();
        $convResult = $getConvStmt->get_result();

        if ($convResult->num_rows > 0) {
            $conversationId = $convResult->fetch_assoc()['id'];
            $getConvStmt->close();

            // Update the notification with the conversation ID for chat navigation
            if (isset($notificationId)) {
                $updateNotificationStmt = $conn->prepare("
                    UPDATE doer_notifications
                    SET conversation_id_for_chat_nav = ?
                    WHERE id = ?
                ");
                if ($updateNotificationStmt !== false) {
                    $updateNotificationStmt->bind_param("ii", $conversationId, $notificationId);
                    $updateNotificationStmt->execute();
                    $updateNotificationStmt->close();
                    error_log("Updated notification $notificationId with conversation ID $conversationId for chat navigation");
                }
            }

            // Send system message (we already have lister's full name from notification creation)
            $systemMessageContent = "$listerFullName started the project.";
            $sendMessageStmt = $conn->prepare("
                INSERT INTO messagesv2 (conversation_id, sender_id, receiver_id, content, sent_at, type)
                VALUES (?, ?, ?, ?, NOW(), 'system')
            ");

            if ($sendMessageStmt !== false) {
                $sendMessageStmt->bind_param("iiis", $conversationId, $listerId, $doerId, $systemMessageContent);

                if ($sendMessageStmt->execute()) {
                    error_log("TAPP Balance Payment: System message sent successfully to conversation $conversationId");
                } else {
                    error_log("TAPP Balance Payment: Failed to send system message: " . $sendMessageStmt->error);
                }
                $sendMessageStmt->close();
            } else {
                error_log("TAPP Balance Payment: Failed to prepare system message statement");
            }
        } else {
            $getConvStmt->close();
            error_log("TAPP Balance Payment: Conversation not found for listing_id: $listingId, listing_type: $listingType, lister_id: $listerId, doer_id: $doerId");
        }
    } else {
        error_log("TAPP Balance Payment: Failed to prepare conversation lookup statement");
    }

    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    error_log("=== PAYMENT PROCESSING COMPLETED SUCCESSFULLY ===");
    error_log("New lister balance: $newListerBalance");
    error_log("Doer fee reserved for completion: $doerFee");
    error_log("Total amount processed: $totalAmount");

    echo json_encode([
        'success' => true,
        'message' => 'Payment processed successfully',
        'new_balance' => $newListerBalance,
        'doer_earning' => $doerFee,
        'debug_file_executed' => 'api/job_payment/process_hanapp_balance_payment.php',
        'debug_info' => [
            'lister_balance_before' => $currentBalance,
            'lister_balance_after' => $newListerBalance,
            'doer_fee_reserved' => $doerFee,
            'total_amount_deducted' => $totalAmount,
            'doer_id' => $doerId,
            'doer_name' => $doerName,
            'payment_confirmed' => true,
            'profit_update_on_completion' => true
        ]
    ]);
    
} catch (Exception $e) {
    // Rollback transaction on error
    $conn->rollback();
    $conn->autocommit(true);

    error_log("=== PAYMENT PROCESSING ERROR ===");
    error_log("Error message: " . $e->getMessage());
    error_log("Error file: " . $e->getFile());
    error_log("Error line: " . $e->getLine());
    error_log("Stack trace: " . $e->getTraceAsString());
    error_log("=== END ERROR LOG ===");

    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug_info' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ],
        'debug_file_executed' => 'api/job_payment/process_hanapp_balance_payment.php'
    ]);
}
?>
