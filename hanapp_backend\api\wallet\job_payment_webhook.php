<?php
// hanapp_backend/api/wallet/job_payment_webhook.php
// Handles Xendit webhook notifications for job payments
// Updates doer's total_profit and records platform transaction fees

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

require_once '../../config/db_connect.php';

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, X-Callback-Token");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(["error" => "Method not allowed"]);
    exit();
}

try {
    // Get webhook payload
    $payload = file_get_contents('php://input');
    $webhookData = json_decode($payload, true);

    if (!$webhookData) {
        throw new Exception("Invalid webhook payload");
    }

    // Log webhook for debugging
    error_log("Job Payment Webhook Received: " . $payload);

    // Verify webhook signature (optional but recommended for production)
    $xenditWebhookToken = 'ibwb3mKaoLK41jzM8145jUOQSAy9714RFRATVW270sAr'; // From Xendit dashboard
    $receivedSignature = $_SERVER['HTTP_X_CALLBACK_TOKEN'] ?? '';

    // Optional: Enable signature verification for production
    // if ($receivedSignature !== $xenditWebhookToken) {
    //     throw new Exception("Invalid webhook signature");
    // }

    error_log("Job Payment Webhook signature received: " . $receivedSignature);

    // Extract invoice data
    $invoiceId = $webhookData['id'] ?? null;
    $externalId = $webhookData['external_id'] ?? null;
    $status = $webhookData['status'] ?? null;
    $paidAmount = $webhookData['paid_amount'] ?? null;
    $paidAt = $webhookData['paid_at'] ?? null;
    $paymentMethod = $webhookData['payment_method'] ?? null;
    $paymentChannel = $webhookData['payment_channel'] ?? null;
    $fees = $webhookData['fees'] ?? null;

    if (!$invoiceId || !$externalId) {
        throw new Exception("Missing required webhook data");
    }

    // Find the job payment transaction in our database
    $stmt = $conn->prepare("
        SELECT id, user_id, application_id, doer_id, doer_fee, transaction_fee, total_amount, status 
        FROM job_payment_transactions 
        WHERE xendit_invoice_id = ? AND xendit_external_id = ?
    ");

    if ($stmt === false) {
        throw new Exception("Failed to prepare transaction lookup statement");
    }

    $stmt->bind_param("ss", $invoiceId, $externalId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        $stmt->close();
        throw new Exception("Transaction not found for invoice: $invoiceId");
    }

    $transaction = $result->fetch_assoc();
    $stmt->close();

    $transactionId = $transaction['id'];
    $listerId = $transaction['user_id']; // The lister who is paying
    $applicationId = $transaction['application_id'];
    $doerId = $transaction['doer_id'];
    $doerFee = floatval($transaction['doer_fee']);
    $transactionFee = floatval($transaction['transaction_fee']);
    $totalAmount = floatval($transaction['total_amount']);
    $currentStatus = $transaction['status'];

    error_log("Job Payment Transaction found: ID $transactionId, Lister: $listerId, Doer: $doerId, Doer Fee: $doerFee, Platform Fee: $transactionFee");

    // Process based on webhook status
    switch (strtoupper($status)) {
        case 'PAID':
            if ($currentStatus === 'completed') {
                // Already processed, return success
                ob_clean();
                echo json_encode(["status" => "ok", "message" => "Already processed"]);
                exit();
            }

            // Start transaction for atomic operations
            $conn->autocommit(false);

            try {
                // 1. Update job payment transaction status
                $stmt = $conn->prepare("
                    UPDATE job_payment_transactions
                    SET status = 'completed',
                        paid_at = ?,
                        payment_method = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ");

                if ($stmt === false) {
                    throw new Exception("Failed to prepare job payment transaction update statement");
                }

                $stmt->bind_param("ssi", $paidAt, $paymentMethod, $transactionId);

                if (!$stmt->execute()) {
                    throw new Exception("Failed to update job payment transaction: " . $stmt->error);
                }
                $stmt->close();

                // 2. Note: Doer's total_profit will be updated when lister confirms completion
                error_log("Doer fee of ₱$doerFee will be added to doer $doerId's total_profit when lister confirms completion");

                // 2. Record platform revenue (transaction fee)
                $stmt = $conn->prepare("
                    INSERT INTO platform_revenue (
                        transaction_type, reference_id, amount, source, 
                        description, created_at
                    ) VALUES (
                        'job_payment_fee', ?, ?, 'job_payment',
                        ?, CURRENT_TIMESTAMP
                    )
                ");

                if ($stmt === false) {
                    throw new Exception("Failed to prepare platform revenue statement");
                }

                $description = "Platform fee from job payment - Application ID: $applicationId";
                $stmt->bind_param("ids", $transactionId, $transactionFee, $description);

                if (!$stmt->execute()) {
                    throw new Exception("Failed to record platform revenue: " . $stmt->error);
                }
                $stmt->close();

                // 3. Update application status to in_progress (payment confirmed, project starts)
                $stmt = $conn->prepare("
                    UPDATE applicationsv2
                    SET status = 'in_progress',
                        payment_confirmed = 1,
                        payment_confirmed_at = CURRENT_TIMESTAMP,
                        project_start_date = CURRENT_TIMESTAMP,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ");

                if ($stmt === false) {
                    throw new Exception("Failed to prepare application update statement");
                }

                $stmt->bind_param("i", $applicationId);

                if (!$stmt->execute()) {
                    throw new Exception("Failed to update application status: " . $stmt->error);
                }
                $stmt->close();

                // 4. Get application details for notification
                $getAppStmt = $conn->prepare("
                    SELECT a.listing_id, a.listing_type, COALESCE(pl.title, al.title) as listing_title
                    FROM applicationsv2 a
                    LEFT JOIN listingsv2 pl ON a.listing_id = pl.id AND a.listing_type = 'PUBLIC'
                    LEFT JOIN asap_listings al ON a.listing_id = al.id AND a.listing_type = 'ASAP'
                    WHERE a.id = ?
                ");

                $listingId = null;
                $listingType = null;
                $listingTitle = 'Project';

                if ($getAppStmt !== false) {
                    $getAppStmt->bind_param("i", $applicationId);
                    $getAppStmt->execute();
                    $appResult = $getAppStmt->get_result();
                    if ($appResult->num_rows > 0) {
                        $appData = $appResult->fetch_assoc();
                        $listingId = $appData['listing_id'];
                        $listingType = $appData['listing_type'];
                        $listingTitle = $appData['listing_title'] ?? 'Project';

                        error_log("Job Payment Webhook - Listing Title: '$listingTitle' (type: " . gettype($listingTitle) . ")");
                        error_log("Job Payment Webhook - Raw App Data: " . json_encode($appData));
                    }
                    $getAppStmt->close();
                }

                // 5. Create notification for doer (project started) with chat navigation
                $stmt = $conn->prepare("
                    INSERT INTO doer_notifications (
                        user_id, sender_id, type, title, content, associated_id,
                        conversation_id_for_chat_nav, conversation_lister_id, conversation_doer_id,
                        listing_id, listing_type, lister_id, lister_name, is_read, created_at
                    ) VALUES (?, ?, 'project_started', 'Project Started', ?, ?, ?, ?, ?, ?, ?, ?, ?, 0, CURRENT_TIMESTAMP)
                ");

                if ($stmt === false) {
                    throw new Exception("Failed to prepare doer notification statement");
                }

                $notificationContent = "Payment confirmed! The project has started. You will receive ₱" . number_format($doerFee, 2) . " when the lister confirms completion. Good luck!";

                // Get lister's full name for the notification
                $getListerNameStmt = $conn->prepare("SELECT full_name FROM users WHERE id = ?");
                $listerFullName = 'Lister'; // Default fallback
                if ($getListerNameStmt !== false) {
                    $getListerNameStmt->bind_param("i", $listerId);
                    $getListerNameStmt->execute();
                    $listerNameResult = $getListerNameStmt->get_result();
                    if ($listerNameResult->num_rows > 0) {
                        $listerFullName = $listerNameResult->fetch_assoc()['full_name'];
                    }
                    $getListerNameStmt->close();
                }

                // We'll update conversation ID later when we find it
                $conversationIdForNotification = null;

                $stmt->bind_param("iisiiiisiss",
                    $doerId,                    // user_id
                    $listerId,                  // sender_id (lister who made the payment)
                    $notificationContent,       // content
                    $applicationId,             // associated_id
                    $conversationIdForNotification, // conversation_id_for_chat_nav (will update later)
                    $listerId,                  // conversation_lister_id
                    $doerId,                    // conversation_doer_id
                    $listingId,                 // listing_id
                    $listingType,               // listing_type
                    $listerId,                  // lister_id
                    $listerFullName             // lister_name
                );

                if (!$stmt->execute()) {
                    error_log("Job Payment Webhook: Failed to create doer notification: " . $stmt->error);
                } else {
                    $notificationId = $conn->insert_id;
                    error_log("Job Payment Webhook: Doer notification created successfully with ID: $notificationId");
                }
                $stmt->close();

                // 5. Send system message to conversation indicating project has started
                error_log("Job Payment Webhook: Sending system message to conversation");

                // Get application details for conversation lookup
                $getAppStmt = $conn->prepare("
                    SELECT listing_id, listing_type FROM applicationsv2 WHERE id = ?
                ");

                if ($getAppStmt !== false) {
                    $getAppStmt->bind_param("i", $applicationId);
                    $getAppStmt->execute();
                    $appResult = $getAppStmt->get_result();

                    if ($appResult->num_rows > 0) {
                        $appData = $appResult->fetch_assoc();
                        $listingId = $appData['listing_id'];
                        $listingType = $appData['listing_type'];
                        $getAppStmt->close();

                        // Get conversation ID
                        $getConvStmt = $conn->prepare("
                            SELECT id FROM conversationsv2
                            WHERE listing_id = ? AND listing_type = ? AND lister_id = ? AND doer_id = ?
                        ");

                        if ($getConvStmt !== false) {
                            $getConvStmt->bind_param("isii", $listingId, $listingType, $listerId, $doerId);
                            $getConvStmt->execute();
                            $convResult = $getConvStmt->get_result();

                            if ($convResult->num_rows > 0) {
                                $conversationId = $convResult->fetch_assoc()['id'];
                                $getConvStmt->close();

                                // Update the notification with the conversation ID for chat navigation
                                if (isset($notificationId)) {
                                    $updateNotificationStmt = $conn->prepare("
                                        UPDATE doer_notifications
                                        SET conversation_id_for_chat_nav = ?
                                        WHERE id = ?
                                    ");
                                    if ($updateNotificationStmt !== false) {
                                        $updateNotificationStmt->bind_param("ii", $conversationId, $notificationId);
                                        $updateNotificationStmt->execute();
                                        $updateNotificationStmt->close();
                                        error_log("Job Payment Webhook: Updated notification $notificationId with conversation ID $conversationId for chat navigation");
                                    }
                                }

                                // Send system message (we already have lister's full name from notification creation)
                                $systemMessageContent = "$listerFullName started the project.";
                                $sendMessageStmt = $conn->prepare("
                                    INSERT INTO messagesv2 (conversation_id, sender_id, receiver_id, content, sent_at, type)
                                    VALUES (?, ?, ?, ?, NOW(), 'system')
                                ");

                                if ($sendMessageStmt !== false) {
                                    $sendMessageStmt->bind_param("iiis", $conversationId, $listerId, $doerId, $systemMessageContent);

                                    if ($sendMessageStmt->execute()) {
                                        error_log("Job Payment Webhook: System message sent successfully to conversation $conversationId");
                                    } else {
                                        error_log("Job Payment Webhook: Failed to send system message: " . $sendMessageStmt->error);
                                    }
                                    $sendMessageStmt->close();
                                } else {
                                    error_log("Job Payment Webhook: Failed to prepare system message statement");
                                }
                            } else {
                                $getConvStmt->close();
                                error_log("Job Payment Webhook: Conversation not found for application $applicationId");
                            }
                        }
                    } else {
                        $getAppStmt->close();
                        error_log("Job Payment Webhook: Application not found: $applicationId");
                    }
                }

                // Commit transaction
                $conn->commit();
                $conn->autocommit(true);

                error_log("Job payment confirmed: Lister $listerId paid ₱$totalAmount, Doer $doerId will receive ₱$doerFee on completion, Platform earned ₱$transactionFee");

                ob_clean();
                echo json_encode([
                    "status" => "ok",
                    "message" => "Job payment processed successfully",
                    "transaction_id" => $transactionId,
                    "application_id" => $applicationId,
                    "lister_id" => $listerId,
                    "doer_id" => $doerId,
                    "doer_fee" => $doerFee,
                    "platform_fee" => $transactionFee,
                    "total_amount" => $totalAmount
                ]);

            } catch (Exception $e) {
                // Rollback on error
                $conn->rollback();
                $conn->autocommit(true);
                throw $e;
            }
            break;

        case 'FAILED':
        case 'EXPIRED':
            // Update transaction status to failed/expired
            $stmt = $conn->prepare("
                UPDATE job_payment_transactions 
                SET status = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ");

            if ($stmt === false) {
                throw new Exception("Failed to prepare failed update statement");
            }

            $failedStatus = strtolower($status);
            $stmt->bind_param("si", $failedStatus, $transactionId);
            $stmt->execute();
            $stmt->close();

            error_log("Job payment $failedStatus: Transaction $transactionId");

            ob_clean();
            echo json_encode(["status" => "ok", "message" => "Transaction marked as $failedStatus"]);
            break;

        default:
            error_log("Unknown job payment webhook status: $status for transaction $transactionId");
            ob_clean();
            echo json_encode(["status" => "ok", "message" => "Status noted"]);
    }

} catch (Exception $e) {
    error_log("Job Payment Webhook Error: " . $e->getMessage());
    http_response_code(500);
    ob_clean();
    echo json_encode(["error" => $e->getMessage()]);
}

$conn->close();
?>
