import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hanapp/models/user.dart';
import 'package:hanapp/services/hanapp_balance_service.dart';
import 'package:hanapp/services/xendit_service.dart';
import 'package:hanapp/services/cash_in_service.dart';
import 'package:hanapp/utils/constants.dart' as Constants;

class CashInScreen extends StatefulWidget {
  final User user;

  const CashInScreen({Key? key, required this.user}) : super(key: key);

  @override
  State<CashInScreen> createState() => _CashInScreenState();
}

class _CashInScreenState extends State<CashInScreen> {
  final TextEditingController _amountController = TextEditingController();
  String? _selectedPaymentMethod;
  String? _selectedBank;
  String? _selectedCardType; // For credit card type selection
  bool _isLoading = false;
  double _currentBalance = 0.0;

  // Predefined amounts for quick selection (minimum ₱500)
  final List<double> _quickAmounts = [500, 1000, 2000, 5000, 10000, 20000];

  // Payment methods
  final Map<String, String> _paymentMethods = {
    'gcash': 'GCash',
    'bank_transfer': 'Bank Transfer',
    'card': 'Credit/Debit Card',
  };

  // Bank options for bank transfer
  final Map<String, String> _bankOptions = {
    'bpi': 'BPI',
    'bdo': 'BDO',
    'metrobank': 'Metrobank',
    'unionbank': 'UnionBank',
    'security_bank': 'Security Bank',
    'rcbc': 'RCBC',
  };

  @override
  void initState() {
    super.initState();
    _loadCurrentBalance();
  }

  Future<void> _loadCurrentBalance() async {
    try {
      final balanceService = HanAppBalanceService();
      final result = await balanceService.getHanAppBalance(userId: widget.user.id);

      if (result['success'] && mounted) {
        setState(() {
          _currentBalance = double.tryParse(result['balance'].toString()) ?? 0.0;
        });
      }
    } catch (e) {
      print('Error loading balance: $e');
    }
  }

  void _selectQuickAmount(double amount) {
    setState(() {
      _amountController.text = amount.toStringAsFixed(0);
    });
  }

  String _getPaymentMethodName(String method) {
    // Handle specific bank cases
    switch (method.toLowerCase()) {
      case 'bpi':
        return 'BPI';
      case 'bdo':
        return 'BDO';
      case 'metrobank':
        return 'Metrobank';
      case 'unionbank':
        return 'UnionBank';
      case 'security_bank':
        return 'Security Bank';
      case 'rcbc':
        return 'RCBC';
      case 'chinabank':
        return 'China Bank';
      case 'bank_transfer':
        if (_selectedBank != null) {
          return _bankOptions[_selectedBank!] ?? 'Bank Transfer';
        }
        return 'Bank Transfer';
      default:
        return _paymentMethods[method] ?? method;
    }
  }

  Future<void> _processCashIn() async {
    if (_selectedPaymentMethod == null) {
      _showSnackBar('Please select a payment method');
      return;
    }

    final amountText = _amountController.text.trim();
    if (amountText.isEmpty) {
      _showSnackBar('Please enter an amount');
      return;
    }

    final amount = double.tryParse(amountText);
    if (amount == null || amount <= 0) {
      _showSnackBar('Please enter a valid amount');
      return;
    }

    if (amount < 500) {
      _showSnackBar('Minimum cash-in amount is ₱500');
      return;
    }

    if (amount > 50000) {
      _showSnackBar('Maximum cash-in amount is ₱50,000');
      return;
    }

    if (_selectedPaymentMethod == 'bank_transfer' && _selectedBank == null) {
      _showSnackBar('Please select a bank');
      return;
    }

    if (_selectedPaymentMethod == 'card' && _selectedCardType == null) {
      _showSnackBar('Please select your card type');
      return;
    }

    await _createCashInInvoice(amount);
  }

  Future<void> _createCashInInvoice(double amount) async {
    setState(() {
      _isLoading = true;
    });

    _showLoadingDialog();

    try {
      // Determine payment method
      String paymentMethod = _selectedPaymentMethod!;
      if (_selectedPaymentMethod == 'bank_transfer' && _selectedBank != null) {
        paymentMethod = _selectedBank!;
      }

      // Use CashInService to create invoice
      final cashInService = CashInService();
      final result = await cashInService.createCashInInvoice(
        userId: widget.user.id,
        amount: amount,
        paymentMethod: paymentMethod,
        cardType: _selectedCardType, // Pass card type for credit cards
        userEmail: widget.user.email,
        userFullName: widget.user.fullName,
      );

      // Hide loading dialog
      if (mounted) Navigator.of(context).pop();

      if (result['success']) {
        final paymentDetails = result['payment_details'];
        final invoiceUrl = paymentDetails['invoice_url'];

        // Show success dialog and launch payment
        _showPaymentDialog(amount, invoiceUrl, paymentDetails);
      } else {
        _showSnackBar(result['message'] ?? 'Failed to create payment invoice');
      }
    } catch (e) {
      // Hide loading dialog
      if (mounted) Navigator.of(context).pop();
      _showSnackBar('Network error: ${e.toString()}');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showLoadingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 20),
            Text('Creating payment invoice...'),
          ],
        ),
      ),
    );
  }

  void _showPaymentDialog(double amount, String invoiceUrl, Map<String, dynamic> paymentDetails) {
    final baseAmount = paymentDetails['base_amount'] ?? amount;
    final transactionFee = paymentDetails['transaction_fee'] ?? 20.0;
    final totalAmount = paymentDetails['total_amount'] ?? (baseAmount + transactionFee);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cash-in Summary'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Payment Method - Compact
              Text(
                'Payment Method: ${_getPaymentMethodName(_selectedPaymentMethod!)}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.blue.shade700,
                ),
              ),
              const SizedBox(height: 12),

              // Amount Breakdown - Compact
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  children: [
                    // Base Amount
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('Cash-in Amount:', style: TextStyle(fontSize: 13)),
                        Text(
                          '₱${baseAmount.toStringAsFixed(2)}',
                          style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),

                    // Transaction Fee
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('Transaction Fee:', style: TextStyle(fontSize: 13)),
                        Text(
                          '₱${transactionFee.toStringAsFixed(2)}',
                          style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),

                    // Divider
                    Container(
                      height: 1,
                      color: Colors.grey.shade300,
                      margin: const EdgeInsets.symmetric(vertical: 6),
                    ),

                    // Total Amount
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Total to Pay:',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '₱${totalAmount.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Constants.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),

              // Compact Instructions
              Text(
                'Click "Pay Now" to complete payment. ₱${baseAmount.toStringAsFixed(2)} will be added to your balance.',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();

              // Launch payment URL
              final success = await XenditService.instance.launchPayment(invoiceUrl);
              if (success) {
                // Payment page opened successfully
                // Optionally navigate back or refresh balance
                Navigator.of(context).pop();
              } else {
                _showSnackBar('Failed to open payment page');
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Constants.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Pay Now'),
          ),
        ],
      ),
    );
  }

  void _showSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message)),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cash In'),
        backgroundColor: Constants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current Balance Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    const Icon(Icons.account_balance_wallet, color: Constants.primaryColor),
                    const SizedBox(width: 12),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Current Balance', style: TextStyle(fontSize: 14, color: Colors.grey)),
                        Text('₱${_currentBalance.toStringAsFixed(2)}',
                            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Quick Amount Selection
            const Text('Quick Select Amount', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _quickAmounts.map((amount) =>
                  OutlinedButton(
                    onPressed: () => _selectQuickAmount(amount),
                    child: Text('₱${amount.toStringAsFixed(0)}'),
                  ),
              ).toList(),
            ),

            const SizedBox(height: 24),

            // Amount Input
            const Text('Enter Amount', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            TextField(
              controller: _amountController,
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
              decoration: const InputDecoration(
                prefixText: '₱',
                hintText: '0.00',
                border: OutlineInputBorder(),
                helperText: 'Minimum: ₱500, Maximum: ₱50,000 (+ ₱20 transaction fee)',
              ),
            ),

            const SizedBox(height: 24),

            // Payment Method Selection
            const Text('Payment Method', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            ..._paymentMethods.entries.map((entry) =>
                RadioListTile<String>(
                  title: Text(entry.value),
                  value: entry.key,
                  groupValue: _selectedPaymentMethod,
                  onChanged: (value) {
                    setState(() {
                      _selectedPaymentMethod = value;
                      if (value != 'bank_transfer') {
                        _selectedBank = null;
                      }
                      if (value != 'card') {
                        _selectedCardType = null;
                      }
                    });
                  },
                ),
            ),

            // Bank Selection (if bank transfer is selected)
            if (_selectedPaymentMethod == 'bank_transfer') ...[
              const SizedBox(height: 16),
              const Text('Select Bank', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              DropdownButtonFormField<String>(
                value: _selectedBank,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  hintText: 'Choose your bank',
                ),
                items: _bankOptions.entries.map((entry) =>
                    DropdownMenuItem(
                      value: entry.key,
                      child: Text(entry.value),
                    ),
                ).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedBank = value;
                  });
                },
              ),
            ],

            // Card Type Selection (if credit card is selected)
            if (_selectedPaymentMethod == 'card') ...[
              const SizedBox(height: 16),
              const Text('Card Type', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    RadioListTile<String>(
                      title: const Text('Philippine Bank Card'),
                      subtitle: const Text('Service fee: 3.2% + ₱30'),
                      value: 'philippine',
                      groupValue: _selectedCardType,
                      onChanged: (value) {
                        setState(() {
                          _selectedCardType = value;
                        });
                      },
                      activeColor: Colors.green,
                    ),
                    Divider(height: 1, color: Colors.grey.shade300),
                    RadioListTile<String>(
                      title: const Text('International Card (PHP-billed)'),
                      subtitle: const Text('Service fee: 4.2% + ₱30'),
                      value: 'international_php',
                      groupValue: _selectedCardType,
                      onChanged: (value) {
                        setState(() {
                          _selectedCardType = value;
                        });
                      },
                      activeColor: Colors.orange,
                    ),
                  ],
                ),
              ),
            ],

            const SizedBox(height: 32),

            // Cash In Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _processCashIn,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Constants.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isLoading
                    ? const CircularProgressIndicator(color: Colors.white)
                    : const Text('Cash In', style: TextStyle(fontSize: 16)),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }
}
