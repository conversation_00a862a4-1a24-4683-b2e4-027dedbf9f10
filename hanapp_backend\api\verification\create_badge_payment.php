<?php
// hanapp_backend/api/verification/create_badge_payment.php
// Create Xendit invoice for HanApp Verified Badge monthly subscription

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Start output buffering to prevent any unexpected output
ob_start();

// Set up error handler to catch fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        ob_clean();
        http_response_code(500);
        echo json_encode([
            "success" => false,
            "message" => "Server error: " . $error['message'],
            "error_code" => "FATAL_ERROR"
        ]);
    }
});

// Test database connection first
try {
    require_once '../../config/db_connect.php';
    if (!isset($conn) || $conn->connect_error) {
        throw new Exception("Database connection failed: " . ($conn->connect_error ?? 'Unknown error'));
    }
} catch (Exception $e) {
    ob_clean();
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Database connection error: " . $e->getMessage(),
        "error_code" => "DB_CONNECTION_ERROR"
    ]);
    exit();
}

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    ob_clean();
    http_response_code(200);
    exit();
}

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("Only POST method is allowed.");
    }

    // Get and validate input
    $userId = $_POST['user_id'] ?? null;
    $paymentMethod = $_POST['payment_method'] ?? 'gcash';

    if (empty($userId) || !is_numeric($userId)) {
        throw new Exception("User ID is required and must be numeric.");
    }

    $userId = intval($userId);

    // Validate payment method - updated for new badge payment options
    $allowedMethods = ['gcash', 'card', 'bank_transfer', 'bpi', 'chinabank', 'rcbc', 'unionbank'];
    if (!in_array($paymentMethod, $allowedMethods)) {
        throw new Exception("Invalid payment method. Allowed: " . implode(', ', $allowedMethods));
    }

    // Badge subscription pricing (monthly)
    $badgePrice = 1.00;

    // Calculate transaction fees based on payment method (case-insensitive)
    $transactionFee = 0.00;
    $paymentMethodLower = strtolower(trim($paymentMethod));
    switch ($paymentMethodLower) {
        case 'gcash':
            $transactionFee = 2.30;
            break;
        case 'card':
        case 'debit_card':
        case 'credit_card':
        case 'credit/debit card':
            $transactionFee = 17.00;
            break;
        case 'bpi':
        case 'chinabank':
        case 'rcbc':
        case 'unionbank':
        case 'bank_transfer':
            $transactionFee = 17.00;
            break;
        default:
            $transactionFee = 0.00;
            break;
    }

    // Total amount = badge price + transaction fee
    $amount = $badgePrice + $transactionFee;

    // Debug logging
    error_log("Badge Payment Creation - User: $userId, Method: $paymentMethod, Badge Price: $badgePrice, Fee: $transactionFee, Total: $amount");

    // Check if user exists and get user info
    $stmt = $conn->prepare("SELECT id, full_name, email, badge_subscription_status, badge_subscription_end FROM users WHERE id = ?");
    if ($stmt === false) {
        throw new Exception("Failed to prepare user check statement.");
    }

    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        $stmt->close();
        throw new Exception("User not found.");
    }

    $user = $result->fetch_assoc();
    $stmt->close();

    // Check if user already has active subscription
    if ($user['badge_subscription_status'] === 'active' &&
        $user['badge_subscription_end'] &&
        strtotime($user['badge_subscription_end']) > time()) {
        throw new Exception("You already have an active badge subscription until " . date('F j, Y', strtotime($user['badge_subscription_end'])));
    }

    // Xendit API configuration
    $xenditSecretKey = 'xnd_production_k5NqlGpmZlTPGEvBlYrk7a9ukwr8b2DzfQtEh3YThOcZazymwOlXwFT5ZEHIZm2';
    $xenditBaseUrl = 'https://api.xendit.co';

    // Generate unique external ID
    $timestamp = time();
    $externalId = "hanapp_badge_{$userId}_{$timestamp}";

    // Configure payment methods for Xendit - updated for badge payments (case-insensitive)
    $xenditPaymentMethods = [];
    $paymentMethodLower = strtolower(trim($paymentMethod));
    switch ($paymentMethodLower) {
        case 'gcash':
            $xenditPaymentMethods = ['GCASH'];
            break;
        case 'card':
        case 'credit/debit card':
            $xenditPaymentMethods = ['CREDIT_CARD', 'DEBIT_CARD'];
            break;
        case 'bank_transfer':
            $xenditPaymentMethods = ['BANK_TRANSFER'];
            break;
        // Individual bank methods
        case 'bpi':
            $xenditPaymentMethods = ['DD_BPI'];
            break;
        case 'chinabank':
            $xenditPaymentMethods = ['BANK_TRANSFER'];
            break;
        case 'rcbc':
            $xenditPaymentMethods = ['BANK_TRANSFER'];
            break;
        case 'unionbank':
            $xenditPaymentMethods = ['BANK_TRANSFER'];
            break;
        default:
            $xenditPaymentMethods = ['GCASH', 'CREDIT_CARD', 'DEBIT_CARD', 'BANK_TRANSFER'];
    }

    // Create Xendit invoice request
    $invoiceData = [
        'external_id' => $externalId,
        'amount' => $amount,
        'description' => "HanApp Verified Badge Monthly Subscription - ₱" . number_format($amount, 2),
        'invoice_duration' => 86400, // 24 hours
        'currency' => 'PHP',
        'customer' => [
            'given_names' => $user['full_name'],
            'email' => $user['email']
        ],
        'success_redirect_url' => 'https://autosell.io/api/payment_success.html?type=badge',
        'failure_redirect_url' => 'https://autosell.io/api/payment_failed.html?type=badge',
        'payment_methods' => $xenditPaymentMethods
    ];

    // Make request to Xendit
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $xenditBaseUrl . '/v2/invoices');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($invoiceData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Basic ' . base64_encode($xenditSecretKey . ':'),
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    if ($curlError) {
        throw new Exception("Curl error: " . $curlError);
    }

    if ($httpCode !== 200) {
        $errorResponse = json_decode($response, true);
        $errorMessage = $errorResponse['message'] ?? 'Unknown Xendit error';
        throw new Exception("Xendit API error (HTTP $httpCode): " . $errorMessage);
    }

    $xenditResponse = json_decode($response, true);

    if (!$xenditResponse || !isset($xenditResponse['invoice_url'])) {
        throw new Exception("Invalid response from Xendit API");
    }

    // Store payment record in database
    $stmt = $conn->prepare("
        INSERT INTO badge_payments (
            user_id,
            xendit_invoice_id,
            external_id,
            amount,
            payment_method,
            status,
            invoice_url,
            created_at
        ) VALUES (?, ?, ?, ?, ?, 'pending', ?, NOW())
    ");

    if ($stmt === false) {
        throw new Exception("Failed to prepare payment insert statement.");
    }

    $stmt->bind_param("issdss",
        $userId,
        $xenditResponse['id'],
        $externalId,
        $amount,
        $paymentMethod,
        $xenditResponse['invoice_url']
    );

    if (!$stmt->execute()) {
        $stmt->close();
        throw new Exception("Failed to store payment record: " . $conn->error);
    }

    $stmt->close();

    // Debug logging for response
    error_log("Badge Payment Response - Amount: $amount, Invoice URL: " . $xenditResponse['invoice_url']);

    // Clean output buffer and send response
    ob_clean();
    echo json_encode([
        "success" => true,
        "message" => "Badge payment invoice created successfully",
        "data" => [
            "invoice_url" => $xenditResponse['invoice_url'],
            "invoice_id" => $xenditResponse['id'],
            "external_id" => $externalId,
            "amount" => $amount,
            "payment_method" => $paymentMethod,
            "expires_at" => $xenditResponse['expiry_date'] ?? null
        ]
    ]);

} catch (Exception $e) {
    ob_clean();
    http_response_code(400);
    echo json_encode([
        "success" => false,
        "message" => $e->getMessage(),
        "error_code" => "BADGE_PAYMENT_ERROR"
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
