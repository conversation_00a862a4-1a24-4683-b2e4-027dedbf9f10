-- Create job payment transactions table
CREATE TABLE IF NOT EXISTS job_payment_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL, -- Lister who is paying
    application_id INT NOT NULL,
    doer_id INT NOT NULL, -- Doer who will receive the payment
    doer_fee DECIMAL(10, 2) NOT NULL, -- Amount doer will receive
    transaction_fee DECIMAL(10, 2) NOT NULL DEFAULT 25.00, -- Platform fee
    total_amount DECIMAL(10, 2) NOT NULL, -- Total amount paid (doer_fee + transaction_fee)
    status ENUM('pending', 'completed', 'failed', 'expired') DEFAULT 'pending',
    payment_method VARCHAR(50),
    description TEXT,
    xendit_invoice_id VARCHAR(255),
    xendit_external_id VARCHAR(255),
    paid_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_application_id (application_id),
    INDEX idx_doer_id (doer_id),
    INDEX idx_status (status),
    INDEX idx_xendit_invoice (xendit_invoice_id),
    INDEX idx_xendit_external (xendit_external_id),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (application_id) REFERENCES applicationsv2(id) ON DELETE CASCADE,
    FOREIGN KEY (doer_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create platform revenue tracking table
CREATE TABLE IF NOT EXISTS platform_revenue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_type ENUM('job_payment_fee', 'subscription_fee', 'other') NOT NULL,
    reference_id INT, -- Reference to the source transaction
    amount DECIMAL(10, 2) NOT NULL,
    source VARCHAR(100) NOT NULL, -- 'job_payment', 'subscription', etc.
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_transaction_type (transaction_type),
    INDEX idx_reference_id (reference_id),
    INDEX idx_source (source),
    INDEX idx_created_at (created_at)
);

-- Add payment confirmation fields to applicationsv2 table
ALTER TABLE applicationsv2 
ADD COLUMN IF NOT EXISTS payment_confirmed BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS payment_confirmed_at TIMESTAMP NULL,
ADD INDEX IF NOT EXISTS idx_payment_confirmed (payment_confirmed);

-- Insert sample platform revenue data (optional)
-- This shows how the platform fee tracking works
INSERT IGNORE INTO platform_revenue (transaction_type, reference_id, amount, source, description) 
VALUES 
('job_payment_fee', 1, 25.00, 'job_payment', 'Sample platform fee from job payment'),
('job_payment_fee', 2, 25.00, 'job_payment', 'Sample platform fee from job payment');
