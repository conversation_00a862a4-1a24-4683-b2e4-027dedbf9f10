<?php
// hanapp_backend/api/job_payment/log_failed_job_payment.php
// Logs failed job payment attempts - Hostinger compatible

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

require_once '../../config/db_connect.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $applicationId = $input['application_id'] ?? null;
    $failureReason = $input['failure_reason'] ?? 'Payment failed';
    
    if (!$applicationId) {
        throw new Exception('Missing application ID');
    }
    
    // Log the failed payment attempt
    $stmt = $conn->prepare("
        INSERT INTO payment_failure_logs (application_id, failure_reason, failed_at) 
        VALUES (?, ?, NOW())
    ");
    $stmt->bind_param("is", $applicationId, $failureReason);
    $stmt->execute();
    $stmt->close();
    
    // Update job payment invoice status to failed (if exists)
    $stmt = $conn->prepare("
        UPDATE job_payment_invoices 
        SET status = 'failed', failed_at = NOW() 
        WHERE application_id = ? AND status = 'pending'
    ");
    $stmt->bind_param("i", $applicationId);
    $stmt->execute();
    $stmt->close();
    
    echo json_encode([
        'success' => true,
        'message' => 'Payment failure logged successfully'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
