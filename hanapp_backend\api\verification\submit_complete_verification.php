<?php
// hanapp_backend/api/verification/submit_complete_verification.php
// Handles submission of complete verification (ID photos + face scan)

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

require_once '../../config/db_connect.php';

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $userId = $_POST['user_id'] ?? null;
    $idType = $_POST['id_type'] ?? null;
    $confirmation = $_POST['confirmation'] ?? null;

    if (empty($userId) || !is_numeric($userId)) {
        throw new Exception("User ID is required and must be numeric.");
    }

    if (empty($idType)) {
        throw new Exception("ID type is required.");
    }

    if (!isset($_FILES['id_photo_front']) || !isset($_FILES['id_photo_back']) ||
        !isset($_FILES['brgy_clearance_photo']) || !isset($_FILES['live_photo'])) {
        throw new Exception("All required photos (ID front, ID back, Barangay clearance, live photo) are required.");
    }

    // Debug: Log received files
    error_log("submit_complete_verification.php: Received files: " . print_r(array_keys($_FILES), true));

    // Create upload directories
    $uploadDirs = [
        'id_front' => '../../uploads/front/images/',
        'id_back' => '../../uploads/back/images/',
        'brgy_clearance' => '../../uploads/brgy_clearance/images/',
        'live_photo' => '../../uploads/live_photos/images/'
    ];

    foreach ($uploadDirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
        }
    }

    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/heic', 'image/heif', 'image/webp'];
    $maxFileSize = 1 * 1024 * 1024; // 1 MB

    $uploadedFiles = [];

    // Process each file
    $files = [
        'id_photo_front' => ['prefix' => 'id_front', 'dir' => $uploadDirs['id_front']],
        'id_photo_back' => ['prefix' => 'id_back', 'dir' => $uploadDirs['id_back']],
        'brgy_clearance_photo' => ['prefix' => 'brgy_clearance', 'dir' => $uploadDirs['brgy_clearance']],
        'live_photo' => ['prefix' => 'live_photo', 'dir' => $uploadDirs['live_photo']]
    ];

    foreach ($files as $fileKey => $config) {
        $file = $_FILES[$fileKey];

        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception("$fileKey: Upload error code " . $file['error']);
        }

        if ($file['size'] > $maxFileSize) {
            throw new Exception("$fileKey: File size exceeds 1MB limit.");
        }

        // Generate unique filename
        $fileName = uniqid($config['prefix'] . '_') . '_' . $userId . '.' . pathinfo($file['name'], PATHINFO_EXTENSION);
        $filePath = $config['dir'] . $fileName;

        if (!move_uploaded_file($file['tmp_name'], $filePath)) {
            throw new Exception("Failed to move $fileKey.");
        }

        $uploadedFiles[$fileKey] = str_replace('../../', '', $filePath);
    }

    // Debug: Log uploaded files
    error_log("submit_complete_verification.php: Uploaded files: " . print_r($uploadedFiles, true));

    // Start database transaction
    $conn->begin_transaction();

    // Update users table with photo URLs (no id_type column in users table)
    $updateUserStmt = $conn->prepare("
        UPDATE users
        SET
            id_photo_front_url = ?,
            id_photo_back_url = ?,
            brgy_clearance_photo_url = ?,
            live_photo_url = ?,
            verification_status = 'pending',
            id_verified = 1,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
    ");

    if (!$updateUserStmt) {
        throw new Exception("Failed to prepare user update statement: " . $conn->error);
    }

    $updateUserStmt->bind_param("ssssi",
        $uploadedFiles['id_photo_front'],
        $uploadedFiles['id_photo_back'],
        $uploadedFiles['brgy_clearance_photo'],
        $uploadedFiles['live_photo'],
        $userId
    );

    if (!$updateUserStmt->execute()) {
        throw new Exception("Failed to update user: " . $updateUserStmt->error);
    }

    // Check if verification record already exists
    $checkStmt = $conn->prepare("SELECT id FROM verifications WHERE user_id = ?");
    if (!$checkStmt) {
        throw new Exception("Failed to prepare check statement: " . $conn->error);
    }

    $checkStmt->bind_param("i", $userId);
    $checkStmt->execute();
    $result = $checkStmt->get_result();

    if ($result->num_rows > 0) {
        // Update existing verification record
        $updateVerificationStmt = $conn->prepare("
            UPDATE verifications
            SET
                id_type = ?,
                id_photo_front_url = ?,
                id_photo_back_url = ?,
                brgy_clearance_photo_url = ?,
                face_scan_photo_url = ?,
                confirmation_status = ?,
                verification_type = 'normal',
                status = 'pending',
                submitted_at = CURRENT_TIMESTAMP,
                reviewed_at = NULL,
                reviewer_id = NULL,
                rejection_reason = NULL
            WHERE user_id = ?
        ");

        if (!$updateVerificationStmt) {
            throw new Exception("Failed to prepare verification update statement: " . $conn->error);
        }

        $updateVerificationStmt->bind_param("ssssssi",
            $idType,
            $uploadedFiles['id_photo_front'],
            $uploadedFiles['id_photo_back'],
            $uploadedFiles['brgy_clearance_photo'],
            $uploadedFiles['live_photo'],
            $confirmation,
            $userId
        );

        if (!$updateVerificationStmt->execute()) {
            throw new Exception("Failed to update verification: " . $updateVerificationStmt->error);
        }
    } else {
        // Insert new verification record
        $insertVerificationStmt = $conn->prepare("
            INSERT INTO verifications (
                user_id, id_type, id_photo_front_url, id_photo_back_url,
                brgy_clearance_photo_url, face_scan_photo_url, confirmation_status,
                verification_type, status, submitted_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, 'normal', 'pending', CURRENT_TIMESTAMP)
        ");

        if (!$insertVerificationStmt) {
            throw new Exception("Failed to prepare verification insert statement: " . $conn->error);
        }

        $insertVerificationStmt->bind_param("issssss",
            $userId, $idType,
            $uploadedFiles['id_photo_front'],
            $uploadedFiles['id_photo_back'],
            $uploadedFiles['brgy_clearance_photo'],
            $uploadedFiles['live_photo'],
            $confirmation
        );

        if (!$insertVerificationStmt->execute()) {
            throw new Exception("Failed to insert verification: " . $insertVerificationStmt->error);
        }
    }

    // Commit transaction
    $conn->commit();

    echo json_encode([
        'success' => true,
        'message' => 'Complete verification submitted successfully! Your verification is now pending review.'
    ]);

} catch (Exception $e) {
    // Log the error for debugging
    error_log("submit_complete_verification.php: Error occurred: " . $e->getMessage());
    error_log("submit_complete_verification.php: Stack trace: " . $e->getTraceAsString());

    // Rollback transaction on error
    if (isset($conn) && $conn->connect_errno === 0) {
        $conn->rollback();
    }

    // Clean up uploaded files on error
    if (isset($uploadedFiles)) {
        foreach ($uploadedFiles as $filePath) {
            $fullPath = '../../' . $filePath;
            if (file_exists($fullPath)) {
                unlink($fullPath);
            }
        }
    }

    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
    error_log("submit_complete_verification.php: Script completed");
}
?>
