<?php
// hanapp_backend/api/job_payment/complete_job_payment.php
// Completes job payment after successful Xendit payment - Hostinger compatible

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

require_once '../../config/db_connect.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // Get JSON input
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    error_log("=== COMPLETE JOB PAYMENT PROCESSING STARTED ===");
    error_log("File being executed: " . __FILE__);
    error_log("Request URI: " . $_SERVER['REQUEST_URI']);
    error_log("Raw input: " . $rawInput);
    error_log("Parsed input: " . json_encode($input));

    if (!$input) {
        error_log("ERROR: Invalid JSON input");
        throw new Exception('Invalid JSON input');
    }

    $applicationId = $input['application_id'] ?? null;
    $doerFee = $input['doer_fee'] ?? null;

    error_log("Parameters: applicationId=$applicationId, doerFee=$doerFee");

    if (!$applicationId || !$doerFee) {
        error_log("ERROR: Missing required parameters");
        throw new Exception('Missing required parameters');
    }

    // Start transaction
    error_log("Starting database transaction...");
    $conn->autocommit(false);

    // Get application details (using correct table: applicationsv2)
    error_log("Querying applicationsv2 table...");
    $query = "
        SELECT a.id, a.doer_id, a.lister_id, a.listing_id, a.listing_type, a.status,
               COALESCE(pl.title, al.title) as listing_title,
               u.full_name as doer_name
        FROM applicationsv2 a
        LEFT JOIN listingsv2 pl ON a.listing_id = pl.id AND a.listing_type = 'PUBLIC'
        LEFT JOIN asap_listings al ON a.listing_id = al.id AND a.listing_type = 'ASAP'
        JOIN users u ON a.doer_id = u.id
        WHERE a.id = ?
    ";

    error_log("SQL Query: " . $query);
    $stmt = $conn->prepare($query);
    if ($stmt === false) {
        error_log("ERROR: Failed to prepare statement: " . $conn->error);
        throw new Exception('Failed to prepare application query: ' . $conn->error);
    }

    $stmt->bind_param("i", $applicationId);
    if (!$stmt->execute()) {
        error_log("ERROR: Failed to execute query: " . $stmt->error);
        throw new Exception('Failed to execute application query: ' . $stmt->error);
    }

    $result = $stmt->get_result();
    $application = $result->fetch_assoc();
    $stmt->close();

    error_log("Query result: " . json_encode($application));

    if (!$application) {
        error_log("ERROR: Application not found");
        throw new Exception('Application not found');
    }

    $doerId = $application['doer_id'];
    $listingId = $application['listing_id'];
    $listingType = $application['listing_type'];

    error_log("Application details: doerId=$doerId, listingId=$listingId, listingType=$listingType");
    
    // Get job payment invoice details
    $stmt = $conn->prepare("SELECT * FROM job_payment_invoices WHERE application_id = ? ORDER BY created_at DESC LIMIT 1");
    $stmt->bind_param("i", $applicationId);
    $stmt->execute();
    $result = $stmt->get_result();
    $invoice = $result->fetch_assoc();
    $stmt->close();

    if (!$invoice) {
        throw new Exception('Payment invoice not found');
    }
    
    $totalAmount = floatval($invoice['amount']);
    $serviceFee = $totalAmount - $doerFee;
    
    // Add doer fee (not total amount) to doer's total_profit
    $stmt = $conn->prepare("UPDATE users SET total_profit = total_profit + ? WHERE id = ?");
    $stmt->bind_param("di", $doerFee, $doerId);
    $stmt->execute();
    $stmt->close();
    
    // Update application status to completed (using correct table: applicationsv2)
    // Note: applicationsv2 table doesn't have completed_at column
    $stmt = $conn->prepare("UPDATE applicationsv2 SET status = 'completed' WHERE id = ?");
    $stmt->bind_param("i", $applicationId);
    $stmt->execute();
    $stmt->close();

    // Update listing status to completed (check listing type to update correct table)
    if ($listingType === 'ASAP') {
        $stmt = $conn->prepare("UPDATE asap_listings SET status = 'completed' WHERE id = ?");
    } else {
        $stmt = $conn->prepare("UPDATE listingsv2 SET status = 'completed' WHERE id = ?");
    }
    $stmt->bind_param("i", $listingId);
    $stmt->execute();
    $stmt->close();
    
    // Update invoice status to completed
    $stmt = $conn->prepare("UPDATE job_payment_invoices SET status = 'completed', completed_at = NOW() WHERE id = ?");
    $stmt->bind_param("i", $invoice['id']);
    $stmt->execute();
    $stmt->close();
    
    // Create transaction record for doer (earning)
    $stmt = $conn->prepare("
        INSERT INTO transactions (user_id, type, amount, description, transaction_date, method, status, xendit_invoice_id)
        VALUES (?, ?, ?, ?, NOW(), ?, ?, ?)
    ");
    $description = "Job earning from application #$applicationId";
    $stmt->bind_param("isdsssss", $doerId, 'credit', $doerFee, $description, $invoice['payment_method'], 'completed', $invoice['xendit_invoice_id']);
    $stmt->execute();
    $stmt->close();
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    echo json_encode([
        'success' => true,
        'message' => 'Job payment completed successfully',
        'doer_earning' => $doerFee,
        'service_fee' => $serviceFee,
        'total_amount' => $totalAmount
    ]);
    
} catch (Exception $e) {
    // Rollback transaction on error
    $conn->rollback();
    $conn->autocommit(true);

    error_log("=== COMPLETE JOB PAYMENT ERROR ===");
    error_log("Error message: " . $e->getMessage());
    error_log("Error file: " . $e->getFile());
    error_log("Error line: " . $e->getLine());
    error_log("Stack trace: " . $e->getTraceAsString());
    error_log("=== END ERROR LOG ===");

    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug_info' => [
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
