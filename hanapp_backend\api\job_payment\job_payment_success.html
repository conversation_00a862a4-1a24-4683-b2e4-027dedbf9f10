<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Payment Successful - HanApp</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
            animation: slideUp 0.6s ease-out;
        }
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .success-icon {
            font-size: 80px;
            margin-bottom: 20px;
            animation: bounce 1s ease-in-out;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        h1 {
            color: #2E7D32;
            margin-bottom: 15px;
            font-size: 28px;
            font-weight: 700;
        }
        p {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
            line-height: 1.5;
        }
        .amount-display {
            background: #E8F5E8;
            border: 2px solid #4CAF50;
            border-radius: 15px;
            padding: 20px;
            margin: 25px 0;
        }
        .amount-display div:first-child {
            color: #2E7D32;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        .amount-display div:last-child {
            color: #1B5E20;
            font-size: 32px;
            font-weight: 700;
        }
        .breakdown {
            background: #F8F9FA;
            border-radius: 12px;
            padding: 20px;
            margin: 25px 0;
            text-align: left;
        }
        .breakdown-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            font-size: 14px;
        }
        .breakdown-row:last-child {
            margin-bottom: 0;
        }
        .breakdown-total {
            border-top: 2px solid #E0E0E0;
            padding-top: 12px;
            margin-top: 12px;
            font-weight: 700;
            font-size: 16px;
            color: #1976D2;
        }
        .info {
            background: #E3F2FD;
            border-left: 4px solid #2196F3;
            padding: 15px;
            margin: 25px 0;
            text-align: left;
            border-radius: 0 8px 8px 0;
            font-size: 14px;
            line-height: 1.6;
        }
        .instruction {
            background: #FFF3E0;
            border: 1px solid #FFB74D;
            border-radius: 12px;
            padding: 20px;
            margin-top: 25px;
            text-align: left;
        }
        .instruction h3 {
            margin-top: 0;
            color: #1976D2;
        }
        .instruction p:first-of-type {
            font-weight: bold;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">💰</div>
        <h1>Job Payment Successful!</h1>
        <p id="message">Your payment has been processed successfully and the job has been completed!</p>

        <div class="amount-display">
            <div>Doer Earning</div>
            <div id="doer-earning">₱0.00</div>
        </div>

        <div class="breakdown">
            <div class="breakdown-row">
                <span>Doer Fee:</span>
                <span id="doer-fee">₱0.00</span>
            </div>
            <div class="breakdown-row">
                <span>Service Fee:</span>
                <span id="service-fee">₱0.00</span>
            </div>
            <div class="breakdown-row breakdown-total">
                <span>Total Paid:</span>
                <span id="total-paid">₱0.00</span>
            </div>
        </div>

        <div class="info">
            <strong>What happens next?</strong><br>
            • The doer's earnings have been updated<br>
            • Job status has been marked as completed<br>
            • Both parties will receive notifications<br>
            • Transaction history is available in the app
        </div>

        <div class="instruction">
            <h3>🎉 Payment Complete!</h3>
            <p><strong>Please return to the HanApp mobile application to see the updated job status.</strong></p>
            <p>The job has been completed and the doer has been paid.</p>
            <p>You can close this page and go back to the app.</p>
        </div>
    </div>

    <script>
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const applicationId = urlParams.get('application_id');
        const doerFee = parseFloat(urlParams.get('doer_fee')) || 0;
        
        // Update the display with the doer fee
        if (doerFee > 0) {
            document.getElementById('doer-earning').textContent = `₱${doerFee.toFixed(2)}`;
            document.getElementById('doer-fee').textContent = `₱${doerFee.toFixed(2)}`;
        }
        
        // Process the payment completion
        if (applicationId && doerFee > 0) {
            processJobPaymentCompletion(applicationId, doerFee);
        }
        
        async function processJobPaymentCompletion(applicationId, doerFee) {
            try {
                const response = await fetch('complete_job_payment.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        application_id: applicationId,
                        doer_fee: doerFee
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // Update the breakdown with actual values
                    if (result.service_fee !== undefined) {
                        document.getElementById('service-fee').textContent = `₱${result.service_fee.toFixed(2)}`;
                    }
                    if (result.total_amount !== undefined) {
                        document.getElementById('total-paid').textContent = `₱${result.total_amount.toFixed(2)}`;
                    }
                } else {
                    console.error('Failed to complete job payment:', result.message);
                }
            } catch (error) {
                console.error('Error processing job payment completion:', error);
            }
        }
    </script>
</body>
</html>
