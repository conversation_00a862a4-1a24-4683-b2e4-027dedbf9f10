<?php
// hanapp_backend/api/wallet/cash_in.php
// Create Xendit invoice for HanApp Balance cash-in

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Start output buffering to prevent any unexpected output
ob_start();

// Set up error handler to catch fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        ob_clean();
        http_response_code(500);
        echo json_encode([
            "success" => false,
            "message" => "Server error: " . $error['message'],
            "error_code" => "FATAL_ERROR",
            "file" => $error['file'],
            "line" => $error['line']
        ]);
    }
});

// Test database connection first
try {
    require_once '../../config/db_connect.php';
    if (!isset($conn) || $conn->connect_error) {
        throw new Exception("Database connection failed: " . ($conn->connect_error ?? 'Unknown error'));
    }
} catch (Exception $e) {
    ob_clean();
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Database connection error: " . $e->getMessage(),
        "error_code" => "DB_CONNECTION_ERROR"
    ]);
    exit();
}

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

// Function to calculate cash-in fees based on payment method
function calculateCashInFee($amount, $paymentMethod, $cardType = null) {
    $paymentMethodLower = strtolower(trim($paymentMethod));

    switch ($paymentMethodLower) {
        case 'gcash':
            // GCash: 2.3% + ₱20
            return ($amount * 0.023) + 20.0;

        case 'card':
        case 'credit/debit card':
            // Check card type for different rates
            $cardTypeLower = strtolower(trim($cardType ?? ''));
            if ($cardTypeLower === 'international_php') {
                // International Cards (PHP): 4.2% + ₱30
                return ($amount * 0.042) + 30.0;
            } else {
                // Philippine Cards (default): 3.2% + ₱30
                return ($amount * 0.032) + 30.0;
            }

        case 'bank_transfer':
        case 'bpi':
        case 'bdo':
        case 'metrobank':
        case 'unionbank':
        case 'security_bank':
        case 'rcbc':
        case 'chinabank':
            // Bank Transfer: 1% + ₱20
            return ($amount * 0.01) + 20.0;

        default:
            // Default fee
            return 20.0;
    }
}

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    ob_clean();
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ob_clean();
    echo json_encode(["success" => false, "message" => "Only POST method allowed."]);
    exit();
}

try {
    // Log the start of the request
    error_log("Cash-in request started at " . date('Y-m-d H:i:s'));

    // Get JSON input
    $rawInput = file_get_contents('php://input');
    error_log("Cash-in raw input: " . $rawInput);

    $input = json_decode($rawInput, true);

    if (!$input) {
        throw new Exception("Invalid JSON input. Raw input: " . $rawInput);
    }

    error_log("Cash-in parsed input: " . json_encode($input));

    // Validate required fields
    $userId = $input['user_id'] ?? null;
    $amount = $input['amount'] ?? null;
    $paymentMethod = $input['payment_method'] ?? 'gcash';
    $userEmail = $input['user_email'] ?? null;
    $userFullName = $input['user_full_name'] ?? null;

    if (empty($userId) || !is_numeric($userId)) {
        throw new Exception("User ID is required and must be numeric.");
    }

    if (empty($amount) || !is_numeric($amount) || $amount <= 0) {
        throw new Exception("Amount is required and must be greater than 0.");
    }

    if (empty($userEmail)) {
        throw new Exception("User email is required for payment processing.");
    }

    // Validate amount limits (minimum ₱500, maximum ₱50,000)
    $amount = floatval($amount);
    if ($amount < 500) {
        throw new Exception("Minimum cash-in amount is ₱500.");
    }
    if ($amount > 50000) {
        throw new Exception("Maximum cash-in amount is ₱50,000.");
    }

    // Calculate transaction fee based on payment method
    $transactionFee = calculateCashInFee($amount, $paymentMethod, $input['card_type'] ?? null);
    $totalAmount = $amount + $transactionFee;

    // Get display name for payment method
    function getPaymentMethodDisplayName($method) {
        switch (strtolower($method)) {
            case 'gcash':
                return 'GCash';
            case 'paymaya':
                return 'PayMaya';
            case 'bank_transfer':
                return 'Bank Transfer';
            case 'card':
                return 'Credit/Debit Card';
            case 'bpi':
                return 'BPI Direct Debit';
            case 'chinabank':
                return 'China Bank Direct Debit';
            case 'rcbc':
                return 'RCBC Direct Debit';
            case 'unionbank':
                return 'UBP Direct Debit';
            default:
                return ucfirst($method);
        }
    }

    $paymentMethodDisplay = getPaymentMethodDisplayName($paymentMethod);

    // Debug logging
    error_log("Cash-in Payment - User: $userId, Base Amount: $amount, Fee: $transactionFee, Total: $totalAmount, Method: $paymentMethodDisplay");

    // Check if user exists
    $stmt = $conn->prepare("SELECT id, full_name, email FROM users WHERE id = ?");
    if ($stmt === false) {
        throw new Exception("Failed to prepare user check statement.");
    }

    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        $stmt->close();
        throw new Exception("User not found.");
    }

    $user = $result->fetch_assoc();
    $stmt->close();

    // Use database user info if not provided
    if (empty($userFullName)) {
        $userFullName = $user['full_name'];
    }
    if (empty($userEmail)) {
        $userEmail = $user['email'];
    }

    // Xendit API configuration
    $xenditSecretKey = 'xnd_production_k5NqlGpmZlTPGEvBlYrk7a9ukwr8b2DzfQtEh3YThOcZazymwOlXwFT5ZEHIZm2';
    $xenditBaseUrl = 'https://api.xendit.co';

    // Generate unique external ID
    $timestamp = time();
    $externalId = "hanapp_cashin_{$userId}_{$timestamp}";

    // Create Xendit invoice request
    $invoiceData = [
        'external_id' => $externalId,
        'amount' => $totalAmount,
        'description' => "HanApp Balance Cash-in ₱" . number_format($amount, 2) . " + ₱" . number_format($transactionFee, 2) . " fee via " . $paymentMethodDisplay,
        'invoice_duration' => 86400, // 24 hours
        'currency' => 'PHP',
        'customer' => [
            'given_names' => $userFullName,
            'email' => $userEmail,
        ],
        'customer_notification_preference' => [
            'invoice_created' => ['email'],
            'invoice_reminder' => ['email'],
            'invoice_paid' => ['email'],
            'invoice_expired' => ['email'],
        ],
        'success_redirect_url' => 'https://autosell.io/cash_in_success.html?base_amount=' . urlencode($amount) . '&transaction_fee=' . urlencode($transactionFee) . '&total_amount=' . urlencode($totalAmount),
        'failure_redirect_url' => 'https://autosell.io/cash_in_failed.html?base_amount=' . urlencode($amount) . '&transaction_fee=' . urlencode($transactionFee) . '&total_amount=' . urlencode($totalAmount),
        'items' => [
            [
                'name' => 'HanApp Balance Cash-in',
                'quantity' => 1,
                'price' => $amount,
                'category' => 'Digital Wallet',
            ],
            [
                'name' => 'Transaction Fee',
                'quantity' => 1,
                'price' => $transactionFee,
                'category' => 'Service Fee',
            ]
        ],
        'metadata' => [
            'user_id' => $userId,
            'transaction_type' => 'cash_in',
            'payment_method' => $paymentMethod,
        ],
    ];

    // Configure payment methods - using same identifiers as make payment page
    switch (strtolower($paymentMethod)) {
        case 'gcash':
            $invoiceData['payment_methods'] = ['GCASH'];
            break;
        case 'paymaya':
            $invoiceData['payment_methods'] = ['PAYMAYA'];
            break;
        case 'bank_transfer':
            // Use generic bank transfer - let Xendit show available banks
            $invoiceData['payment_methods'] = ['BANK_TRANSFER'];
            break;
        case 'bpi':
            // BPI Direct Debit - CONFIRMED WORKING identifier
            $invoiceData['payment_methods'] = ['DD_BPI'];
            break;
        case 'chinabank':
            // China Bank Direct Debit - CONFIRMED WORKING identifier
            $invoiceData['payment_methods'] = ['DD_CHINABANK'];
            break;
        case 'rcbc':
            // RCBC Direct Debit - CONFIRMED WORKING identifier
            $invoiceData['payment_methods'] = ['DD_RCBC'];
            break;
        case 'unionbank':
            // UBP Direct Debit - CONFIRMED WORKING identifier
            $invoiceData['payment_methods'] = ['DD_UBP'];
            break;
        case 'card':
            $invoiceData['payment_methods'] = ['CREDIT_CARD', 'DEBIT_CARD'];
            break;
        default:
            $invoiceData['payment_methods'] = ['GCASH', 'PAYMAYA', 'BANK_TRANSFER', 'CREDIT_CARD', 'DEBIT_CARD'];
    }

    // Debug: Log the invoice data being sent to Xendit
    error_log("Cash-in Xendit Invoice Data: " . json_encode($invoiceData));

    // Make API call to Xendit
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $xenditBaseUrl . '/v2/invoices');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($invoiceData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Basic ' . base64_encode($xenditSecretKey . ':'),
        'Content-Type: application/json',
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $xenditResponse = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    // Log detailed information for debugging
    error_log("Cash-in Xendit API Call - HTTP Code: $httpCode");
    error_log("Cash-in Xendit API Call - Response: $xenditResponse");
    error_log("Cash-in Xendit API Call - cURL Error: $curlError");
    error_log("Cash-in Xendit API Call - Request Data: " . json_encode($invoiceData));

    if (($httpCode !== 200 && $httpCode !== 201) || !$xenditResponse) {
        error_log("Cash-in Xendit API Error: HTTP $httpCode, Response: $xenditResponse, cURL Error: $curlError");
        throw new Exception("Failed to create payment invoice. HTTP Code: $httpCode. Please try again.");
    }

    $invoiceResponse = json_decode($xenditResponse, true);
    if (!$invoiceResponse || !isset($invoiceResponse['id'])) {
        error_log("Cash-in Invalid Xendit Response: $xenditResponse");
        throw new Exception("Invalid response from payment provider.");
    }

    // Record transaction in database
    $stmt = $conn->prepare("
        INSERT INTO transactions (
            user_id, type, method, amount, transaction_fee, status, description,
            xendit_invoice_id, xendit_external_id, processed_by, notes
        ) VALUES (?, 'cash_in', ?, ?, ?, 'pending', ?, ?, ?, 'system', ?)
    ");

    if ($stmt === false) {
        throw new Exception("Failed to prepare transaction insert statement.");
    }

    $description = "HanApp Balance Cash-in ₱" . number_format($amount, 2) . " + ₱" . number_format($transactionFee, 2) . " fee via " . $paymentMethodDisplay;

    // Store card type in notes for webhook processing
    $notes = null;
    if ($paymentMethod === 'card' && !empty($input['card_type'])) {
        $notes = json_encode(['card_type' => $input['card_type']]);
    }

    $stmt->bind_param(
        "isddssss",
        $userId,
        $paymentMethod,
        $totalAmount,
        $transactionFee,
        $description,
        $invoiceResponse['id'],
        $externalId,
        $notes
    );

    if (!$stmt->execute()) {
        $stmt->close();
        error_log("Cash-in Database Error: " . $stmt->error);
        throw new Exception("Failed to record transaction.");
    }

    $transactionId = $conn->insert_id;
    $stmt->close();

    // Success response
    ob_clean();
    echo json_encode([
        "success" => true,
        "message" => "Payment invoice created successfully.",
        "payment_details" => [
            "invoice_id" => $invoiceResponse['id'],
            "external_id" => $externalId,
            "invoice_url" => $invoiceResponse['invoice_url'],
            "base_amount" => $amount,
            "transaction_fee" => $transactionFee,
            "total_amount" => $totalAmount,
            "currency" => "PHP",
            "status" => "pending",
            "expires_at" => $invoiceResponse['expiry_date'],
            "transaction_id" => $transactionId,
        ]
    ]);

} catch (Exception $e) {
    error_log("Cash-in Exception: " . $e->getMessage());
    ob_clean();
    echo json_encode([
        "success" => false,
        "message" => $e->getMessage(),
        "error_code" => "CASH_IN_ERROR"
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
