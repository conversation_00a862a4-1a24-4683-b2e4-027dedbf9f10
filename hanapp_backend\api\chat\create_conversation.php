<?php
// hanapp_backend/api/chat/create_conversation.php
// Creates a new conversation or gets an existing one

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

require_once '../config/db_connect.php';

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $input = file_get_contents("php://input");
    $data = json_decode($input, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("Invalid JSON payload.");
    }

    // Extract data from the payload
    $listerId = $data['lister_id'] ?? null;
    $doerId = $data['doer_id'] ?? null;
    $listingId = $data['listing_id'] ?? null;
    $listingType = $data['listing_type'] ?? null;
    $applicationId = $data['application_id'] ?? null; // NEW: Optional application ID

    // Debug logging
    error_log("create_conversation.php: Received data - lister_id: $listerId, doer_id: $doerId, listing_id: $listingId, listing_type: $listingType, application_id: $applicationId", 0);

    // Validation
    if (empty($listerId) || !is_numeric($listerId)) {
        throw new Exception("Lister ID is required and must be numeric.");
    }
    if (empty($doerId) || !is_numeric($doerId)) {
        throw new Exception("Doer ID is required and must be numeric.");
    }
    if (empty($listingId) || !is_numeric($listingId)) {
        throw new Exception("Listing ID is required and must be numeric.");
    }
    if (empty($listingType)) {
        throw new Exception("Listing type is required.");
    }

    // Start transaction
    $conn->begin_transaction();

    // Check if conversation already exists
    $checkSql = "
        SELECT id FROM conversationsv2 
        WHERE listing_id = ? AND listing_type = ? AND lister_id = ? AND doer_id = ?
    ";
    
    $checkStmt = $conn->prepare($checkSql);
    if ($checkStmt === false) {
        throw new Exception("Failed to prepare check statement: " . $conn->error);
    }

    $checkStmt->bind_param("isii", $listingId, $listingType, $listerId, $doerId);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();
    
    $conversationId = null;
    $isNewConversation = false;
    $applicationMessageSent = false;
    
    if ($checkResult->num_rows > 0) {
        // Conversation already exists
        $existingConversation = $checkResult->fetch_assoc();
        $conversationId = (int)$existingConversation['id'];
        $checkStmt->close();
        error_log("create_conversation.php: Found existing conversation ID: $conversationId", 0);
    } else {
        // Create new conversation
        $checkStmt->close();
        
        $insertSql = "
            INSERT INTO conversationsv2 (
                listing_id, listing_type, lister_id, doer_id, created_at, last_message_at
            ) VALUES (?, ?, ?, ?, NOW(), NOW())
        ";

        $insertStmt = $conn->prepare($insertSql);
        if ($insertStmt === false) {
            throw new Exception("Failed to prepare insert statement: " . $conn->error);
        }

        $insertStmt->bind_param("isii", $listingId, $listingType, $listerId, $doerId);

        if (!$insertStmt->execute()) {
            throw new Exception("Failed to create conversation: " . $insertStmt->error);
        }

        $conversationId = $conn->insert_id;
        $insertStmt->close();
        $isNewConversation = true;
        error_log("create_conversation.php: Created new conversation ID: $conversationId", 0);
    }

    // NEW: If application_id is provided, retrieve and send the application message
    if (!empty($applicationId) && is_numeric($applicationId)) {
        error_log("create_conversation.php: Processing application_id: $applicationId", 0);
        
        // Get application message and doer info
        $getAppSql = "
            SELECT a.message, u.full_name as doer_name 
            FROM applicationsv2 a 
            JOIN users u ON a.doer_id = u.id 
            WHERE a.id = ? AND a.lister_id = ? AND a.doer_id = ?
        ";
        
        $getAppStmt = $conn->prepare($getAppSql);
        if ($getAppStmt === false) {
            throw new Exception("Failed to prepare application query: " . $conn->error);
        }
        
        $getAppStmt->bind_param("iii", $applicationId, $listerId, $doerId);
        $getAppStmt->execute();
        $appResult = $getAppStmt->get_result();
        
        if ($appResult->num_rows > 0) {
            $appData = $appResult->fetch_assoc();
            $applicationMessage = $appData['message'];
            $doerName = $appData['doer_name'];
            $getAppStmt->close();
            
            error_log("create_conversation.php: Found application message: '$applicationMessage' from doer: '$doerName'", 0);
            
            // Check if this application message has already been sent
            $checkMessageSql = "
                SELECT id FROM messagesv2 
                WHERE conversation_id = ? AND sender_id = ? AND content = ? AND type = 'text'
            ";
            
            $checkMessageStmt = $conn->prepare($checkMessageSql);
            if ($checkMessageStmt === false) {
                throw new Exception("Failed to prepare message check statement: " . $conn->error);
            }
            
            $checkMessageStmt->bind_param("iis", $conversationId, $doerId, $applicationMessage);
            $checkMessageStmt->execute();
            $messageCheckResult = $checkMessageStmt->get_result();
            
            if ($messageCheckResult->num_rows === 0) {
                // Message doesn't exist, send it
                $checkMessageStmt->close();
                
                error_log("create_conversation.php: Application message not found in conversation, sending it now", 0);
                
                // Set timezone to Philippines time
                date_default_timezone_set('Asia/Manila');
                $currentTimestamp = date('Y-m-d H:i:s');
                
                // Insert the application message
                $insertMessageSql = "
                    INSERT INTO messagesv2 (conversation_id, sender_id, receiver_id, content, sent_at, type) 
                    VALUES (?, ?, ?, ?, ?, 'text')
                ";
                
                $insertMessageStmt = $conn->prepare($insertMessageSql);
                if ($insertMessageStmt === false) {
                    throw new Exception("Failed to prepare message insert statement: " . $conn->error);
                }
                
                $insertMessageStmt->bind_param("iiiss", $conversationId, $doerId, $listerId, $applicationMessage, $currentTimestamp);
                
                if (!$insertMessageStmt->execute()) {
                    throw new Exception("Failed to send application message: " . $insertMessageStmt->error);
                }
                
                $messageId = $conn->insert_id;
                $insertMessageStmt->close();
                $applicationMessageSent = true;
                
                error_log("create_conversation.php: Application message sent successfully with message ID: $messageId", 0);
                
                // Update conversation's last_message_at
                $updateConvSql = "UPDATE conversationsv2 SET last_message_at = ? WHERE id = ?";
                $updateConvStmt = $conn->prepare($updateConvSql);
                if ($updateConvStmt) {
                    $updateConvStmt->bind_param("si", $currentTimestamp, $conversationId);
                    $updateConvStmt->execute();
                    $updateConvStmt->close();
                }
                
                // Create notification for the lister
                $notificationTitle = "New Message";
                $notificationContent = "$doerName sent you a message";
                $notificationType = "message";
                
                $listerNotificationSql = "
                    INSERT INTO notificationsv2 (
                        user_id, sender_id, type, title, content, associated_id,
                        conversation_id_for_chat_nav, conversation_lister_id, conversation_doer_id,
                        related_listing_title, is_read
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0)
                ";
                
                // Get listing title for notification
                $listingTitle = '';
                $getTitleSql = "SELECT title FROM listingsv2 WHERE id = ?";
                $getTitleStmt = $conn->prepare($getTitleSql);
                if ($getTitleStmt) {
                    $getTitleStmt->bind_param("i", $listingId);
                    $getTitleStmt->execute();
                    $titleResult = $getTitleStmt->get_result();
                    if ($titleRow = $titleResult->fetch_assoc()) {
                        $listingTitle = $titleRow['title'];
                    }
                    $getTitleStmt->close();
                }
                
                $listerNotificationStmt = $conn->prepare($listerNotificationSql);
                if ($listerNotificationStmt) {
                    $listerNotificationStmt->bind_param("iisssiiiss",
                        $listerId,              // user_id (receiver - lister)
                        $doerId,                // sender_id (doer)
                        $notificationType,      // type
                        $notificationTitle,     // title
                        $notificationContent,   // content
                        $messageId,             // associated_id (message_id)
                        $conversationId,        // conversation_id_for_chat_nav
                        $listerId,              // conversation_lister_id
                        $doerId,                // conversation_doer_id
                        $listingTitle           // related_listing_title
                    );
                    
                    $listerNotificationStmt->execute();
                    $listerNotificationStmt->close();
                }
                
                error_log("create_conversation.php: Application message sent successfully for application ID $applicationId", 0);
            } else {
                $checkMessageStmt->close();
                error_log("create_conversation.php: Application message already exists for application ID $applicationId", 0);
            }
        } else {
            $getAppStmt->close();
            error_log("create_conversation.php: Application not found or access denied for application ID $applicationId", 0);
        }
    } else {
        error_log("create_conversation.php: No application_id provided or invalid: $applicationId", 0);
    }

    $conn->commit();

    echo json_encode([
        'success' => true,
        'conversation_id' => $conversationId,
        'message' => $isNewConversation ? 'Conversation created successfully.' : 'Existing conversation found.',
        'is_new' => $isNewConversation,
        'application_message_sent' => $applicationMessageSent
    ]);

} catch (Exception $e) {
    if (isset($conn)) {
        $conn->rollback();
    }
    http_response_code(500);
    error_log("create_conversation.php: Caught exception: " . $e->getMessage(), 0);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to create conversation: ' . $e->getMessage()
    ]);
} finally {
    if (isset($conn) && $conn instanceof mysqli && $conn->ping()) {
        $conn->close();
    }
}
?>