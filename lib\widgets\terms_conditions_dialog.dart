import 'package:flutter/material.dart';
import 'package:hanapp/models/app_setting.dart';
import 'package:hanapp/services/setting_service.dart';
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:shared_preferences/shared_preferences.dart';

class TermsConditionsDialog extends StatefulWidget {
  final VoidCallback onAccept;
  final VoidCallback onDeny;

  const TermsConditionsDialog({
    Key? key,
    required this.onAccept,
    required this.onDeny,
  }) : super(key: key);

  @override
  State<TermsConditionsDialog> createState() => _TermsConditionsDialogState();

  /// Static method to check if terms have been accepted
  static Future<bool> hasAcceptedTerms() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('terms_accepted') ?? false;
  }

  /// Static method to show the dialog
  static Future<void> showTermsDialog({
    required BuildContext context,
    required VoidCallback onAccept,
    required VoidCallback onDeny,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false, // User must tap a button
      builder: (BuildContext context) {
        return TermsConditionsDialog(
          onAccept: onAccept,
          onDeny: onDeny,
        );
      },
    );
  }
}

class _TermsConditionsDialogState extends State<TermsConditionsDialog> {
  AppSetting? _termsAndConditions;
  bool _isLoading = true;
  String? _errorMessage;
  final SettingService _settingService = SettingService();

  @override
  void initState() {
    super.initState();
    _fetchTermsAndConditions();
  }

  Future<void> _fetchTermsAndConditions() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await _settingService.getTermsAndConditions();
      if (response['success']) {
        setState(() {
          _termsAndConditions = response['data'];
        });
      } else {
        _errorMessage = response['message'] ?? 'Failed to load Terms and Conditions.';
      }
    } catch (e) {
      _errorMessage = 'An error occurred: $e';
      debugPrint('Error fetching Terms and Conditions: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _handleAccept() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('terms_accepted', true);
    widget.onAccept();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.description,
                  color: Constants.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'Terms & Conditions',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Please read and accept our Terms & Conditions to continue using the app.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 16),
            
            // Content
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _errorMessage != null
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.error_outline, color: Colors.red.shade400, size: 40),
                            const SizedBox(height: 8),
                            Text(
                              _errorMessage!,
                              textAlign: TextAlign.center,
                              style: const TextStyle(color: Colors.red, fontSize: 14),
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _fetchTermsAndConditions,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Constants.primaryColor,
                                foregroundColor: Colors.white,
                              ),
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      )
                    : _termsAndConditions == null || _termsAndConditions!.description.isEmpty
                    ? Center(
                        child: Text(
                          'Terms and Conditions content is not available.',
                          style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                        ),
                      )
                    : SingleChildScrollView(
                        padding: const EdgeInsets.all(16.0),
                        child: HtmlWidget(
                          _termsAndConditions!.description,
                          textStyle: const TextStyle(fontSize: 14, height: 1.4),
                          enableCaching: true,
                        ),
                      ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: widget.onDeny,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                      side: const BorderSide(color: Colors.red),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text('Deny'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading || _errorMessage != null ? null : _handleAccept,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Constants.primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text('Accept'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  // Remove the static methods from here since they're now in the main class
}