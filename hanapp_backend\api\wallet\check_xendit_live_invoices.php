<?php
// hanapp_backend/api/wallet/check_xendit_live_invoices.php
// Fetch all invoices directly from Xendit API (not database)

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    echo json_encode(["success" => true, "message" => "CORS preflight handled"]);
    exit();
}

try {
    // Get parameters
    $limit = $_GET['limit'] ?? 25;
    $status_filter = $_GET['status'] ?? null; // PENDING, PAID, EXPIRED, FAILED
    $created_after = $_GET['created_after'] ?? null; // ISO date
    $created_before = $_GET['created_before'] ?? null; // ISO date

    // Xendit API credentials
    $xendit_secret_key = 'xnd_production_k5NqlGpmZlTPGEvBlYrk7a9ukwr8b2DzfQtEh3YThOcZazymwOlXwFT5ZEHIZm2';

    // Build Xendit API URL with parameters
    $xendit_url = "https://api.xendit.co/v2/invoices";
    $params = [];

    if ($limit) $params['limit'] = $limit;
    if ($status_filter) $params['statuses'] = $status_filter;
    if ($created_after) $params['created_gte'] = $created_after;
    if ($created_before) $params['created_lte'] = $created_before;

    if (!empty($params)) {
        $xendit_url .= '?' . http_build_query($params);
    }

    error_log("XENDIT_LIVE: Fetching invoices from: $xendit_url");

    // Make API call to Xendit
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $xendit_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Basic ' . base64_encode($xendit_secret_key . ':'),
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $xendit_response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_code !== 200 || !$xendit_response) {
        throw new Exception("Failed to fetch from Xendit API. HTTP: $http_code");
    }

    $xendit_data = json_decode($xendit_response, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("Invalid JSON response from Xendit");
    }

    // Process invoices
    $processed_invoices = [];
    $stats = [
        'total' => 0,
        'pending' => 0,
        'paid' => 0,
        'expired' => 0,
        'failed' => 0,
        'total_amount' => 0,
        'paid_amount' => 0,
        'pending_amount' => 0
    ];

    foreach ($xendit_data as $invoice) {
        $status = $invoice['status'] ?? 'UNKNOWN';
        $amount = floatval($invoice['amount'] ?? 0);
        $paid_amount = floatval($invoice['paid_amount'] ?? 0);

        // Update statistics
        $stats['total']++;
        $stats['total_amount'] += $amount;

        switch (strtolower($status)) {
            case 'pending':
                $stats['pending']++;
                $stats['pending_amount'] += $amount;
                break;
            case 'paid':
                $stats['paid']++;
                $stats['paid_amount'] += $paid_amount;
                break;
            case 'expired':
                $stats['expired']++;
                break;
            case 'failed':
                $stats['failed']++;
                break;
        }

        // Process invoice data
        $processed_invoice = [
            'id' => $invoice['id'],
            'external_id' => $invoice['external_id'] ?? null,
            'status' => $status,
            'amount' => $amount,
            'paid_amount' => $paid_amount,
            'currency' => $invoice['currency'] ?? 'PHP',
            'description' => $invoice['description'] ?? '',
            'invoice_url' => $invoice['invoice_url'] ?? '',
            'created' => $invoice['created'] ?? '',
            'updated' => $invoice['updated'] ?? '',
            'expiry_date' => $invoice['expiry_date'] ?? '',
            'payment_method' => $invoice['payment_method'] ?? null,
            'payment_channel' => $invoice['payment_channel'] ?? null,
            'payment_destination' => $invoice['payment_destination'] ?? null,
            'customer' => [
                'given_names' => $invoice['customer']['given_names'] ?? '',
                'email' => $invoice['customer']['email'] ?? ''
            ],
            'fees' => $invoice['fees'] ?? [],
            'available_banks' => $invoice['available_banks'] ?? [],
            'available_retail_outlets' => $invoice['available_retail_outlets'] ?? [],
            'available_ewallets' => $invoice['available_ewallets'] ?? []
        ];

        $processed_invoices[] = $processed_invoice;
    }

    // Sort by created date (newest first)
    usort($processed_invoices, function($a, $b) {
        return strtotime($b['created']) - strtotime($a['created']);
    });

    error_log("XENDIT_LIVE: Successfully fetched " . count($processed_invoices) . " invoices");

    echo json_encode([
        'success' => true,
        'source' => 'xendit_api',
        'invoices' => $processed_invoices,
        'total_found' => count($processed_invoices),
        'statistics' => $stats,
        'filters_applied' => [
            'limit' => (int)$limit,
            'status' => $status_filter,
            'created_after' => $created_after,
            'created_before' => $created_before
        ],
        'api_info' => [
            'xendit_url' => $xendit_url,
            'http_status' => $http_code,
            'response_time' => date('Y-m-d H:i:s')
        ]
    ]);

} catch (Exception $e) {
    error_log("XENDIT_LIVE ERROR: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'source' => 'xendit_api'
    ]);
}
?>
