<?php
// hanapp_backend/api/public_listing/update_listing.php
// Updates an existing public listing with payment method and fee handling

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set up error handler to return JSON errors
function handleError($errno, $errstr, $errfile, $errline) {
    error_log("PHP Error: [$errno] $errstr in $errfile on line $errline");
    if (!headers_sent()) {
        header('Content-Type: application/json');
        echo json_encode([
            "success" => false,
            "message" => "Server error: $errstr",
            "error_details" => "Error in $errfile on line $errline"
        ]);
    }
    exit();
}
set_error_handler("handleError");

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../../config/db_connect.php';

// Check database connection (db_connect.php already handles connection errors)
if (!isset($conn)) {
    error_log("DEBUG - Connection object not created");
    echo json_encode(["success" => false, "message" => "Database connection not established."]);
    exit();
}

if ($conn->connect_error) {
    error_log("DEBUG - Database connection error: " . $conn->connect_error);
    echo json_encode(["success" => false, "message" => "Database connection failed: " . $conn->connect_error]);
    exit();
}

error_log("DEBUG - Database connection established successfully");

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(["success" => false, "message" => "Only POST requests are allowed."]);
    exit();
}

// Get JSON input and log it for debugging
$rawInput = file_get_contents('php://input');
error_log("DEBUG - Raw input received: " . $rawInput);

$input = json_decode($rawInput, true);
$jsonError = json_last_error();

if ($jsonError !== JSON_ERROR_NONE) {
    error_log("DEBUG - JSON decode error: " . json_last_error_msg());
    echo json_encode(["success" => false, "message" => "Invalid JSON input: " . json_last_error_msg()]);
    exit();
}

if (!$input) {
    error_log("DEBUG - Input is empty or invalid");
    echo json_encode(["success" => false, "message" => "Invalid JSON input: Empty or null"]);
    exit();
}

// Log the decoded input for debugging
error_log("DEBUG - Decoded input: " . print_r($input, true));

// Validate required fields
$requiredFields = ['id', 'title', 'description', 'price'];
error_log("DEBUG - Validating required fields: " . implode(', ', $requiredFields));

foreach ($requiredFields as $field) {
    if (!isset($input[$field])) {
        error_log("DEBUG - Missing field: $field");
        echo json_encode(["success" => false, "message" => "Missing required field: $field"]);
        exit();
    }
    if (is_string($input[$field]) && trim($input[$field]) === '') {
        error_log("DEBUG - Empty field: $field");
        echo json_encode(["success" => false, "message" => "Empty required field: $field"]);
        exit();
    }
}

error_log("DEBUG - All required fields validated successfully");

// Extract and validate input data
$listingId = intval($input['id']);
$title = trim($input['title']);
$description = trim($input['description']);
$price = floatval($input['price']);
$category = isset($input['category']) ? trim($input['category']) : 'Onsite';
$latitude = isset($input['latitude']) ? floatval($input['latitude']) : null;
$longitude = isset($input['longitude']) ? floatval($input['longitude']) : null;

// Check both possible field names for location address
$locationAddress = null;
if (isset($input['location_address'])) {
    $locationAddress = trim($input['location_address']);
} elseif (isset($input['locationAddress'])) {
    $locationAddress = trim($input['locationAddress']);
}

// Check both possible field names for preferred doer gender
$preferredDoerGender = 'Any';
if (isset($input['preferred_doer_gender'])) {
    $preferredDoerGender = trim($input['preferred_doer_gender']);
} elseif (isset($input['preferredDoerGender'])) {
    $preferredDoerGender = trim($input['preferredDoerGender']);
}
$tags = isset($input['tags']) ? trim($input['tags']) : null;

// Debug: Log extracted values
error_log("DEBUG - Extracted input values:");
error_log("  listingId: $listingId");
error_log("  title: '$title'");
error_log("  description: '$description'");
error_log("  price: $price");
error_log("  category: '$category'");
error_log("  latitude: " . ($latitude ?? 'NULL'));
error_log("  longitude: " . ($longitude ?? 'NULL'));
error_log("  locationAddress: " . ($locationAddress ?? 'NULL'));
error_log("  preferredDoerGender: '$preferredDoerGender'");
error_log("  tags: " . ($tags ?? 'NULL'));
error_log("  Input keys: " . implode(', ', array_keys($input)));







// Payment-related fields - only doer fee, no transaction fees during editing
$doerFee = isset($input['doerFee']) ? floatval($input['doerFee']) : $price;
$totalAmount = $doerFee; // No transaction fees during editing

// Handle pictures URLs - check both possible field names (camelCase and snake_case)
$picturesUrls = isset($input['pictures_urls']) ? $input['pictures_urls'] :
                (isset($input['picturesUrls']) ? $input['picturesUrls'] : null);
$picturesUrlsJson = null;

error_log("PUBLIC listing update - Pictures URLs received: " . json_encode($picturesUrls));

if ($picturesUrls !== null && is_array($picturesUrls) && !empty($picturesUrls)) {
    // Filter out empty URLs and placeholders
    $picturesUrls = array_filter($picturesUrls, function($url) {
        return !empty(trim($url)) &&
               strpos($url, 'placehold.co') === false &&
               strpos($url, 'example.com') === false;
    });

    if (!empty($picturesUrls)) {
        $picturesUrlsJson = json_encode(array_values($picturesUrls));
        error_log("PUBLIC listing update - Processed pictures JSON: " . $picturesUrlsJson);
    }
}

// Validate price
if ($price <= 0) {
    echo json_encode(["success" => false, "message" => "Price must be greater than zero."]);
    exit();
}

if ($price < 20) {
    echo json_encode(["success" => false, "message" => "Minimum price is ₱20.00."]);
    exit();
}

// Validate listing exists and get current data
$checkStmt = $conn->prepare("SELECT * FROM listingsv2 WHERE id = ?");
if ($checkStmt === false) {
    error_log("Failed to prepare listing check statement: " . $conn->error);
    echo json_encode(["success" => false, "message" => "Internal server error."]);
    exit();
}

$checkStmt->bind_param("i", $listingId);
$checkStmt->execute();
$result = $checkStmt->get_result();

if ($result->num_rows === 0) {
    $checkStmt->close();
    echo json_encode(["success" => false, "message" => "Listing not found."]);
    exit();
}

$existingListing = $result->fetch_assoc();
$checkStmt->close();

// Check if price has changed
$priceChanged = (floatval($existingListing['price']) !== $price);

// Debug price change information
error_log("DEBUG - Price change check: Price changed: " . ($priceChanged ? 'YES' : 'NO'));

// Recalculate fees if price changed (only doer fee, no payment method fees during editing)
if ($priceChanged) {
    $doerFee = $price; // Doer fee equals the price
    $totalAmount = $doerFee; // No transaction fees during editing

    error_log("DEBUG - Fees recalculated: doerFee=$doerFee, totalAmount=$totalAmount, Reason: PRICE_CHANGED");
}

// Check if the database connection is still valid
if ($conn->ping() === false) {
    error_log("DEBUG - Database connection lost. Attempting to reconnect.");
    // Use the same connection parameters from db_connect.php
    $servername = "localhost";
    $username = "u688984333_hanapp_use_new";
    $password = "Jardinel@2015";
    $dbname = "u688984333_hanapp_db_new";

    $conn = new mysqli($servername, $username, $password, $dbname);
    if ($conn->connect_error) {
        error_log("DEBUG - Failed to reconnect to database: " . $conn->connect_error);
        echo json_encode(["success" => false, "message" => "Database connection lost and failed to reconnect."]);
        exit();
    }
    $conn->set_charset("utf8mb4");
    $conn->query("SET time_zone = '+08:00'");
    error_log("DEBUG - Successfully reconnected to database.");
}

// Update listing in the 'listingsv2' table
// Build dynamic update query with only changed fields
$updateFields = [];
$paramTypes = "";
$paramValues = [];

// Debug: Log all field comparisons
error_log("DEBUG - Field comparisons:");
error_log("  Title: '$title' vs '{$existingListing['title']}' = " . ($title !== $existingListing['title'] ? 'CHANGED' : 'SAME'));
error_log("  Description: '$description' vs '{$existingListing['description']}' = " . ($description !== $existingListing['description'] ? 'CHANGED' : 'SAME'));
error_log("  Price: $price vs {$existingListing['price']} = " . ($priceChanged ? 'CHANGED' : 'SAME'));
error_log("  Category: '$category' vs '{$existingListing['category']}' = " . ($category !== $existingListing['category'] ? 'CHANGED' : 'SAME'));
error_log("  Latitude: $latitude vs {$existingListing['latitude']} = " . ($latitude != $existingListing['latitude'] ? 'CHANGED' : 'SAME'));
error_log("  Longitude: $longitude vs {$existingListing['longitude']} = " . ($longitude != $existingListing['longitude'] ? 'CHANGED' : 'SAME'));
error_log("  Location Address: '$locationAddress' vs '{$existingListing['location_address']}' = " . (($locationAddress !== null && $locationAddress !== $existingListing['location_address']) ? 'CHANGED' : 'SAME'));
error_log("  Preferred Doer Gender: '$preferredDoerGender' vs '{$existingListing['preferred_doer_gender']}' = " . ($preferredDoerGender !== $existingListing['preferred_doer_gender'] ? 'CHANGED' : 'SAME'));


// Check and add each field only if it has changed
if ($title !== $existingListing['title']) {
    $updateFields[] = "title = ?";
    $paramTypes .= "s";
    $paramValues[] = $title;
}

if ($description !== $existingListing['description']) {
    $updateFields[] = "description = ?";
    $paramTypes .= "s";
    $paramValues[] = $description;
}

if ($priceChanged) {
    $updateFields[] = "price = ?";
    $paramTypes .= "d";
    $paramValues[] = $price;

    // Update fee-related fields when price changes
    $updateFields[] = "doer_fee = ?";
    $paramTypes .= "d";
    $paramValues[] = $doerFee;

    $updateFields[] = "total_amount = ?";
    $paramTypes .= "d";
    $paramValues[] = $totalAmount;
}

if ($category !== $existingListing['category']) {
    $updateFields[] = "category = ?";
    $paramTypes .= "s";
    $paramValues[] = $category;
}

if ($latitude != $existingListing['latitude']) {
    $updateFields[] = "latitude = ?";
    $paramTypes .= "d";
    $paramValues[] = $latitude;
}

if ($longitude != $existingListing['longitude']) {
    $updateFields[] = "longitude = ?";
    $paramTypes .= "d";
    $paramValues[] = $longitude;
}

// Only update location_address if it's provided and different
if ($locationAddress !== null && $locationAddress !== $existingListing['location_address']) {
    $updateFields[] = "location_address = ?";
    $paramTypes .= "s";
    $paramValues[] = $locationAddress;
    error_log("DEBUG - Updating location_address from '{$existingListing['location_address']}' to '$locationAddress'");
} else {
    error_log("DEBUG - Not updating location_address: " .
        ($locationAddress === null ? "New location is null" : "Location unchanged"));
}

if ($preferredDoerGender !== $existingListing['preferred_doer_gender']) {
    $updateFields[] = "preferred_doer_gender = ?";
    $paramTypes .= "s";
    $paramValues[] = $preferredDoerGender;
}

// Enhanced pictures comparison logic (similar to ASAP listings)
$shouldUpdatePictures = false;

if ($picturesUrlsJson !== null) {
    // Get existing pictures for comparison
    $existingPicturesJson = $existingListing['pictures_urls'];
    $existingPicturesArray = json_decode($existingPicturesJson, true) ?? [];
    $newPicturesArray = json_decode($picturesUrlsJson, true) ?? [];

    error_log("PUBLIC listing update - Existing pictures: " . json_encode($existingPicturesArray));
    error_log("PUBLIC listing update - New pictures: " . json_encode($newPicturesArray));

    // Normalize arrays for comparison
    $existingPicturesFiltered = array_filter($existingPicturesArray, function($url) {
        return !empty($url) &&
               strpos($url, 'placehold.co') === false &&
               strpos($url, 'example.com') === false &&
               trim($url) !== '';
    });
    $newPicturesFiltered = array_filter($newPicturesArray, function($url) {
        return !empty($url) &&
               strpos($url, 'placehold.co') === false &&
               strpos($url, 'example.com') === false &&
               trim($url) !== '';
    });

    // Reset array keys and sort for comparison
    $existingPicturesFiltered = array_values($existingPicturesFiltered);
    $newPicturesFiltered = array_values($newPicturesFiltered);
    sort($existingPicturesFiltered);
    sort($newPicturesFiltered);

    // Check if pictures have changed
    if ($existingPicturesFiltered !== $newPicturesFiltered) {
        $shouldUpdatePictures = true;
        error_log("PUBLIC listing update - Pictures changed! Count: " . count($existingPicturesFiltered) . " -> " . count($newPicturesFiltered));
    } else {
        error_log("PUBLIC listing update - Pictures unchanged");
    }

    // Force update if new pictures array is different from existing (includes new uploads)
    if (!empty($newPicturesArray) && $newPicturesArray !== $existingPicturesArray) {
        $shouldUpdatePictures = true;
        error_log("PUBLIC listing update - Force pictures update due to new images");
    }
}

if ($shouldUpdatePictures) {
    $updateFields[] = "pictures_urls = ?";
    $paramTypes .= "s";
    $paramValues[] = $picturesUrlsJson;
    error_log("PUBLIC listing update - Adding pictures_urls to update fields");
}

if ($tags !== $existingListing['tags'] && $tags !== null) {
    $updateFields[] = "tags = ?";
    $paramTypes .= "s";
    $paramValues[] = $tags;
}

// If no fields to update, return success
if (empty($updateFields)) {
    echo json_encode([
        "success" => true,
        "message" => "No changes detected.",
        "listing_id" => $listingId
    ]);
    exit();
}

// Add updated_at timestamp
$updateFields[] = "updated_at = NOW()";

// Add ID parameter for WHERE clause
$paramTypes .= "i";
$paramValues[] = $listingId;

// Build the final query
$updateQuery = "UPDATE listingsv2 SET " . implode(", ", $updateFields) . " WHERE id = ?";

error_log("DEBUG - Update query: " . $updateQuery);
error_log("DEBUG - Param types: " . $paramTypes);

$stmt = $conn->prepare($updateQuery);

if ($stmt === false) {
    error_log("Failed to prepare public listing update statement: " . $conn->error);
    echo json_encode(["success" => false, "message" => "Internal server error."]);
    exit();
}

// Log parameter values for debugging
error_log("DEBUG - Parameters count: " . count($paramValues));

try {
    // Bind parameters dynamically
    if (!empty($paramValues)) {
        // Create array of references for bind_param
        $bindParams = [$paramTypes];
        foreach ($paramValues as $key => $value) {
            $bindParams[] = &$paramValues[$key];
        }
        call_user_func_array([$stmt, 'bind_param'], $bindParams);
    }
    error_log("DEBUG - Parameters bound successfully");
} catch (Exception $e) {
    error_log("DEBUG - Error binding parameters: " . $e->getMessage());
    echo json_encode(["success" => false, "message" => "Error binding parameters: " . $e->getMessage()]);
    exit();
}

error_log("DEBUG - About to execute update statement");

if ($stmt->execute()) {
    $affectedRows = $stmt->affected_rows;
    error_log("DEBUG - Statement executed successfully. Affected rows: " . $affectedRows);

    if ($affectedRows > 0) {
        $stmt->close();
        error_log("DEBUG - Listing updated successfully. Listing ID: " . $listingId);
        // Build changes detected array for response
        $changesDetected = [];
        foreach ($updateFields as $field) {
            if (strpos($field, 'pictures_urls') !== false) {
                $changesDetected[] = 'pictures_urls';
            } elseif (strpos($field, 'title') !== false) {
                $changesDetected[] = 'title';
            } elseif (strpos($field, 'description') !== false) {
                $changesDetected[] = 'description';
            } elseif (strpos($field, 'price') !== false) {
                $changesDetected[] = 'price';
            } elseif (strpos($field, 'category') !== false) {
                $changesDetected[] = 'category';
            } elseif (strpos($field, 'location') !== false) {
                $changesDetected[] = 'location';

            }
        }

        $pictureCount = 0;
        if ($picturesUrlsJson !== null) {
            $pictureArray = json_decode($picturesUrlsJson, true) ?? [];
            $pictureCount = count($pictureArray);
        }

        echo json_encode([
            "success" => true,
            "message" => "Public listing updated successfully!",
            "listing_id" => $listingId,
            "changes_detected" => $changesDetected,
            "pictures_count" => $pictureCount
        ]);
    } else {
        $stmt->close();
        error_log("DEBUG - No rows were affected. Listing may not exist or no changes were made.");
        echo json_encode([
            "success" => false,
            "message" => "No chDanges were made to the listing. The listing may not exist or the data is identical."
        ]);
    }
} else {
    $error = $stmt->error;
    $errno = $stmt->errno;
    error_log("DEBUG - Error executing public listing update statement. Error: " . $error . " (Code: " . $errno . ")");
    echo json_encode([
        "success" => false,
        "message" => "Database error: " . $error,
        "error_code" => $errno
    ]);
}

$conn->close();
?>