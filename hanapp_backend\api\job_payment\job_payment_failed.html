<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Payment Failed - TAPP</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
            animation: slideUp 0.6s ease-out;
        }
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .error-icon {
            font-size: 80px;
            margin-bottom: 20px;
            animation: shake 0.8s ease-in-out;
        }
        @keyframes shake {
            0%, 100% {
                transform: translateX(0);
            }
            25% {
                transform: translateX(-5px);
            }
            75% {
                transform: translateX(5px);
            }
        }
        h1 {
            color: #D32F2F;
            margin-bottom: 15px;
            font-size: 28px;
            font-weight: 700;
        }
        p {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
            line-height: 1.5;
        }
        .error-details {
            background: #FFEBEE;
            border: 2px solid #F44336;
            border-radius: 15px;
            padding: 20px;
            margin: 25px 0;
            text-align: left;
        }
        .error-details h3 {
            color: #D32F2F;
            margin-bottom: 10px;
            font-size: 16px;
        }
        .error-details ul {
            color: #666;
            font-size: 14px;
            line-height: 1.6;
            margin-left: 20px;
        }
        .error-details li {
            margin-bottom: 8px;
        }
        .retry-info {
            background: #E3F2FD;
            border-left: 4px solid #2196F3;
            padding: 15px;
            margin: 25px 0;
            text-align: left;
            border-radius: 0 8px 8px 0;
            font-size: 14px;
            line-height: 1.6;
        }
        .instruction {
            background: #FFF3E0;
            border: 1px solid #FFB74D;
            border-radius: 12px;
            padding: 20px;
            margin-top: 25px;
            text-align: left;
        }
        .instruction h3 {
            margin-top: 0;
            color: #1976D2;
        }
        .instruction p:first-of-type {
            font-weight: bold;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-icon">❌</div>
        <h1>Job Payment Failed</h1>
        <p id="message">Unfortunately, your payment could not be processed at this time.</p>

        <div class="error-details">
            <h3>Common reasons for payment failure:</h3>
            <ul>
                <li>Insufficient funds in your account</li>
                <li>Payment method temporarily unavailable</li>
                <li>Network connection issues</li>
                <li>Payment was cancelled by user</li>
                <li>Card or account verification required</li>
            </ul>
        </div>

        <div class="retry-info">
            <strong>What you can do:</strong><br>
            • Check your account balance or card details<br>
            • Try a different payment method<br>
            • Contact your bank if the issue persists<br>
            • Return to the app and try again later
        </div>

        <div class="instruction">
            <h3>💡 Next Steps</h3>
            <p><strong>Please return to the TAPP mobile application to retry the payment.</strong></p>
            <p>The job is still pending payment and can be completed once payment is successful.</p>
            <p>You can close this page and go back to the app to try again.</p>
        </div>
    </div>

    <script>
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const applicationId = urlParams.get('application_id');
        
        // Log the failed payment attempt
        if (applicationId) {
            logFailedPayment(applicationId);
        }
        
        async function logFailedPayment(applicationId) {
            try {
                const response = await fetch('log_failed_job_payment.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        application_id: applicationId,
                        failure_reason: 'Payment failed or cancelled'
                    })
                });
                
                const result = await response.json();
                
                if (!result.success) {
                    console.error('Failed to log payment failure:', result.message);
                }
            } catch (error) {
                console.error('Error logging failed payment:', error);
            }
        }
    </script>
</body>
</html>
