<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $applicationId = $input['application_id'] ?? null;
    $failureReason = $input['failure_reason'] ?? 'Payment failed';
    
    if (!$applicationId) {
        throw new Exception('Missing application ID');
    }
    
    // Log the failed payment attempt
    $stmt = $pdo->prepare("
        INSERT INTO payment_failure_logs (application_id, failure_reason, failed_at) 
        VALUES (?, ?, NOW())
    ");
    $stmt->execute([$applicationId, $failureReason]);
    
    // Update job payment invoice status to failed (if exists)
    $stmt = $pdo->prepare("
        UPDATE job_payment_invoices 
        SET status = 'failed', failed_at = NOW() 
        WHERE application_id = ? AND status = 'pending'
    ");
    $stmt->execute([$applicationId]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Payment failure logged successfully'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
