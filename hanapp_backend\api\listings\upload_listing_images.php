<?php
// hanapp_backend/api/listings/upload_listing_images.php
// Dedicated endpoint for uploading listing images - Hostinger compatible

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST method is allowed');
    }

    // Check if files were uploaded (handle both single and multiple file uploads)
    if (empty($_FILES)) {
        echo json_encode(['success' => false, 'message' => 'No images uploaded']);
        exit();
    }
    
    // Debug: Log all uploaded files
    error_log("Uploaded files: " . json_encode(array_keys($_FILES)));
    foreach ($_FILES as $key => $file) {
        error_log("File key: $key, name: " . json_encode($file['name']));
    }

    $uploadedUrls = [];
    $maxFileSize = 5 * 1024 * 1024; // 5MB
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    
    // Create upload directory - adjusted for Hostinger structure
    // Use ../../uploads/ to align with server structure
    $upload_dir = '../../api/uploads/listing_images/';
    if (!file_exists($upload_dir)) {
        if (!mkdir($upload_dir, 0755, true)) {
            throw new Exception('Failed to create upload directory');
        }
    }
    
    // Check directory permissions
    if (!is_writable($upload_dir)) {
        throw new Exception('Upload directory is not writable');
    }
    
    // Process each uploaded file (handle both array and individual file uploads)
    foreach ($_FILES as $fileKey => $fileData) {
        // Handle array of files (images[])
        if (is_array($fileData['name'])) {
            $fileCount = count($fileData['name']);
            for ($i = 0; $i < $fileCount; $i++) {
                if ($fileData['error'][$i] !== UPLOAD_ERR_OK) {
                    error_log("Upload error for file $fileKey[$i]: " . $fileData['error'][$i]);
                    continue;
                }
                
                if ($fileData['size'][$i] > $maxFileSize) {
                    error_log("File $fileKey[$i] exceeds size limit: " . $fileData['size'][$i]);
                    continue;
                }
                
                $finfo = finfo_open(FILEINFO_MIME_TYPE);
                $mimeType = finfo_file($finfo, $fileData['tmp_name'][$i]);
                finfo_close($finfo);
                
                if (!in_array($mimeType, $allowedTypes)) {
                    error_log("Invalid file type for file $fileKey[$i]: $mimeType");
                    continue;
                }
                
                $file_extension = pathinfo($fileData['name'][$i], PATHINFO_EXTENSION);
                $filename = 'listing_' . uniqid() . '_' . time() . '.' . $file_extension;
                $filepath = $upload_dir . $filename;
                
                if (move_uploaded_file($fileData['tmp_name'][$i], $filepath)) {
                    $relative_url = 'api/uploads/listing_images/' . $filename;
                    $uploadedUrls[] = $relative_url;
                    error_log("Successfully uploaded file: $relative_url");
                } else {
                    error_log("Failed to move uploaded file $fileKey[$i]");
                }
            }
        } else {
            // Handle single file upload
            if ($fileData['error'] !== UPLOAD_ERR_OK) {
                error_log("Upload error for file $fileKey: " . $fileData['error']);
                continue;
            }
            
            if ($fileData['size'] > $maxFileSize) {
                error_log("File $fileKey exceeds size limit: " . $fileData['size']);
                continue;
            }
            
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $fileData['tmp_name']);
            finfo_close($finfo);
            
            if (!in_array($mimeType, $allowedTypes)) {
                error_log("Invalid file type for file $fileKey: $mimeType");
                continue;
            }
            
            $file_extension = pathinfo($fileData['name'], PATHINFO_EXTENSION);
            $filename = 'listing_' . uniqid() . '_' . time() . '.' . $file_extension;
            $filepath = $upload_dir . $filename;
            
            if (move_uploaded_file($fileData['tmp_name'], $filepath)) {
                $relative_url = 'api/uploads/listing_images/' . $filename;
                $uploadedUrls[] = $relative_url;
                error_log("Successfully uploaded file: $relative_url");
            } else {
                error_log("Failed to move uploaded file $fileKey");
            }
        }
    }
    
    if (empty($uploadedUrls)) {
        throw new Exception('No images were successfully uploaded');
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Images uploaded successfully',
        'image_urls' => $uploadedUrls,
        'count' => count($uploadedUrls)
    ]);
    
} catch (Exception $e) {
    error_log("Upload listing images error: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
