-- SQL script to fetch ASAP listing notifications
-- This can be run directly in <PERSON><PERSON>'s phpMyAdmin or SQL interface
-- Replace 'YOUR_USER_ID' with the actual user ID you want to query

-- Query to get ASAP listing notifications for a specific user
SELECT 
    id,
    user_id,
    sender_id,
    type,
    title,
    content,
    associated_id,
    conversation_id_for_chat_nav,
    conversation_lister_id,
    conversation_doer_id,
    related_listing_title,
    listing_id,
    listing_type,
    lister_id,
    lister_name,
    is_read,
    created_at
FROM doer_notifications 
WHERE user_id = YOUR_USER_ID 
    AND type = 'asap_listing_available' 
ORDER BY created_at DESC;

-- Example usage (working query):
-- SELECT id, user_id, sender_id, type, title, content, associated_id, conversation_id_for_chat_nav, conversation_lister_id, conversation_doer_id, related_listing_title, listing_id, listing_type, lister_id, lister_name, is_read, created_at FROM doer_notifications WHERE user_id = 130 AND type = 'asap_listing_available' OR<PERSON><PERSON> BY created_at DESC;

-- To get all ASAP notifications for all users:
-- SELECT * FROM doer_notifications WHERE type = 'asap_listing_available' ORDER BY created_at DESC;

-- To count ASAP notifications per user:
-- SELECT user_id, COUNT(*) as asap_count FROM doer_notifications WHERE type = 'asap_listing_available' GROUP BY user_id ORDER BY asap_count DESC;