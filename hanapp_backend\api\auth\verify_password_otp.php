<?php
require_once '../config/db_connect.php';

// Set timezone at the beginning
date_default_timezone_set('Asia/Manila'); // or your local timezone

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(["success" => false, "message" => "Only POST method allowed"]);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['user_id']) || !isset($input['otp'])) {
    echo json_encode(["success" => false, "message" => "User ID and OTP are required"]);
    exit();
}

$userId = $input['user_id'];
$otp = $input['otp'];

try {
    // Get current time in the same timezone
    $currentTime = date('Y-m-d H:i:s');
    
    // Verify OTP
    $stmt = $conn->prepare("SELECT otp_code, expires_at FROM password_otp_codes WHERE user_id = ? AND otp_code = ? AND expires_at > ?");
    $stmt->bind_param("iss", $userId, $otp, $currentTime);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        // OTP is valid, delete it to prevent reuse
        $deleteStmt = $conn->prepare("DELETE FROM password_otp_codes WHERE user_id = ?");
        $deleteStmt->bind_param("i", $userId);
        $deleteStmt->execute();
        
        echo json_encode(["success" => true, "message" => "OTP verified successfully"]);
    } else {
        echo json_encode(["success" => false, "message" => "Invalid or expired OTP"]);
    }
    
} catch (Exception $e) {
    error_log("Password OTP Verification Error: " . $e->getMessage());
    echo json_encode(["success" => false, "message" => "Server error occurred"]);
}

$conn->close();
?>