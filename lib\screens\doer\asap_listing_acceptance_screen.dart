import 'package:flutter/material.dart';
import 'package:hanapp/models/asap_listing.dart';
import 'package:hanapp/utils/asap_listing_service.dart';
import 'package:hanapp/utils/auth_service.dart';
import 'package:hanapp/models/user.dart';
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/services/asap_acceptance_service.dart';
import 'dart:async';
import 'package:intl/intl.dart';
import 'dart:convert';
// Add this import if not already present
import 'package:hanapp/services/notification_service.dart';
import 'package:hanapp/utils/api_config.dart';

class AsapListingAcceptanceScreen extends StatefulWidget {
  final int listingId;
  final String? notificationId;

  const AsapListingAcceptanceScreen({
    Key? key,
    required this.listingId,
    this.notificationId,
  }) : super(key: key);

  

  @override
  State<AsapListingAcceptanceScreen> createState() => _AsapListingAcceptanceScreenState();
}

class _AsapListingAcceptanceScreenState extends State<AsapListingAcceptanceScreen> {
  AsapListing? _listing;
  User? _currentUser;
  bool _isLoading = true;
  bool _isProcessing = false;
  String? _errorMessage;
  Timer? _countdownTimer;
  int _remainingSeconds = 30;
  
  // Image carousel variables
  final PageController _pageController = PageController();
  int _currentImageIndex = 0;
  

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _initializeScreen();
    _startCountdown();
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    _priceOfferController.dispose();
    super.dispose();
  }

  Future<void> _initializeScreen() async {
    await _fetchCurrentUser();
    await _fetchListingDetails();
  }

  Future<void> _fetchCurrentUser() async {
    _currentUser = await AuthService.getUser();
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _fetchListingDetails() async {
    print('[DEBUG] Fetching listing details for ID: ${widget.listingId}');
    final response = await AsapListingService().getAsapListingDetails(widget.listingId);
    print('[DEBUG] API Response: ${json.encode(response)}');
    if (mounted) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
        if (response['success']) {
          _listing = response['listing'];
        } else {
          _errorMessage = response['message'] ?? 'Failed to load ASAP listing details.';
        }
      });
    }
  }

  void _startCountdown() {
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingSeconds > 0) {
        setState(() {
          _remainingSeconds--;
        });
      } else {
        timer.cancel();
        // Handle timeout: e.g., auto-reject or show message
        _handleTimeout();
      }
    });
  }

  void _handleTimeout() {
    if (mounted) {
      // Add this line to clear price input
      _priceOfferController.clear();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Time expired. Listing rejected.')),
      );
      Navigator.pop(context);
    }
  }

  // Add new state variables
  final TextEditingController _priceOfferController = TextEditingController();
  bool _showPriceInput = false;
  
  void _handleAcceptance(bool accept) async {
    print('[DEBUG] Open Price Status: ${_listing?.isOpenPrice}');
    print('[DEBUG] Show Input Flag: $_showPriceInput');
    if (_isProcessing || _currentUser == null) return;

    // If it's an open price listing and user wants to accept, show price input
    if (accept && _listing?.isOpenPrice == true && !_showPriceInput) {
      setState(() {
        _showPriceInput = true;
      });
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      double? offeredPrice;
      String action = accept ? 'accept' : 'reject';
      
      // For open price listings, use 'offer' action and include price
      if (accept && _listing?.isOpenPrice == true) {
        action = 'offer';
        offeredPrice = double.tryParse(_priceOfferController.text);
        
        if (offeredPrice == null || offeredPrice < 20) {
          _showSnackBar('Please enter a valid price offer (minimum ₱20)', isError: true);
          setState(() {
            _isProcessing = false;
          });
          return;
        }
      }

      final response = await AsapAcceptanceService().respondToAsapListing(
        listingId: widget.listingId,
        doerId: _currentUser!.id,
        action: action,
        offeredPrice: offeredPrice,
      );

      if (response['success']) {
        // Mark notification as read if it came from notification
        if (widget.notificationId != null) {
          await NotificationService().markDoerNotificationAsRead(
            notificationId: int.parse(widget.notificationId!),
          );
        }

        String message;
        if (action == 'offer') {
          message = 'Price offer of ₱${offeredPrice!.toStringAsFixed(2)} submitted successfully!';
        } else {
          message = accept ? 'ASAP request accepted successfully!' : 'ASAP request rejected.';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: accept ? Colors.green : Colors.orange,
          ),
        );

        Navigator.pop(context);
      } else {
        throw Exception(response['message'] ?? 'Failed to process request');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }
  
  // Build image carousel widget
  Widget _buildImageGallery() {
    if (_listing?.picturesUrls == null || _listing!.picturesUrls!.isEmpty) {
      return const SizedBox();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Images',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2D3748),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: 250,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.grey[100],
          ),
          child: Stack(
            children: [
              PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentImageIndex = index;
                  });
                },
                itemCount: _listing!.picturesUrls!.length,
                itemBuilder: (context, index) {
                  String originalUrl = _listing!.picturesUrls![index];
                  print('=== DEBUG IMAGE URL PARSING ===');
                  print('Image $index - Original URL: $originalUrl');
                  
                  String imageUrl;
                  
                  // Clean escape characters, remove quotes and all brackets
                  String cleanUrl = originalUrl
                      .replaceAll('\\/', '/')
                      .replaceAll('"', '')
                      .replaceAll(RegExp(r'[\[\]]+'), '') // Remove all brackets
                      .trim();
                  print('Image $index - Cleaned URL: $cleanUrl');
                  
                  // Construct proper image URL based on path type
                  String baseUrlWithoutApi = ApiConfig.baseUrl.replaceAll('/api', '');
                  
                  if (cleanUrl.startsWith('http')) {
                    imageUrl = cleanUrl;
                    print('Image $index - Using direct HTTP URL');
                  } else if (cleanUrl.startsWith('api/uploads/')) {
                    // Direct path to uploads - use base URL
                    imageUrl = '$baseUrlWithoutApi/$cleanUrl';
                    print('Image $index - Using base URL with api/uploads/ path');
                  } else if (cleanUrl.startsWith('uploads/')) {
                    // Just uploads path - add api prefix
                    imageUrl = '${ApiConfig.baseUrl}/$cleanUrl';
                    print('Image $index - Using API base URL with uploads/ path');
                  } else if (cleanUrl.startsWith('api/')) {
                    // Already has api prefix
                    imageUrl = '$baseUrlWithoutApi/$cleanUrl';
                    print('Image $index - Using base URL with existing api/ path');
                  } else {
                    // Default case - add api prefix
                    imageUrl = '${ApiConfig.baseUrl}/$cleanUrl';
                    print('Image $index - Using API base URL with default path');
                  }
                  
                  print('Image $index - Final Image URL: $imageUrl');
                    print('=== END DEBUG ===');
                   
                   return GestureDetector(
                    onTap: () => _showImagePreview(imageUrl),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: SizedBox(
                        width: double.infinity,
                        height: double.infinity,
                        child: Image.network(
                          imageUrl,
                          fit: BoxFit.cover,
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Center(
                              child: CircularProgressIndicator(
                                value: loadingProgress.expectedTotalBytes != null
                                    ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                                    : null,
                              ),
                            );
                          },
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.grey[200],
                              child: const Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(Icons.error_outline, size: 48, color: Colors.grey),
                                    SizedBox(height: 8),
                                    Text('Failed to load image', style: TextStyle(color: Colors.grey)),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  );
                },
              ),
              if (_listing!.picturesUrls!.length > 1)
                Positioned(
                  bottom: 16,
                  left: 0,
                  right: 0,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      _listing!.picturesUrls!.length,
                      (index) => Container(
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: _currentImageIndex == index
                              ? Colors.white
                              : Colors.white.withOpacity(0.5),
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  // Show image in full screen dialog
  void _showImagePreview(String imageUrl) {
    showDialog(
      context: context,
      barrierColor: Colors.black87,
      builder: (BuildContext context) {
        return GestureDetector(
          onTap: () => Navigator.of(context).pop(),
          child: Dialog(
            backgroundColor: Colors.transparent,
            insetPadding: EdgeInsets.zero,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              child: InteractiveViewer(
                child: Center(
                  child: Image.network(
                    imageUrl,
                    fit: BoxFit.contain,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Center(
                        child: CircularProgressIndicator(
                          value: loadingProgress.expectedTotalBytes != null
                              ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                              : null,
                          color: Colors.white,
                        ),
                      );
                    },
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[800],
                        child: const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.error_outline, size: 48, color: Colors.white),
                              SizedBox(height: 8),
                              Text('Failed to load image', style: TextStyle(color: Colors.white)),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // Add price input widget in the build method
  Widget _buildPriceOfferInput() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        border: Border.all(color: Colors.blue.shade200),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Enter Your Price Offer',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.blue.shade700,
            ),
          ),
          const SizedBox(height: 12),
          TextFormField(
            controller: _priceOfferController,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            decoration: InputDecoration(
              labelText: 'Your Price Offer *',
              hintText: 'e.g., 350.00',
              helperText: 'Minimum: ₱20.00',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              prefixText: '₱ ',
              prefixStyle: const TextStyle(
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your price offer';
              }
              final price = double.tryParse(value);
              if (price == null || price < 20) {
                return 'Minimum price offer is ₱20.00';
              }
              return null;
            },
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _showPriceInput = false;
                      _priceOfferController.clear();
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.shade400,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Cancel'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _handleAcceptance(true),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Submit Offer'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // In your build method, display the remaining seconds and progress bar
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade100,
      appBar: AppBar(
        title: const Text(
          'ASAP Request',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Constants.primaryColor,
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0,
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 16),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _remainingSeconds <= 10 
                  ? Colors.red.shade600 
                  : Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              '${_remainingSeconds}s',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage!,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey.shade600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('Go Back'),
                      ),
                    ],
                  ),
                )
              : _buildListingContent(),
    );
  }

  Widget _buildListingContent() {
    if (_listing == null) return const SizedBox();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Urgent banner
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.orange.shade600, Colors.red.shade600],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.flash_on,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'URGENT REQUEST',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _listing!.isOpenPrice
                        ? 'Open Price'  // Show this text for open price
                        : 'PHP ${_listing!.price.toStringAsFixed(0)}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          
          // Add this conditional for price input
          if (_showPriceInput) _buildPriceOfferInput(),
          
          // Listing details card
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _listing!.title,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    _listing!.description ?? 'No description provided',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade700,
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // Images section
                  if (_listing!.picturesUrls != null && _listing!.picturesUrls!.isNotEmpty)
                    _buildImageGallery(),
                  if (_listing!.picturesUrls != null && _listing!.picturesUrls!.isNotEmpty)
                    const SizedBox(height: 16),
                  
                  // Location and time info
                  Row(
                    children: [
                      Icon(Icons.location_on, color: Colors.grey.shade600, size: 20),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          _listing!.locationAddress ?? 'Location not specified',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.access_time, color: Colors.grey.shade600, size: 20),
                      const SizedBox(width: 4),
                      Text(
                        'Posted ${DateFormat('MMM dd, yyyy - hh:mm a').format(_listing!.createdAt)}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),
          
          // Action buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: _isProcessing ? null : () => _handleAcceptance(false),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.shade600,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isProcessing
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text(
                          'Reject',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: _isProcessing ? null : () => _handleAcceptance(true),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Constants.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isProcessing
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text(
                          'Accept',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Warning text
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange.shade200),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.warning_amber_outlined,
                  color: Colors.orange.shade600,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'You have ${_remainingSeconds} seconds to respond to this ASAP request.',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.orange.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
