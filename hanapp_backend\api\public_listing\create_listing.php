<?php
// hanapp_backend/api/public_listing/create_listing.php
// Creates a new public listing with proper payment method and fee handling

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../../config/db_connect.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(["success" => false, "message" => "Only POST requests are allowed."]);
    exit();
}

// Get JSON input
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    echo json_encode(["success" => false, "message" => "Invalid JSON input."]);
    exit();
}

// Extract data from request
$listerId = $data['lister_id'] ?? null;
$title = $data['title'] ?? '';
$description = $data['description'] ?? '';
$price = $data['price'] ?? 0.0;
$category = $data['category'] ?? 'Onsite';
$latitude = $data['latitude'] ?? null;
$longitude = $data['longitude'] ?? null;
$locationAddress = $data['location_address'] ?? '';
$preferredDoerGender = $data['preferred_doer_gender'] ?? 'Any';
$picturesUrls = $data['pictures_urls'] ?? [];

$isActive = $data['is_active'] ?? true;
$tags = $data['tags'] ?? '';

// The 'price' from Flutter is the doer fee that the user entered
// In the database:
// - price: stores the original user input (doer fee)
// - doer_fee: same as price (the amount doer will receive)
// - transaction_fee: calculated service fee based on payment method
// - total_amount: doer_fee + service_fee (what lister pays)
$doerFee = $price; // User entered doer fee directly





// No fee calculation during listing creation - fees will be calculated at payment time
$totalAmount = $doerFee;

// Debug logging
error_log("Public Listing Creation Debug:");
error_log("- User input (price): $price");
error_log("- Doer fee: $doerFee");
error_log("- Total amount: $totalAmount");

// Basic validation
if (empty($listerId) || empty($title) || empty($price)) {
    echo json_encode(["success" => false, "message" => "Required fields are missing (lister_id, title, price)."]);
    exit();
}

// Validate price minimum
if ($price < 20) {
    echo json_encode(["success" => false, "message" => "Minimum price is Php 20.00"]);
    exit();
}

// Convert pictures_urls array to JSON string
$picturesUrlsJson = json_encode($picturesUrls);
if ($picturesUrlsJson === false) {
    error_log("Failed to encode pictures_urls to JSON.");
    echo json_encode(["success" => false, "message" => "Error processing image URLs."]);
    exit();
}

// Insert new listing into the 'listingsv2' table
$stmt = $conn->prepare("
    INSERT INTO listingsv2 (
        lister_id, title, description, price, category, latitude, longitude,
        location_address, preferred_doer_gender, pictures_urls, tags,
        doer_fee, total_amount, status, is_active, created_at, updated_at
    )
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'open', ?, NOW(), NOW())
");

if ($stmt === false) {
    error_log("Failed to prepare public listing insert statement: " . $conn->error);
    echo json_encode(["success" => false, "message" => "Internal server error."]);
    exit();
}

$stmt->bind_param(
    "issdsddssssddi",
    $listerId,
    $title,
    $description,
    $price,
    $category,
    $latitude,
    $longitude,
    $locationAddress,
    $preferredDoerGender,
    $picturesUrlsJson,
    $tags,
    $doerFee,
    $totalAmount,
    $isActive
);

if ($stmt->execute()) {
    $listingId = $stmt->insert_id;
    $stmt->close();
    echo json_encode([
        "success" => true,
        "message" => "Public Listing created successfully!",
        "listing_id" => $listingId
    ]);
} else {
    error_log("Error executing public listing insert statement: " . $stmt->error);
    echo json_encode(["success" => false, "message" => "Failed to create public listing. Please try again."]);
}

$conn->close();
?>
