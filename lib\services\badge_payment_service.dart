import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:hanapp/utils/api_config.dart';

class BadgePaymentService {
  static String get _baseUrl => '${ApiConfig.baseUrl}/verification';

  /// Create a badge payment invoice using Xendit
  Future<Map<String, dynamic>> createBadgePayment({
    required int userId,
    required String paymentMethod,
  }) async {
    try {
      print('BadgePaymentService: Creating badge payment for user $userId with method $paymentMethod');

      final url = Uri.parse('$_baseUrl/create_badge_payment.php');

      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'user_id': userId.toString(),
          'payment_method': paymentMethod,
        },
      );

      print('BadgePaymentService Response: ${response.statusCode}');
      print('BadgePaymentService Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        print('BadgePaymentService Decoded Response: $data');
        return data;
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      print('BadgePaymentService Error: $e');
      throw Exception('Failed to create badge payment: $e');
    }
  }

  /// Check badge payment status
  Future<Map<String, dynamic>> checkBadgePaymentStatus({
    required String externalId,
  }) async {
    try {
      print('BadgePaymentService: Checking payment status for $externalId');

      final url = Uri.parse('$_baseUrl/check_badge_payment_status.php');

      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'external_id': externalId,
        },
      );

      print('BadgePaymentService Status Check Response: ${response.statusCode}');
      print('BadgePaymentService Status Check Body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        print('BadgePaymentService Status Decoded Response: $data');
        return data;
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      print('BadgePaymentService Status Check Error: $e');
      throw Exception('Failed to check payment status: $e');
    }
  }

  /// Get user's badge subscription info
  Future<Map<String, dynamic>> getBadgeSubscriptionInfo({
    required int userId,
  }) async {
    try {
      print('BadgePaymentService: Getting subscription info for user $userId');

      final url = Uri.parse('$_baseUrl/get_badge_subscription_info.php');

      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'user_id': userId.toString(),
        },
      );

      print('BadgePaymentService Subscription Info Response: ${response.statusCode}');
      print('BadgePaymentService Subscription Info Body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        print('BadgePaymentService Subscription Info Decoded Response: $data');
        return data;
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      print('BadgePaymentService Subscription Info Error: $e');
      throw Exception('Failed to get subscription info: $e');
    }
  }

  /// Cancel badge subscription
  Future<Map<String, dynamic>> cancelBadgeSubscription({
    required int userId,
  }) async {
    try {
      print('BadgePaymentService: Cancelling subscription for user $userId');

      final url = Uri.parse('$_baseUrl/cancel_badge_subscription.php');

      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'user_id': userId.toString(),
        },
      );

      print('BadgePaymentService Cancel Response: ${response.statusCode}');
      print('BadgePaymentService Cancel Body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        print('BadgePaymentService Cancel Decoded Response: $data');
        return data;
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      print('BadgePaymentService Cancel Error: $e');
      throw Exception('Failed to cancel subscription: $e');
    }
  }
}
