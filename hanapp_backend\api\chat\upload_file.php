<?php
// hanapp_backend/api/chat/upload_file.php
// Handles file uploads for chat messages

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../config/db_connect.php';

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST method allowed');
    }
    
    // Validate required fields
    if (!isset($_POST['conversation_id']) || !isset($_POST['sender_id']) || !isset($_POST['file_type'])) {
        throw new Exception('Missing required fields: conversation_id, sender_id, file_type');
    }
    
    $conversationId = (int)$_POST['conversation_id'];
    $senderId = (int)$_POST['sender_id'];
    $fileType = $_POST['file_type']; // 'file' or 'audio'
    
    // Validate file upload
    if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('File upload failed or no file provided');
    }
    
    $uploadedFile = $_FILES['file'];
    $originalName = $uploadedFile['name'];
    $fileSize = $uploadedFile['size'];
    $tmpPath = $uploadedFile['tmp_name'];
    
    // Validate file size (max 10MB)
    if ($fileSize > 10 * 1024 * 1024) {
        throw new Exception('File size exceeds 10MB limit');
    }
    
    // Get file info
    $fileInfo = pathinfo($originalName);
    $extension = strtolower($fileInfo['extension'] ?? '');
    
    // Validate file types
    $allowedExtensions = [
        'file' => ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'gif', 'zip', 'rar', 'mp4', 'mov', 'avi', 'mkv', 'wmv', 'flv', 'webm'],
        'audio' => ['mp3', 'wav', 'aac', 'm4a', 'ogg']
    ];
    
    if (!in_array($extension, $allowedExtensions[$fileType] ?? [])) {
        throw new Exception('File type not allowed for ' . $fileType);
    }
    
    // Create upload directory
    $uploadDir = $fileType === 'audio' ? '../uploads/chat_audio/' : '../uploads/chat_files/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // Generate unique filename
    // Around line 65, after generating unique filename
    $uniqueName = uniqid() . '_' . time() . '.' . $extension;
    $uploadPath = $uploadDir . $uniqueName;
    
    // Add debugging
    error_log("upload_file.php: Generated filename: $uniqueName");
    error_log("upload_file.php: Upload path: $uploadPath");
    
    // Move uploaded file
    if (!move_uploaded_file($tmpPath, $uploadPath)) {
        throw new Exception('Failed to save uploaded file');
    }
    
    // Prepare file URL (relative to API base)
    $fileUrl = ($fileType === 'audio' ? 'uploads/chat_audio/' : 'uploads/chat_files/') . $uniqueName;
    
    // Add more debugging
    error_log("upload_file.php: Final file_url: $fileUrl");
    
    // Prepare file metadata
    $fileMetadata = [
        'original_name' => $originalName,
        'file_size' => $fileSize,
        'file_type' => $extension,
        'mime_type' => mime_content_type($uploadPath),
        'upload_time' => date('Y-m-d H:i:s')
    ];
    
    // Prepare file URL (relative to API base) - ALWAYS use relative path
    $fileUrl = ($fileType === 'audio' ? 'uploads/chat_audio/' : 'uploads/chat_files/') . $uniqueName;
    
    // Remove the duplicate line and ensure consistency
    error_log("upload_file.php: Final file_url: $fileUrl");
    
    echo json_encode([
        'success' => true,
        'message' => 'File uploaded successfully',
        'file_url' => $fileUrl,
        'file_metadata' => $fileMetadata
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    error_log("upload_file.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>