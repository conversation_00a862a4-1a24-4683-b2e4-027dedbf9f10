<?php
require_once '../config/db_connect.php';

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['user_id']) || !isset($input['enabled'])) {
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit();
}

$userId = intval($input['user_id']);
$enabled = $input['enabled'] ? 1 : 0;

try {
    $stmt = $conn->prepare("UPDATE users SET two_factor_enabled = ? WHERE id = ?");
    $stmt->bind_param("ii", $enabled, $userId);
    
    if ($stmt->execute()) {
        echo json_encode([
            'success' => true, 
            'message' => $enabled ? '2FA enabled successfully' : '2FA disabled successfully',
            'two_factor_enabled' => $enabled
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update 2FA status']);
    }
    
    $stmt->close();
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}

$conn->close();
?>