import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:hanapp/viewmodels/review_view_model.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:image_picker/image_picker.dart';
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/services/review_service.dart';
import 'package:hanapp/utils/auth_service.dart';
import 'package:hanapp/models/user_review.dart';
import 'package:hanapp/models/user.dart';
import 'dart:io';
import 'dart:convert';
import 'package:share_plus/share_plus.dart';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:path_provider/path_provider.dart';
import 'package:flutter/rendering.dart';
import 'package:hanapp/utils/user_service.dart';
import 'dart:math' show cos, sin;

class ReviewScreen extends StatefulWidget {
  final int reviewerId;
  final int reviewedUserId;
  final int? listingId;
  final String? listingTitle;
  final String mode; // 'write' or 'view'

  const ReviewScreen({
    super.key,
    required this.reviewerId,
    required this.reviewedUserId,
    this.listingId,
    this.listingTitle,
    this.mode = 'write',
  });

  @override
  State<ReviewScreen> createState() => _ReviewScreenState();
}

class _ReviewScreenState extends State<ReviewScreen> with WidgetsBindingObserver {
  double _rating = 0.0;
  final TextEditingController _commentController = TextEditingController();
  final List<XFile> _pickedImages = [];
  bool _saveToFavorites = false;
  bool _isSubmitting = false;
  bool _hasSubmittedReview = false;

  // For view/reply mode
  Review? _review;
  bool _isLoading = false;
  String? _errorMessage;
  User? _currentUser;
  User? _doerProfile; // Add this line
  final TextEditingController _replyController = TextEditingController();
  bool _isSubmittingReply = false;
  final ReviewService _reviewService = ReviewService();
  final UserService _userService = UserService(); // Add this line
  final GlobalKey _shareCardKey = GlobalKey();
  bool _isSharing = false;

  @override
  void initState() {
    super.initState();
    if (widget.mode == 'view') {
      _initializeViewMode();
    } else {
      // For write mode, also fetch doer profile
      _fetchDoerProfileForWriteMode();
      // Prevent system navigation until review is submitted
      _setSystemUIMode();
      // Add observer to handle app lifecycle
      WidgetsBinding.instance.addObserver(this);
    }
  }

  @override
  void dispose() {
    if (widget.mode == 'write') {
      // Restore normal system UI when leaving
      _restoreSystemUIMode();
      // Remove observer
      WidgetsBinding.instance.removeObserver(this);
    }
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    // Prevent app from being backgrounded if review not submitted
    if (widget.mode == 'write' && !_hasSubmittedReview) {
      if (state == AppLifecycleState.paused || 
          state == AppLifecycleState.inactive) {
        // Show a system alert that prevents backgrounding
        _showSystemAlert();
      }
    }
  }

  void _showSystemAlert() {
    if (!mounted) return;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => WillPopScope(
        onWillPop: () async => false,
        child: AlertDialog(
          title: const Text('Review Required'),
          content: const Text('Please complete your review before leaving the app.'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Force the app back to foreground
                SystemChrome.setEnabledSystemUIMode(
                  SystemUiMode.immersiveSticky,
                  overlays: [SystemUiOverlay.top],
                );
              },
              child: const Text('Continue Review'),
            ),
          ],
        ),
      ),
    );
  }

  void _setSystemUIMode() {
    // Hide system navigation and make it immersive
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.immersiveSticky,
      overlays: [SystemUiOverlay.top], // Keep status bar but hide navigation
    );
    
    // Also set system navigation bar color to make it less visible
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
    );
  }

  void _restoreSystemUIMode() {
    // Restore normal system UI
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.edgeToEdge,
      overlays: SystemUiOverlay.values,
    );
  }

  Future<void> _initializeViewMode() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    try {
      _currentUser = await AuthService.getUser();
      if (_currentUser == null) {
        throw Exception("Authentication error. Please log in.");
      }
      await _fetchReview();
      await _fetchDoerProfile(); // Add this line
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load data: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Add this method
  Future<void> _fetchDoerProfile() async {
    try {
      final response = await _userService.getUserProfile(widget.reviewedUserId);
      if (response['success']) {
        setState(() {
          _doerProfile = response['user'];
        });
      }
    } catch (e) {
      debugPrint('Error fetching doer profile: $e');
    }
  }

  // Add this method
  Future<void> _fetchDoerProfileForWriteMode() async {
    try {
      final response = await _userService.getUserProfile(widget.reviewedUserId);
      if (response['success']) {
        setState(() {
          _doerProfile = response['user'];
        });
      }
    } catch (e) {
      debugPrint('Error fetching doer profile for write mode: $e');
    }
  }

  Future<void> _fetchReview() async {
    try {
      final response = await _reviewService.getReviewForJob(
        listerId: widget.reviewerId,
        doerId: widget.reviewedUserId,
      );
      if (response['success']) {
        setState(() {
          _review = response['review'];
          if (_review?.doerReplyMessage != null && _review!.doerReplyMessage!.isNotEmpty) {
            _replyController.text = _review!.doerReplyMessage!;
          }
        });
      } else {
        setState(() {
          _errorMessage = response['message'] ?? 'Failed to load review.';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Network error fetching review: $e';
      });
    }
  }

  Future<void> _submitDoerReply() async {
    if (_review == null || _currentUser == null || _currentUser!.id == null) {
      _showSnackBar('Error: Review or user data missing to submit reply.', isError: true);
      return;
    }
    if (_replyController.text.trim().isEmpty) {
      _showSnackBar('Reply message cannot be empty.', isError: true);
      return;
    }
    if (_review!.doerReplyMessage != null && _review!.doerReplyMessage!.isNotEmpty) {
      _showSnackBar('You have already replied to this review.', isError: false);
      return;
    }
    setState(() {
      _isSubmittingReply = true;
    });
    try {
      final response = await _reviewService.submitDoerReply(
        reviewId: _review!.id,
        replyMessage: _replyController.text.trim(),
        doerId: _currentUser!.id!,
      );
      if (response['success']) {
        _showSnackBar(response['message'] ?? 'Reply submitted successfully!');
        setState(() {
          _review = _review!.copyWith(
            doerReplyMessage: _replyController.text.trim(),
            repliedAt: DateTime.now(),
          );
        });
      } else {
        _showSnackBar('Failed to submit reply: ${response['message']}', isError: true);
      }
    } catch (e) {
      _showSnackBar('Network error submitting reply: $e', isError: true);
    } finally {
      setState(() {
        _isSubmittingReply = false;
      });
    }
  }

  Future<void> _pickImages() async {
    final ImagePicker picker = ImagePicker();
    final List<XFile>? images = await picker.pickMultiImage();
    if (images != null && images.isNotEmpty) {
      setState(() {
        _pickedImages.addAll(images);
      });
    }
  }

  void _removeImage(int index) {
    setState(() {
      _pickedImages.removeAt(index);
    });
  }

  Future<Map<String, dynamic>> _submitReviewOnly() async {
    if (_rating == 0.0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please provide a star rating.')),
      );
      return {'success': false, 'message': 'Please provide a star rating.'};
    }
    setState(() { _isSubmitting = true; });
    final reviewViewModel = Provider.of<ReviewViewModel>(context, listen: false);
    // Convert image file paths to base64 strings
    final List<String> base64Images = [];
    for (final xfile in _pickedImages) {
      final bytes = await File(xfile.path).readAsBytes();
      final base64Str = base64Encode(bytes);
      base64Images.add('data:image/jpeg;base64,$base64Str');
    }
    final response = await reviewViewModel.submitReview(
      reviewerId: widget.reviewerId,
      reviewedUserId: widget.reviewedUserId,
      applicationId: widget.listingId,
      rating: _rating,
      comment: _commentController.text.trim(),
      imagePaths: base64Images,
      saveToFavorites: _saveToFavorites,
    );
    setState(() { _isSubmitting = false; });
    return response;
  }

  Future<void> _submitReview() async {
    final response = await _submitReviewOnly();
    if (response['success']) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(response['message'])),
      );
      Navigator.of(context).pop();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to submit review: ${response['message']}')),
      );
    }
  }

  Future<void> _handleSubmitReviewWithShareOption() async {
    final response = await _submitReviewOnly(); // Submit without navigation
    
    if (!response['success']) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to submit review: ${response['message']}')),
      );
      return;
    }
    
    // Mark review as submitted and restore system UI
    setState(() {
      _hasSubmittedReview = true;
    });
    
    // Restore normal system UI since review is submitted
    _restoreSystemUIMode();
    
    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(response['message'])),
    );
    
    // Now show the sharing dialog
    final shouldShare = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Share to your social media?'),
        content: const Text('Would you like to share this review to your Facebook story?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Yes'),
          ),
        ],
      ),
    );
    
    if (shouldShare == true) {
      final imageBytes = await _generateShareImage();
      if (imageBytes != null) {
        final tempDir = await getTemporaryDirectory();
        final file = await File('${tempDir.path}/review.png').writeAsBytes(imageBytes);
        await Share.shareXFiles([XFile(file.path)], text: 'Check out my review!');
      } else {
        _showSnackBar('Failed to generate share image.', isError: true);
      }
    }
    
    // Finally navigate back to chat screen
    Navigator.of(context).pop();
  }

  // Builds the review card widget for sharing
  Widget _buildShareableReviewCard() {
    return Material(
      color: Colors.white,
      child: Container(
        width: 400,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              widget.listingTitle ?? 'Project #${widget.listingId ?? ''}',
              style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold, color: Colors.black87),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                ...List.generate(5, (index) => Icon(
                  index < _rating ? Icons.star : Icons.star_border,
                  color: Colors.amber,
                  size: 24,
                )),
                const SizedBox(width: 8),
                Text('${_rating.toStringAsFixed(1)}/5.0', style: const TextStyle(fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _commentController.text.trim(),
              style: const TextStyle(fontSize: 16, color: Colors.black87),
            ),
            const SizedBox(height: 16),
            if (_pickedImages.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.photo_library, color: Colors.grey.shade600),
                    const SizedBox(width: 8),
                    Text(
                      '${_pickedImages.length} photo${_pickedImages.length > 1 ? 's' : ''} attached',
                      style: TextStyle(color: Colors.grey.shade600),
                    ),
                  ],
                ),
              ),
            if (_pickedImages.isNotEmpty) const SizedBox(height: 16),
            Text(
              'Reviewed on: ${_formatDate(DateTime.now())}',
              style: const TextStyle(fontSize: 13, color: Colors.grey),
            ),
            const SizedBox(height: 8),
            const Text('#Tapp #Reviews #ServiceQuality', style: TextStyle(fontSize: 13, color: Colors.blueGrey)),
          ],
        ),
      ),
    );
  }

  // Captures the review card as an image and shares it
  Future<void> _shareReviewCardAsImage() async {
    setState(() { _isSharing = true; });
    try {
      // Ensure the widget is built before capturing
      await Future.delayed(const Duration(milliseconds: 100));
      if (_shareCardKey.currentContext == null) {
        _showSnackBar('Unable to share: Review card is not ready.', isError: true);
        setState(() { _isSharing = false; });
        return;
      }
      final renderObject = _shareCardKey.currentContext!.findRenderObject();
      if (renderObject == null || renderObject is! RenderRepaintBoundary) {
        _showSnackBar('Unable to share: Review card is not ready.', isError: true);
        setState(() { _isSharing = false; });
        return;
      }
      RenderRepaintBoundary boundary = renderObject as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      Uint8List pngBytes = byteData!.buffer.asUint8List();
      final tempDir = await getTemporaryDirectory();
      final file = await File('${tempDir.path}/review_share_${DateTime.now().millisecondsSinceEpoch}.png').create();
      await file.writeAsBytes(pngBytes);
      await Share.shareXFiles([XFile(file.path)], text: 'Shared via Tapp');
    } catch (e) {
      _showSnackBar('Failed to share review image: $e', isError: true);
    } finally {
      setState(() { _isSharing = false; });
    }
  }

  Future<void> _shareToFacebookStory() async {
    setState(() { _isSharing = true; });
    if (_pickedImages.isNotEmpty) {
      // Wait for the widget to be built before capturing
      await WidgetsBinding.instance.endOfFrame;
      await _shareReviewCardAsImage();
    } else {
      final String shareText = '''
🌟 My Review on HanApp

Project: ${widget.listingTitle ?? 'Project #${widget.listingId ?? ''}'}
Rating: ${_rating.toStringAsFixed(1)}/5.0 ⭐
Review: ${_commentController.text.trim()}

Reviewed on: ${_formatDate(DateTime.now())}

#HanApp #Reviews #ServiceQuality
      '''.trim();
      await Share.share(shareText, subject: 'My Review on Tapp - ${widget.listingTitle ?? 'Project #${widget.listingId ?? ''}'}');
    }
    setState(() { _isSharing = false; });
    await _submitReview();
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        if (difference.inMinutes == 0) {
          return 'Just now';
        }
        return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
      }
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isError ? Icons.error_outline : Icons.check_circle_outline,
              color: Colors.white,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: isError ? Colors.red : Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(10),
      ),
    );
  }

  Future<Uint8List?> _generateShareImage() async {
    try {
      // Create a simple widget without overlay complications
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);
      
      // Define the card dimensions - make it taller to accommodate images
      const double cardWidth = 400;
      double cardHeight = 350; // Base height
      
      // Add extra height if there are images
      if (_pickedImages.isNotEmpty) {
        cardHeight += 120; // Extra space for images
      }
      
      // Draw background
      final paint = Paint()..color = Colors.white;
      canvas.drawRect(Rect.fromLTWH(0, 0, cardWidth, cardHeight), paint);
      
      // Draw content using TextPainter
      final titlePainter = TextPainter(
        text: TextSpan(
          text: widget.listingTitle ?? 'Project #${widget.listingId ?? ''}',
          style: const TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      titlePainter.layout(maxWidth: cardWidth - 48);
      titlePainter.paint(canvas, const Offset(24, 24));
      
      // Draw rating stars
      double starY = 24 + titlePainter.height + 16;
      for (int i = 0; i < 5; i++) {
        final starPaint = Paint()
          ..color = i < _rating ? Colors.amber : Colors.grey.shade300;
        _drawStar(canvas, Offset(24 + (i * 32), starY), 12, starPaint);
      }
      
      // Draw rating text
      final ratingPainter = TextPainter(
        text: TextSpan(
          text: '${_rating.toStringAsFixed(1)}/5.0',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      ratingPainter.layout();
      ratingPainter.paint(canvas, Offset(24 + (5 * 32) + 8, starY - 4));
      
      // Draw comment
      double commentY = starY + 32;
      final commentPainter = TextPainter(
        text: TextSpan(
          text: _commentController.text.trim(),
          style: const TextStyle(
            fontSize: 16,
            color: Colors.black87,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      commentPainter.layout(maxWidth: cardWidth - 48);
      commentPainter.paint(canvas, Offset(24, commentY));
      
      // Draw actual images if they exist
      double currentY = commentY + commentPainter.height + 16;
      if (_pickedImages.isNotEmpty) {
        // Load and draw images
        const double imageSize = 80;
        const double imageSpacing = 8;
        double imageX = 24;
        
        for (int i = 0; i < _pickedImages.length && i < 3; i++) {
          try {
            // Load image from file
            final imageFile = File(_pickedImages[i].path);
            final imageBytes = await imageFile.readAsBytes();
            final codec = await ui.instantiateImageCodec(imageBytes);
            final frame = await codec.getNextFrame();
            final image = frame.image;
            
            // Draw image with rounded corners effect
            final imagePaint = Paint();
            final imageRect = Rect.fromLTWH(imageX, currentY, imageSize, imageSize);
            
            // Create a rounded rectangle path
            final path = Path()
              ..addRRect(RRect.fromRectAndRadius(
                imageRect,
                const Radius.circular(8),
              ));
            
            canvas.save();
            canvas.clipPath(path);
            canvas.drawImageRect(
              image,
              Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble()),
              imageRect,
              imagePaint,
            );
            canvas.restore();
            
            imageX += imageSize + imageSpacing;
          } catch (e) {
            debugPrint('Error loading image ${i}: $e');
            // Draw placeholder if image fails to load
            final placeholderPaint = Paint()..color = Colors.grey.shade300;
            final placeholderRect = Rect.fromLTWH(imageX, currentY, imageSize, imageSize);
            canvas.drawRRect(
              RRect.fromRectAndRadius(placeholderRect, const Radius.circular(8)),
              placeholderPaint,
            );
            
            // Draw camera icon placeholder
            final iconPainter = TextPainter(
              text: const TextSpan(
                text: '📷',
                style: TextStyle(fontSize: 24),
              ),
              textDirection: TextDirection.ltr,
            );
            iconPainter.layout();
            iconPainter.paint(
              canvas,
              Offset(
                imageX + (imageSize - iconPainter.width) / 2,
                currentY + (imageSize - iconPainter.height) / 2,
              ),
            );
            
            imageX += imageSize + imageSpacing;
          }
        }
        
        currentY += imageSize + 16;
      }
      
      // Draw date and hashtags at bottom
      final datePainter = TextPainter(
        text: TextSpan(
          text: 'Reviewed on: ${_formatDate(DateTime.now())}',
          style: const TextStyle(
            fontSize: 13,
            color: Colors.grey,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      datePainter.layout();
      datePainter.paint(canvas, Offset(24, cardHeight - 60));
      
      final hashtagPainter = TextPainter(
        text: const TextSpan(
          text: '#Tapp #Reviews #ServiceQuality',
          style: TextStyle(
            fontSize: 13,
            color: Colors.blueGrey,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      hashtagPainter.layout();
      hashtagPainter.paint(canvas, Offset(24, cardHeight - 40));
      
      // Convert to image
      final picture = recorder.endRecording();
      final image = await picture.toImage(cardWidth.toInt(), cardHeight.toInt());
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      
      return byteData?.buffer.asUint8List();
    } catch (e) {
      debugPrint('Error generating share image: $e');
      return null;
    }
  }
  
  void _drawStar(Canvas canvas, Offset center, double radius, Paint paint) {
    const int points = 5;
    const double angle = (3.14159 * 2) / points;
    
    final path = Path();
    for (int i = 0; i < points; i++) {
      final x = center.dx + radius * cos(i * angle - 3.14159 / 2);
      final y = center.dy + radius * sin(i * angle - 3.14159 / 2);
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
      
      // Inner point
      final innerRadius = radius * 0.4;
      final innerX = center.dx + innerRadius * cos((i + 0.5) * angle - 3.14159 / 2);
      final innerY = center.dy + innerRadius * sin((i + 0.5) * angle - 3.14159 / 2);
      path.lineTo(innerX, innerY);
    }
    path.close();
    canvas.drawPath(path, paint);
  }

  Future<bool> _onWillPop() async {
    if (widget.mode == 'write' && !_hasSubmittedReview) {
      // Show dialog asking user to leave a review first
      final shouldExit = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: const Text('Please leave a review first'),
          content: const Text('You need to submit a review before leaving this page.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Stay'),
            ),
          ],
        ),
      );
      return shouldExit ?? false;
    }
    return true;
  }


  @override
  Widget build(BuildContext context) {
    if (widget.mode == 'write') {
      return WillPopScope(
        onWillPop: _onWillPop,
        child: Scaffold(
          appBar: AppBar(
            title: const Text('Leave a Review'),
            backgroundColor: Constants.primaryColor,
            foregroundColor: Colors.white,
            automaticallyImplyLeading: false, // Remove back button
          ),
        body: _isSubmitting
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Add doer profile section
                    if (_doerProfile != null) ...[
                      Card(
                        elevation: 2,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Row(
                            children: [
                              CircleAvatar(
                                radius: 30,
                                backgroundImage: _doerProfile!.profilePictureUrl != null && _doerProfile!.profilePictureUrl!.isNotEmpty
                                    ? NetworkImage(_doerProfile!.profilePictureUrl!)
                                    : null,
                                backgroundColor: Constants.primaryColor.withOpacity(0.1),
                                child: (_doerProfile!.profilePictureUrl == null || _doerProfile!.profilePictureUrl!.isEmpty)
                                    ? Icon(Icons.person, size: 30, color: Constants.primaryColor)
                                    : null,
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      _doerProfile!.fullName,
                                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                                    ),
                                    const SizedBox(height: 4),
                                    if (_doerProfile!.addressDetails != null && _doerProfile!.addressDetails!.isNotEmpty)
                                      Row(
                                        children: [
                                          Icon(Icons.location_on, size: 16, color: Colors.grey.shade600),
                                          const SizedBox(width: 4),
                                          Expanded(
                                            child: Text(
                                              _doerProfile!.addressDetails!,
                                              style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ],
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],
                    Text(
                      'How did it go? Please leave a review for ${_doerProfile?.fullName ?? widget.reviewedUserId.toString()} to exit this page',
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 24),
                    const Text(
                      'How would you rate it?',
                      style: TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    RatingBar.builder(
                      initialRating: _rating,
                      minRating: 1,
                      direction: Axis.horizontal,
                      allowHalfRating: false,
                      itemCount: 5,
                      itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
                      itemBuilder: (context, _) => const Icon(Icons.star, color: Colors.amber),
                      onRatingUpdate: (rating) => setState(() => _rating = rating),
                    ),
                    const SizedBox(height: 24),
                    const Text(
                      'What can you say?',
                      style: TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _commentController,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                        hintText: 'Write your review here...',
                      ),
                      maxLines: 4,
                    ),
                    const SizedBox(height: 24),
                    const Text(
                      'Upload Media (optional)',
                      style: TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: List.generate(3, (index) {
                        if (index < _pickedImages.length) {
                          return Padding(
                            padding: const EdgeInsets.only(right: 8.0),
                            child: Stack(
                              children: [
                                Image.file(File(_pickedImages[index].path), width: 100, height: 100, fit: BoxFit.cover),
                                Positioned(
                                  top: 0,
                                  right: 0,
                                  child: IconButton(
                                    icon: const Icon(Icons.remove_circle, color: Colors.red),
                                    onPressed: () => _removeImage(index),
                                  ),
                                ),
                              ],
                            ),
                          );
                        } else {
                          return Padding(
                            padding: const EdgeInsets.only(right: 8.0),
                            child: GestureDetector(
                              onTap: _pickImages,
                              child: Container(
                                width: 100,
                                height: 100,
                                color: Colors.grey.shade200,
                                child: const Icon(Icons.add_photo_alternate, size: 40, color: Colors.grey),
                              ),
                            ),
                          );
                        }
                      }),
                    ),
                    const SizedBox(height: 24),
                    Center(
                      child: ElevatedButton(
                        onPressed: _handleSubmitReviewWithShareOption,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Constants.primaryColor,
                          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                        ),
                        child: const Text('Leave Review', style: TextStyle(fontSize: 16, color: Colors.white)),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Center(
                      child: ElevatedButton(
                        onPressed: () => setState(() => _saveToFavorites = !_saveToFavorites),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.yellow,
                          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                        ),
                        child: Text(
                          _saveToFavorites ? 'Saved to Favorites' : 'Save to Favorites',
                          style: const TextStyle(fontSize: 16, color: Colors.black),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
        ),
      );
    }
    if (widget.mode == 'view') {
      if (_isLoading) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Review Details'),
            backgroundColor: Constants.primaryColor,
            foregroundColor: Colors.white,
          ),
          body: const Center(child: CircularProgressIndicator()),
        );
      }
      if (_errorMessage != null) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Review Details'),
            backgroundColor: Constants.primaryColor,
            foregroundColor: Colors.white,
          ),
          body: Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                _errorMessage!,
                textAlign: TextAlign.center,
                style: const TextStyle(color: Colors.red, fontSize: 16),
              ),
            ),
          ),
        );
      }
      if (_review == null) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Review Details'),
            backgroundColor: Constants.primaryColor,
            foregroundColor: Colors.white,
          ),
          body: const Center(child: Text('No review found.')),
        );
      }
      // Show review details and reply box if doer
      return Scaffold(
        appBar: AppBar(
          title: const Text('Review Details'),
          backgroundColor: Constants.primaryColor,
          foregroundColor: Colors.white,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Card with Lister Info and Rating
              Card(
                elevation: 4,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Lister Info Row
                      Row(
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: Constants.primaryColor.withOpacity(0.1),
                            child: Icon(
                              Icons.person,
                              size: 30,
                              color: Constants.primaryColor,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  _review!.listerFullName,
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: Constants.textColor,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Lister',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      
                      // Star Rating
                      Row(
                        children: [
                          Text(
                            'Rating: ',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey.shade700,
                            ),
                          ),
                          const SizedBox(width: 8),
                          ...List.generate(5, (index) {
                            return Icon(
                              index < _review!.rating ? Icons.star : Icons.star_border,
                              color: Colors.amber,
                              size: 24,
                            );
                          }),
                          const SizedBox(width: 8),
                          Text(
                            '${_review!.rating.toStringAsFixed(1)}/5.0',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // Review Date
                      Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            size: 16,
                            color: Colors.grey.shade600,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Reviewed on ${_formatDate(_review!.createdAt)}',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 20),
              
              // Review Content Card
              if (_review!.reviewContent.isNotEmpty)
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.rate_review,
                              color: Constants.primaryColor,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Review Comment',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Constants.textColor,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Text(
                          _review!.reviewContent,
                          style: const TextStyle(
                            fontSize: 16,
                            height: 1.5,
                            color: Constants.textColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              
              // Photos Section (if any)
              if (_review!.reviewImageUrls.isNotEmpty) ...[
                const SizedBox(height: 20),
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.photo_library,
                              color: Constants.primaryColor,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Photos',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Constants.textColor,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        // TODO: Display images from URLs
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.image,
                                color: Colors.grey.shade600,
                                size: 24,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '${_review!.reviewImageUrls.length} photo(s)',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
              
              const SizedBox(height: 20),
              
              // Reply Section
              if (_currentUser != null && _currentUser!.id == _review!.doerId)
                _review!.doerReplyMessage == null || _review!.doerReplyMessage!.isEmpty
                  ? Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.reply,
                                  color: Constants.primaryColor,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Reply to this review',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Constants.textColor,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            TextField(
                              controller: _replyController,
                              maxLines: 4,
                              decoration: InputDecoration(
                                hintText: 'Share your thoughts about this review...',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: BorderSide(color: Colors.grey.shade300),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: const BorderSide(color: Constants.primaryColor, width: 2),
                                ),
                                filled: true,
                                fillColor: Colors.grey.shade50,
                                contentPadding: const EdgeInsets.all(16),
                              ),
                            ),
                            const SizedBox(height: 16),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                onPressed: _isSubmittingReply ? null : _submitDoerReply,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Constants.primaryColor,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                                  elevation: 2,
                                ),
                                child: _isSubmittingReply
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                                      )
                                    : const Text(
                                        'Submit Reply',
                                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                                      ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  : Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.check_circle,
                                  color: Colors.green,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Your Reply',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Constants.textColor,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.green.shade50,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(color: Colors.green.shade200),
                              ),
                              child: Text(
                                _review!.doerReplyMessage!,
                                style: const TextStyle(
                                  fontSize: 16,
                                  height: 1.5,
                                  color: Constants.textColor,
                                ),
                              ),
                            ),
                            if (_review!.repliedAt != null) ...[
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Icon(
                                    Icons.access_time,
                                    size: 14,
                                    color: Colors.grey.shade600,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'Replied on ${_formatDate(_review!.repliedAt!)}',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
            ],
          ),
        ),
      );
    }
    // Default fallback to satisfy non-null return type
    return Container();
  }
}
