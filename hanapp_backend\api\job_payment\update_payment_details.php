<?php
// hanapp_backend/api/job_payment/update_payment_details.php
// Updates payment details (doer_fee, transaction_fee, total_amount) in listing tables

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../../config/db_connect.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(["success" => false, "message" => "Only POST requests are allowed."]);
    exit();
}

try {
    // Get and validate input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception("Invalid JSON payload");
    }
    
    $applicationId = intval($input['application_id'] ?? 0);
    $doerFee = floatval($input['doer_fee'] ?? 0);
    $transactionFee = floatval($input['transaction_fee'] ?? 0);
    $totalAmount = floatval($input['total_amount'] ?? 0);
    
    // Validate required fields
    if ($applicationId <= 0) {
        throw new Exception("Valid application ID is required");
    }
    
    if ($doerFee <= 0) {
        throw new Exception("Valid doer fee is required");
    }
    
    if ($totalAmount <= 0) {
        throw new Exception("Valid total amount is required");
    }
    
    error_log("update_payment_details.php: Starting payment details update for application ID: $applicationId");
    error_log("update_payment_details.php: Doer Fee: ₱$doerFee, Transaction Fee: ₱$transactionFee, Total Amount: ₱$totalAmount");
    
    // Start transaction
    $conn->autocommit(false);
    
    // Get application details to determine listing type and ID
    $query = "
        SELECT a.id, a.listing_id, a.listing_type, a.lister_id, a.doer_id, a.status,
               COALESCE(pl.title, al.title) as listing_title
        FROM applicationsv2 a
        LEFT JOIN listingsv2 pl ON a.listing_id = pl.id AND a.listing_type = 'PUBLIC'
        LEFT JOIN asap_listings al ON a.listing_id = al.id AND a.listing_type = 'ASAP'
        WHERE a.id = ?
    ";
    
    $stmt = $conn->prepare($query);
    if ($stmt === false) {
        throw new Exception("Failed to prepare application query: " . $conn->error);
    }
    
    $stmt->bind_param("i", $applicationId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception("Application not found");
    }
    
    $application = $result->fetch_assoc();
    $stmt->close();
    
    $listingId = $application['listing_id'];
    $listingType = $application['listing_type'];
    $listingTitle = $application['listing_title'];
    
    error_log("update_payment_details.php: Found application - Listing ID: $listingId, Type: $listingType, Title: $listingTitle");
    
    // Update payment details in the appropriate listing table
    if ($listingType === 'ASAP') {
        $updateQuery = "
            UPDATE asap_listings 
            SET doer_fee = ?, transaction_fee = ?, total_amount = ?, updated_at = NOW() 
            WHERE id = ?
        ";
        error_log("update_payment_details.php: Updating asap_listings table");
    } else if ($listingType === 'PUBLIC') {
        $updateQuery = "
            UPDATE listingsv2 
            SET doer_fee = ?, transaction_fee = ?, total_amount = ?, updated_at = NOW() 
            WHERE id = ?
        ";
        error_log("update_payment_details.php: Updating listingsv2 table");
    } else {
        throw new Exception("Invalid listing type: $listingType");
    }
    
    $updateStmt = $conn->prepare($updateQuery);
    if ($updateStmt === false) {
        throw new Exception("Failed to prepare update query: " . $conn->error);
    }
    
    $updateStmt->bind_param("dddi", $doerFee, $transactionFee, $totalAmount, $listingId);
    
    if (!$updateStmt->execute()) {
        throw new Exception("Failed to update payment details: " . $updateStmt->error);
    }
    
    if ($updateStmt->affected_rows === 0) {
        throw new Exception("No rows were updated. Listing may not exist or values may be unchanged.");
    }
    
    $updateStmt->close();
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    error_log("update_payment_details.php: Successfully updated payment details for listing ID: $listingId");
    
    echo json_encode([
        'success' => true,
        'message' => 'Payment details updated successfully',
        'application_id' => $applicationId,
        'listing_id' => $listingId,
        'listing_type' => $listingType,
        'listing_title' => $listingTitle,
        'doer_fee' => $doerFee,
        'transaction_fee' => $transactionFee,
        'total_amount' => $totalAmount
    ]);
    
} catch (Exception $e) {
    // Rollback transaction on error
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    error_log("update_payment_details.php: Error - " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
