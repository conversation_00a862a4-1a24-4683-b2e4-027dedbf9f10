<?php
// hanapp_backend/api/set_login_status.php
// Sets user login status after successful verification

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'config/db_connect.php';

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $input = file_get_contents("php://input");
    $data = json_decode($input, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("Invalid JSON payload.");
    }

    $userId = $data['user_id'] ?? null;

    if (empty($userId)) {
        echo json_encode([
            "success" => false, 
            "message" => "User ID is required."
        ]);
        exit();
    }

    // Set user as logged in
    $stmt = $conn->prepare("UPDATE users SET is_logged_in = 1 WHERE id = ?");
    
    if ($stmt === false) {
        throw new Exception("Failed to prepare database statement.");
    }

    $stmt->bind_param("i", $userId);
    $success = $stmt->execute();
    $stmt->close();

    if ($success) {
        echo json_encode([
            "success" => true,
            "message" => "Login status updated successfully."
        ]);
    } else {
        throw new Exception("Failed to update login status.");
    }

} catch (Exception $e) {
    http_response_code(500);
    error_log("set_login_status.php: Caught exception: " . $e->getMessage(), 0);
    echo json_encode([
        "success" => false,
        "message" => "An error occurred: " . $e->getMessage()
    ]);
} finally {
    if (isset($conn) && $conn instanceof mysqli && $conn->ping()) {
        $conn->close();
    }
}
?>