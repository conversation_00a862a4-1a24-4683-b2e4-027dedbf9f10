<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $applicationId = $input['application_id'] ?? null;
    $listerId = $input['lister_id'] ?? null;
    $doerFee = $input['doer_fee'] ?? null;
    $totalAmount = $input['total_amount'] ?? null;
    
    if (!$applicationId || !$listerId || !$doerFee || !$totalAmount) {
        throw new Exception('Missing required parameters');
    }
    
    // Start transaction
    $pdo->beginTransaction();
    
    // Get application details to find doer_id
    $stmt = $pdo->prepare("SELECT doer_id, listing_id FROM applications WHERE id = ?");
    $stmt->execute([$applicationId]);
    $application = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$application) {
        throw new Exception('Application not found');
    }
    
    $doerId = $application['doer_id'];
    $listingId = $application['listing_id'];
    
    // Check lister's HanApp Balance
    $stmt = $pdo->prepare("SELECT hanapp_balance FROM users WHERE id = ?");
    $stmt->execute([$listerId]);
    $lister = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$lister) {
        throw new Exception('Lister not found');
    }
    
    $currentBalance = floatval($lister['hanapp_balance']);
    
    if ($currentBalance < $totalAmount) {
        throw new Exception('Insufficient HanApp Balance. Current balance: ₱' . number_format($currentBalance, 2));
    }
    
    // Deduct total amount from lister's balance
    $newListerBalance = $currentBalance - $totalAmount;
    $stmt = $pdo->prepare("UPDATE users SET hanapp_balance = ? WHERE id = ?");
    $stmt->execute([$newListerBalance, $listerId]);
    
    // Add doer fee (not total amount) to doer's total_profit
    $stmt = $pdo->prepare("UPDATE users SET total_profit = total_profit + ? WHERE id = ?");
    $stmt->execute([$doerFee, $doerId]);
    
    // Update application status to completed
    $stmt = $pdo->prepare("UPDATE applications SET status = 'completed', completed_at = NOW() WHERE id = ?");
    $stmt->execute([$applicationId]);
    
    // Update listing status to completed
    $stmt = $pdo->prepare("UPDATE listings SET status = 'completed' WHERE id = ?");
    $stmt->execute([$listingId]);
    
    // Create single transaction record for the job payment
    $stmt = $pdo->prepare("
        INSERT INTO transactions (user_id, type, amount, description, transaction_date, method, status)
        VALUES (?, ?, ?, ?, NOW(), ?, ?)
    ");
    $stmt->execute([
        $listerId,
        'job_payment',
        $totalAmount,
        "Job payment for application #$applicationId - Lister: $listerId to Doer: $doerId (₱" . number_format($doerFee, 2) . " + ₱" . number_format($totalAmount - $doerFee, 2) . " fee)",
        'tapp_balance',
        'completed'
    ]);
    
    // Commit transaction
    $pdo->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'Payment processed successfully',
        'new_balance' => $newListerBalance,
        'doer_earning' => $doerFee
    ]);
    
} catch (Exception $e) {
    // Rollback transaction on error
    if ($pdo->inTransaction()) {
        $pdo->rollback();
    }
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
