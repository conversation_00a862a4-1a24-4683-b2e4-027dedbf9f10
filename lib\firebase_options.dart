// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
///import 'package:firebase_core/firebase_core.dart';
///import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
    /// case TargetPlatform.web:
    ///   return web;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
              'you can reconfigure this by running the FlutterFire CLI command `flutterfire configure`.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCV3PZd7rLbppacAnq6Q12R2kouALhY3u4',
    appId: '1:758079153152:android:508d83c7c6e672ae6a64e5',
    messagingSenderId: '758079153152',
    projectId: 'hanappproject',
    storageBucket: 'hanappproject.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCV3PZd7rLbppacAnq6Q12R2kouALhY3u4',
    appId: '1:758079153152:ios:508d83c7c6e672ae6a64e5',
    messagingSenderId: '758079153152',
    projectId: 'hanappproject',
    storageBucket: 'hanappproject.firebasestorage.app',
    iosClientId: '758079153152-ktuo5f615qoerei6jqlnabrotve9lno6.apps.googleusercontent.com',
    iosBundleId: 'com.example.hanapp',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCV3PZd7rLbppacAnq6Q12R2kouALhY3u4',
    appId: '1:758079153152:ios:508d83c7c6e672ae6a64e5',
    messagingSenderId: '758079153152',
    projectId: 'hanappproject',
    storageBucket: 'hanappproject.firebasestorage.app',
    iosClientId: '758079153152-ktuo5f615qoerei6jqlnabrotve9lno6.apps.googleusercontent.com',
    iosBundleId: 'com.example.hanapp',
  );

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCV3PZd7rLbppacAnq6Q12R2kouALhY3u4',
    appId: '1:758079153152:web:508d83c7c6e672ae6a64e5',
    messagingSenderId: '758079153152',
    projectId: 'hanappproject',
    authDomain: 'hanappproject.firebaseapp.com',
    storageBucket: 'hanappproject.firebasestorage.app',
    measurementId: 'G-MEASUREMENT_ID',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCV3PZd7rLbppacAnq6Q12R2kouALhY3u4',
    appId: '1:758079153152:web:508d83c7c6e672ae6a64e5',
    messagingSenderId: '758079153152',
    projectId: 'hanappproject',
    authDomain: 'hanappproject.firebaseapp.com',
    storageBucket: 'hanappproject.firebasestorage.app',
    measurementId: 'G-MEASUREMENT_ID',
  );

}