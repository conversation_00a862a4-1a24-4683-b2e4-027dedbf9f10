<?php
// hanapp_backend/api/asap_listings/create_asap_listing.php
// Handles the creation of new ASAP listings, inserting into the 'asap_listings' table.

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set timezone to Asia/Manila
date_default_timezone_set('Asia/Manila');

require_once '../../config/db_connect.php'; // Database connection

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(["success" => false, "message" => "Method not allowed"]);
    exit();
}

if (!isset($conn) || $conn->connect_error) {
    error_log("Database connection not established in create_asap_listing.php: " . $conn->connect_error);
    echo json_encode(["success" => false, "message" => "Database connection not established."]);
    exit();
}

// Set MySQL session timezone
$conn->query("SET time_zone = '+08:00'");

// Get JSON input data with better error handling
$input = file_get_contents("php://input");
error_log("ASAP Listing Raw Input: " . $input);

if (empty($input)) {
    echo json_encode(["success" => false, "message" => "No input data received"]);
    exit();
}

$data = json_decode($input, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    error_log("JSON decode error: " . json_last_error_msg());
    echo json_encode(["success" => false, "message" => "Invalid JSON data: " . json_last_error_msg()]);
    exit();
}

$listerId = $data['lister_id'] ?? null;
$title = $data['title'] ?? '';
$description = $data['description'] ?? '';
$price = $data['price'] ?? 0.0;
$latitude = $data['latitude'] ?? null;
$longitude = $data['longitude'] ?? null;
$locationAddress = $data['location_address'] ?? '';
$preferredDoerGender = $data['preferred_doer_gender'] ?? 'Any';
$picturesUrls = $data['pictures_urls'] ?? []; // Array of URLs
$paymentMethod = $data['payment_method'] ?? '';
$isActive = $data['is_active'] ?? true; // Default to true
$isOpenPrice = $data['is_open_price'] ?? false; // Default to false

// The 'price' from Flutter is actually the doer fee that the user entered
// In the database:
// - price: stores the original user input (doer fee)
// - doer_fee: same as price (the amount doer will receive)
// - transaction_fee: calculated service fee based on payment method
// - total_amount: doer_fee + service_fee (what lister pays)
$doerFee = $price; // User entered doer fee directly

// Extract card type for credit/debit card payments
$cardType = null;
if (isset($data['card_type'])) {
    $cardType = trim($data['card_type']);
    error_log("DEBUG - Found card_type in data: " . $cardType);
} elseif (isset($data['cardType'])) {
    $cardType = trim($data['cardType']);
    error_log("DEBUG - Found cardType in data: " . $cardType);
} else {
    error_log("DEBUG - No card type found in data. Available keys: " . implode(', ', array_keys($data)));
}

// Function to calculate transaction fee based on payment method
function calculateTransactionFee($doerFee, $paymentMethod, $cardType = null) {
    if (!$paymentMethod || $doerFee <= 0) {
        return 0.0;
    }

    $serviceFee = 0;
    $paymentMethodLower = strtolower(trim($paymentMethod));

    // Debug card type information
    error_log("DEBUG - Fee calculation: Payment method='$paymentMethod', lowercased='$paymentMethodLower', Card type='" . ($cardType ?? 'NULL') . "', Doer fee=$doerFee");

    switch ($paymentMethodLower) {
        case 'gcash':
            $serviceFee = ($doerFee * 0.023) + 20; // 2.3% + ₱20
            error_log("DEBUG - Using GCash fee: " . $serviceFee);
            break;
        case 'credit/debit card':
        case 'card': // Handle both variations
            // Check card type for different rates - use case-insensitive comparison
            $cardTypeLower = strtolower(trim($cardType ?? ''));
            error_log("DEBUG - Card type check: Original='$cardType', Lowercased='$cardTypeLower'");

            if ($cardTypeLower === 'international_php') {
                // International Cards (PHP): 4.2% + ₱30
                $serviceFee = ($doerFee * 0.042) + 30;
                error_log("DEBUG - Using International Card fee (4.2% + ₱30): " . $serviceFee);
            } else {
                // Philippine Cards (default): 3.2% + ₱30
                $serviceFee = ($doerFee * 0.032) + 30;
                error_log("DEBUG - Using Philippine Card fee (3.2% + ₱30): " . $serviceFee);
            }
            break;
        case 'philippine_card':
            // Philippine Cards: 3.2% + ₱30
            $serviceFee = ($doerFee * 0.032) + 30;
            error_log("DEBUG - Using Philippine Card fee (3.2% + ₱30): " . $serviceFee);
            break;
        case 'international_card':
            // International Cards: 4.2% + ₱30
            $serviceFee = ($doerFee * 0.042) + 30;
            error_log("DEBUG - Using International Card fee (4.2% + ₱30): " . $serviceFee);
            break;
        case 'bpi':
        case 'bdo':
        case 'metrobank':
        case 'unionbank':
        case 'rcbc':
        case 'chinabank':
        case 'bank transfer':
            $serviceFee = ($doerFee * 0.01) + 20; // 1% + ₱20
            error_log("DEBUG - Using Bank Transfer fee (1% + ₱20): " . $serviceFee);
            break;
        case 'hanapp balance':
        case 'hanapp_balance':
        case 'tapp balance':
        case 'tapp_balance':
            $serviceFee = 0; // No fee for TAPP Balance
            error_log("DEBUG - Using HanApp Balance fee (no fee): " . $serviceFee);
            break;
        default:
            $serviceFee = 0;
            error_log("DEBUG - Unknown payment method '$paymentMethod', using default fee: " . $serviceFee);
            break;
    }

    error_log("DEBUG - Final calculated fee: " . $serviceFee);
    return $serviceFee;
}

// No fee calculation during listing creation - fees will be calculated at payment time (like public listings)
$transactionFee = 0; // Will be calculated when payment method is selected
$totalAmount = $doerFee; // For now, just the doer fee

// Debug logging
error_log("ASAP Listing Creation Debug:");
error_log("- User input (price): $price");
error_log("- Doer fee: $doerFee");
error_log("- Payment method: " . ($paymentMethod ?: 'Not provided - will be selected at payment time'));
error_log("- Transaction fee: $transactionFee (will be calculated at payment time)");
error_log("- Total amount: $totalAmount");

// Basic validation - Modified to handle open price listings and remove payment_method requirement
if (empty($listerId) || empty($title) || empty($locationAddress) || !is_numeric($latitude) || !is_numeric($longitude)) {
    echo json_encode(["success" => false, "message" => "Required fields are missing or invalid (lister_id, title, location_address, latitude, longitude)."]);
    exit();
}

// Additional validation for price based on listing type
if (!$isOpenPrice && (empty($price) || $price <= 0)) {
    echo json_encode(["success" => false, "message" => "Price is required and must be greater than 0 for fixed price listings."]);
    exit();
}

if ($isOpenPrice && $price != 0) {
    echo json_encode(["success" => false, "message" => "Price must be 0 for open price listings."]);
    exit();
}

// Convert pictures_urls array to JSON string
$picturesUrlsJson = json_encode($picturesUrls);
if ($picturesUrlsJson === false) {
    error_log("Failed to encode pictures_urls to JSON.");
    echo json_encode(["success" => false, "message" => "Error processing image URLs."]);
    exit();
}

// Remove this line as we've moved it up
// $isOpenPrice = $data['is_open_price'] ?? false; // Default to false

// Update the INSERT statement around line 165 to include is_open_price
$stmt = $conn->prepare("
    INSERT INTO asap_listings (
        lister_id, title, description, price, latitude, longitude,
        location_address, preferred_doer_gender, pictures_urls,
        doer_fee, transaction_fee, total_amount, payment_method, is_active, is_open_price
    )
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
");

// Update the bind_param call around line 175
$stmt->bind_param(
    "issdddsssdddsis",  // Added 's' for is_open_price
    $listerId,           // i - integer
    $title,              // s - string
    $description,        // s - string
    $price,              // d - double
    $latitude,           // d - double
    $longitude,          // d - double
    $locationAddress,    // s - string
    $preferredDoerGender,// s - string
    $picturesUrlsJson,   // s - string
    $doerFee,            // d - double
    $transactionFee,     // d - double
    $totalAmount,        // d - double
    $paymentMethod,      // s - string
    $isActive,           // i - integer
    $isOpenPrice         // s - boolean (stored as tinyint)
);

if ($stmt === false) {
    error_log("Failed to prepare ASAP listing insert statement: " . $conn->error);
    echo json_encode(["success" => false, "message" => "Internal server error."]);
    exit();
}

// Remove this entire duplicate bind_param block (lines 204-218)
// $stmt->bind_param(
//     "issdddsssdddsi",
//     $listerId,           // i - integer
//     $title,              // s - string  
//     $description,        // s - string
//     $price,              // d - double
//     $latitude,           // d - double
//     $longitude,          // d - double
//     $locationAddress,    // s - string
//     $preferredDoerGender,// s - string
//     $picturesUrlsJson,   // s - string
//     $doerFee,            // d - double
//     $transactionFee,     // d - double
//     $totalAmount,        // d - double
//     $paymentMethod,      // s - string
//     $isActive            // i - integer
// );

if ($stmt->execute()) {
    // After successful listing insertion
    $listingId = $stmt->insert_id;
    $stmt->close();
    
    error_log("=== ASAP LISTING NOTIFICATION DEBUG START ===");
    error_log("Listing ID: $listingId");
    error_log("Lister ID: $listerId");
    error_log("Title: $title");
    error_log("Location: $locationAddress");
    error_log("Latitude: $latitude");
    error_log("Longitude: $longitude");
    
    // Fetch lister's name for notification
    $listerQuery = "SELECT CONCAT(first_name, ' ', last_name) AS lister_name FROM users WHERE id = ?";
    $listerStmt = $conn->prepare($listerQuery);
    $listerStmt->bind_param("i", $listerId);
    $listerStmt->execute();
    $listerResult = $listerStmt->get_result();
    if ($listerRow = $listerResult->fetch_assoc()) {
        $listerName = $listerRow['lister_name'];
        error_log("Lister Name: $listerName");
    } else {
        $listerName = 'Unknown Lister';
        error_log("Failed to fetch lister name for ID $listerId");
    }
    $listerStmt->close();
    
    // Define notification details
    $notificationType = 'asap_listing_available';
    $notificationTitle = "New ASAP Listing: $title";
    $notificationContent = "A new ASAP listing is available near you at $locationAddress. Fee: ₱$doerFee. Tap to view details.";
    $listingTitle = $title; // Use the listing's title
    
    // Find nearby available doers within 10km, excluding the lister if they are a doer
    $doerQuery = "
        SELECT id, latitude AS doer_lat, longitude AS doer_lon,
               CONCAT(first_name, ' ', last_name) AS doer_name
        FROM users u 
        WHERE u.role = 'doer' 
        AND u.is_available = 1
        AND u.id != ?  -- Exclude lister if they are a doer
        AND u.latitude IS NOT NULL
        AND u.longitude IS NOT NULL
        AND (
            6371 * acos(
                cos(radians(?)) * cos(radians(u.latitude)) * 
                cos(radians(u.longitude) - radians(?)) + 
                sin(radians(?)) * sin(radians(u.latitude))
            )
        ) <= 10
    ";
    
    $doerStmt = $conn->prepare($doerQuery);
    if ($doerStmt) {
        $doerStmt->bind_param("iddd", $listerId, $latitude, $longitude, $latitude);
        $doerStmt->execute();
        $doerResult = $doerStmt->get_result();
        
        $nearbyDoersCount = $doerResult->num_rows;
        error_log("Nearby available doers found: $nearbyDoersCount");
        
        if ($nearbyDoersCount > 0) {
            // Prepare insert statement
            $insertSql = "INSERT INTO doer_notifications (
                user_id, sender_id, type, title, content, associated_id,
                related_listing_title, listing_id, listing_type, lister_id,
                lister_name, is_read, created_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?,
                ?, ?, 'asap', ?,
                ?, 0, NOW()
            )";
            
            $insertStmt = $conn->prepare($insertSql);
            if ($insertStmt) {
                while ($doer = $doerResult->fetch_assoc()) {
                    $doerId = $doer['id'];
                    $doerName = $doer['doer_name'];
                    error_log("Inserting notification for doer $doerId ($doerName) at ({$doer['doer_lat']}, {$doer['doer_lon']})");
                    
                    $insertStmt->bind_param(
                        "iissssiiss",  // Corrected type string (10 parameters)
                        $doerId,        // i
                        $listerId,      // i
                        $notificationType, // s
                        $notificationTitle, // s
                        $notificationContent, // s (was incorrectly 'i' before)
                        $listingId,     // s
                        $listingTitle,  // s
                        $listingId,     // i
                        $listerId,      // s
                        $listerName     // s
                    );
                    
                    if (!$insertStmt->execute()) {
                        error_log("Failed to insert notification for doer $doerId: " . $insertStmt->error);
                    } else {
                        error_log("Successfully inserted notification for doer $doerId");
                    }
                }
                $insertStmt->close();
            } else {
                error_log("Failed to prepare insert statement: " . $conn->error);
            }
        } else {
            error_log("No nearby doers found to notify.");
        }
        $doerStmt->close();
    } else {
        error_log("Failed to prepare doer query: " . $conn->error);
    }

    error_log("=== ASAP LISTING NOTIFICATION DEBUG END ===");

    echo json_encode([
        "success" => true,
        "message" => "ASAP Listing created successfully!",
        "listing_id" => $listingId
    ]);
} else {
    error_log("Error executing ASAP listing insert statement: " . $stmt->error);
    echo json_encode(["success" => false, "message" => "Failed to create ASAP listing. Please try again."]);
}

$conn->close();
?>