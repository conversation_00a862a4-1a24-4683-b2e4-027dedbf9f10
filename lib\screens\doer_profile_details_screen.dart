import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:hanapp/models/user.dart';
import 'package:hanapp/models/review.dart';
import 'package:hanapp/utils/auth_service.dart';
import 'package:hanapp/services/review_service.dart';
import 'package:intl/intl.dart';
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/services/user_service.dart';
import 'package:hanapp/utils/image_utils.dart';
// Add these imports for chat functionality
import 'package:hanapp/services/chat_service.dart';
import 'package:hanapp/screens/chat_screen.dart';

class DoerProfileDetailsScreen extends StatefulWidget {
  final int doerId;
  final int? listingId; // Optional: if we need listing context for future features

  const DoerProfileDetailsScreen({
    super.key,
    required this.doerId,
    this.listingId,
  });

  @override
  State<DoerProfileDetailsScreen> createState() => _DoerProfileDetailsScreenState();
}

class _DoerProfileDetailsScreenState extends State<DoerProfileDetailsScreen> {
  User? _doerProfile;
  User? _currentUser;
  List<Review> _reviews = [];
  bool _isLoading = true;
  String? _errorMessage;
  final ReviewService _reviewService = ReviewService();
  final UserService _userService = UserService();
  final ChatService _chatService = ChatService(); // Add ChatService instance

  @override
  void initState() {
    super.initState();
    _fetchDoerDetails();
    _loadCurrentUser(); // Add current user loading
  }

  Future<void> _loadCurrentUser() async {
    try {
      _currentUser = await AuthService.getUser();
    } catch (e) {
      debugPrint('Error loading current user: $e');
    }
  }

  Future<void> _fetchDoerDetails() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Use the same method as view_profile_screen.dart
      final userResponse = await _userService.getUserProfile(widget.doerId);
      if (userResponse['success']) {
        _doerProfile = userResponse['user'];
      } else {
        _errorMessage = userResponse['message'] ?? 'Failed to load doer profile.';
        _doerProfile = null;
      }

      // Fetch Reviews for Doer (keep existing logic)
      final reviewsResponse = await _reviewService.getReviewsForUser(userId: widget.doerId);
      if (reviewsResponse['success'] && reviewsResponse['reviews'] != null) {
        _reviews = reviewsResponse['reviews'].cast<Review>();
      } else {
        _reviews = [];
        print('Failed to load reviews: ${reviewsResponse['message']}');
      }
    } catch (e) {
      debugPrint('Error fetching profile or reviews: $e');
      _errorMessage = 'Network error loading profile: ${e.toString()}';
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Widget _buildStarRating(double rating) {
    List<Widget> stars = [];
    for (int i = 0; i < 5; i++) {
      if (i < rating.floor()) {
        stars.add(const Icon(Icons.star, color: Colors.amber, size: 20));
      } else if (i < rating && rating - i > 0) {
        stars.add(const Icon(Icons.star_half, color: Colors.amber, size: 20));
      } else {
        stars.add(const Icon(Icons.star_border, color: Colors.amber, size: 20));
      }
    }
    return Row(children: stars);
  }

  // Add this method for chat functionality
  Future<void> _startChat(int targetUserId, String targetUserFullName) async {
    if (_currentUser == null) {
      _showSnackBar('Error: Current user not logged in.', isError: true);
      return;
    }
  
    // Prevent self-chatting
    if (_currentUser!.id == targetUserId) {
      _showSnackBar('You cannot chat with yourself.', isError: true);
      return;
    }
  
    setState(() {
      _isLoading = true;
    });
  
    try {
      // For direct profile chats with a doer:
      // - targetUserId is always the doer (since this is doer profile screen)
      // - currentUser is the lister who wants to chat with the doer
      final response = await _chatService.createOrGetConversation(
        listingId: widget.listingId ?? 0, // Use provided listingId or 0 for direct chat
        listingType: widget.listingId != null ? 'PUBLIC' : 'DIRECT', // Use appropriate type
        listerId: _currentUser!.id!, // Current user is the lister
        doerId: targetUserId, // Target user is the doer
      );
  
      if (response['success']) {
        final int conversationId = response['conversation_id'];
        
        _showSnackBar('Chat initiated!');
        if (mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => ChatScreen(
                conversationId: conversationId,
                otherUserId: targetUserId,
                listingTitle: widget.listingId != null 
                    ? 'Chat about listing' 
                    : 'Direct Chat with ${targetUserFullName}',
                applicationId: 0,
                isLister: true, // Current user is always the lister when viewing doer profile
              ),
            ),
          );
        }
      } else {
        _showSnackBar('Failed to start chat: ${response['message']}', isError: true);
      }
    } catch (e) {
      _showSnackBar('Error starting chat: ${e.toString()}', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            message,
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: isError ? Colors.red : Colors.green,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          margin: const EdgeInsets.all(10),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Doer Profile'),
        backgroundColor: Constants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
          ? Center(
        child: Padding(
          padding: Constants.screenPadding,
          child: Text(
            'Error: $_errorMessage',
            textAlign: TextAlign.center,
            style: const TextStyle(color: Colors.red, fontSize: 16),
          ),
        ),
      )
          : SingleChildScrollView(
        padding: Constants.screenPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Doer Profile Section
            Center(
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 60,
                    backgroundImage: ImageUtils.createProfileImageProvider(_doerProfile?.profilePictureUrl),
                    child: (_doerProfile?.profilePictureUrl == null || _doerProfile!.profilePictureUrl!.isEmpty)
                        ? const Icon(Icons.person, size: 60, color: Colors.grey)
                        : null,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _doerProfile?.fullName ?? 'Unknown Doer',
                    style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _doerProfile?.addressDetails ?? 'Address not available',
                    style: TextStyle(fontSize: 16, color: Colors.grey.shade700),
                  ),
                  const SizedBox(height: 8),
                  // Assuming 'Started on' refers to when the doer joined or started their first job
                  // This data is not typically in User model, so using a placeholder or
                  // you might need to add a 'member_since' field to your User model.
                  Text(
                    'Started on: N/A', // Placeholder
                    style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Flexible(
                        child: Text(
                          _doerProfile?.averageRating?.toStringAsFixed(1) ?? 'N/A',
                          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Flexible(
                        child: _buildStarRating(_doerProfile?.averageRating ?? 0.0),
                      ),
                      const SizedBox(width: 4),
                      Flexible(
                        child: Text(
                          '(${_doerProfile?.reviewCount ?? 0})',
                          style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
            const Text(
              'Reviews',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _reviews.isEmpty
                ? const Center(
              child: Text(
                'No reviews yet.',
                style: TextStyle(fontSize: 16, color: Colors.grey),
              ),
            )
                : ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(), // Disable scrolling for inner list
              itemCount: _reviews.length,
              itemBuilder: (context, index) {
                final review = _reviews[index];
                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 8.0),
                  elevation: 1,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            CircleAvatar(
                              radius: 20,
                              backgroundImage: review.reviewerProfilePictureUrl != null && review.reviewerProfilePictureUrl!.isNotEmpty
                                  ? CachedNetworkImageProvider(review.reviewerProfilePictureUrl!) as ImageProvider<Object>?
                                  : null,
                              child: (review.reviewerProfilePictureUrl == null || review.reviewerProfilePictureUrl!.isEmpty)
                                  ? const Icon(Icons.person, size: 25)
                                  : null,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    review.reviewerFullName,
                                    style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                                  ),
                                  _buildStarRating(review.rating.toDouble()),
                                ],
                              ),
                            ),
                            Text(
                              DateFormat('MMM d, yyyy').format(review.createdAt),
                              style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                            ),
                          ],
                        ),
                        if (review.comment != null && review.comment!.isNotEmpty) ...[
                          const SizedBox(height: 8),
                          Text(
                            review.comment!,
                            style: const TextStyle(fontSize: 14),
                          ),
                        ],
                      ],
                    ),
                  ),
                );
              },
            ),
            // Remove the Connect button section
            // if (_currentUser != null && _currentUser!.id != widget.doerId) ...[
            //   const SizedBox(height: 24),
            //   SizedBox(
            //     width: double.infinity,
            //     child: ElevatedButton(
            //       onPressed: () {
            //         _startChat(
            //           widget.doerId,
            //           _doerProfile?.fullName ?? 'Doer',
            //         );
            //       },
            //       style: ElevatedButton.styleFrom(
            //         backgroundColor: Constants.primaryColor,
            //         foregroundColor: Colors.white,
            //         padding: const EdgeInsets.symmetric(vertical: 16),
            //         shape: RoundedRectangleBorder(
            //           borderRadius: BorderRadius.circular(8),
            //         ),
            //       ),
            //       child: const Text(
            //         'Connect',
            //         style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            //       ),
            //     ),
            //   ),
            // ],
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}
