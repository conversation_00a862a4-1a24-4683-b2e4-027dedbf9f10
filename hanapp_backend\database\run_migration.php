<?php
// hanapp_backend/database/run_migration.php
// <PERSON>ript to run database migrations for job payment tables

require_once '../api/config/db_connect.php';

if (!isset($conn) || $conn->connect_error) {
    die("Database connection failed: " . $conn->connect_error);
}

echo "Running job payment tables migration...\n";

// Read the migration SQL file
$migrationFile = __DIR__ . '/migrations/create_job_payment_tables.sql';
if (!file_exists($migrationFile)) {
    die("Migration file not found: $migrationFile\n");
}

$sql = file_get_contents($migrationFile);
if ($sql === false) {
    die("Failed to read migration file\n");
}

// Split SQL into individual statements
$statements = array_filter(array_map('trim', explode(';', $sql)));

$successCount = 0;
$errorCount = 0;

foreach ($statements as $statement) {
    if (empty($statement) || strpos($statement, '--') === 0) {
        continue; // Skip empty statements and comments
    }
    
    echo "Executing: " . substr($statement, 0, 50) . "...\n";
    
    if ($conn->query($statement)) {
        echo "✓ Success\n";
        $successCount++;
    } else {
        echo "✗ Error: " . $conn->error . "\n";
        $errorCount++;
    }
}

echo "\nMigration completed!\n";
echo "Successful statements: $successCount\n";
echo "Failed statements: $errorCount\n";

// Verify tables were created
echo "\nVerifying tables...\n";

$tables = ['job_payment_transactions', 'platform_revenue'];
foreach ($tables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result && $result->num_rows > 0) {
        echo "✓ Table '$table' exists\n";
    } else {
        echo "✗ Table '$table' not found\n";
    }
}

// Check if columns were added to applicationsv2
$result = $conn->query("DESCRIBE applicationsv2");
$columns = [];
while ($row = $result->fetch_assoc()) {
    $columns[] = $row['Field'];
}

$requiredColumns = ['payment_confirmed', 'payment_confirmed_at'];
foreach ($requiredColumns as $column) {
    if (in_array($column, $columns)) {
        echo "✓ Column '$column' exists in applicationsv2\n";
    } else {
        echo "✗ Column '$column' not found in applicationsv2\n";
    }
}

$conn->close();
echo "\nMigration script completed.\n";
?>
