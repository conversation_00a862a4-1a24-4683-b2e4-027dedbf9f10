<?php
// hanapp_backend/database/create_login_tables.php
// <PERSON>ript to create login notification related tables

require_once '../api/config/db_connect.php';

if (!isset($conn) || $conn->connect_error) {
    die("Database connection failed: " . $conn->connect_error);
}

echo "Creating login notification tables...\n";

// Read the SQL file
$sqlFile = __DIR__ . '/create_login_notifications_table.sql';
if (!file_exists($sqlFile)) {
    die("SQL file not found: $sqlFile\n");
}

$sql = file_get_contents($sqlFile);
if ($sql === false) {
    die("Failed to read SQL file\n");
}

// Split SQL into individual statements
$statements = array_filter(array_map('trim', explode(';', $sql)));

$successCount = 0;
$errorCount = 0;

foreach ($statements as $statement) {
    if (empty($statement) || strpos($statement, '--') === 0) {
        continue; // Skip empty statements and comments
    }
    
    echo "Executing: " . substr($statement, 0, 50) . "...\n";
    
    if ($conn->query($statement)) {
        echo "✓ Success\n";
        $successCount++;
    } else {
        echo "✗ Error: " . $conn->error . "\n";
        $errorCount++;
    }
}

echo "\nTable creation completed!\n";
echo "Successful statements: $successCount\n";
echo "Failed statements: $errorCount\n";

// Verify tables were created
echo "\nVerifying tables...\n";

$tables = ['login_notifications', 'security_incidents', 'password_reset_tokens'];
foreach ($tables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result && $result->num_rows > 0) {
        echo "✓ Table '$table' exists\n";
        
        // Show table structure
        $desc = $conn->query("DESCRIBE $table");
        echo "  Columns: ";
        $columns = [];
        while ($row = $desc->fetch_assoc()) {
            $columns[] = $row['Field'];
        }
        echo implode(', ', $columns) . "\n";
    } else {
        echo "✗ Table '$table' not found\n";
    }
}

$conn->close();
echo "\nTable creation script completed.\n";
?>