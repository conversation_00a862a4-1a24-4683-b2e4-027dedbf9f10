<?php
// hanapp_backend/api/listings/test_image_urls.php
// Test script to check image URLs in database

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../../config/db_connect.php';

try {
    $listingId = $_GET['listing_id'] ?? null;
    
    if (!$listingId) {
        echo json_encode(['success' => false, 'message' => 'listing_id parameter required']);
        exit();
    }
    
    // Get ASAP listing images
    $stmt = $conn->prepare("SELECT id, title, pictures_urls FROM asap_listings WHERE id = ?");
    $stmt->bind_param("i", $listingId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $listing = $result->fetch_assoc();
        $picturesUrls = json_decode($listing['pictures_urls'], true) ?? [];
        
        $imageTests = [];
        foreach ($picturesUrls as $url) {
            $fullUrl1 = "https://autosell.io/$url";
            $fullUrl2 = "https://autosell.io/api/$url";
            
            // Test if files exist
            $headers1 = @get_headers($fullUrl1);
            $headers2 = @get_headers($fullUrl2);
            
            $imageTests[] = [
                'stored_url' => $url,
                'test_url_1' => $fullUrl1,
                'test_url_1_exists' => $headers1 && strpos($headers1[0], '200') !== false,
                'test_url_2' => $fullUrl2,
                'test_url_2_exists' => $headers2 && strpos($headers2[0], '200') !== false,
            ];
        }
        
        echo json_encode([
            'success' => true,
            'listing_id' => $listingId,
            'listing_title' => $listing['title'],
            'pictures_urls_raw' => $listing['pictures_urls'],
            'pictures_urls_parsed' => $picturesUrls,
            'image_tests' => $imageTests,
            'upload_directory_info' => [
                'uploads_exists' => file_exists('../../uploads/listing_images/'),
                'uploads_path' => realpath('../../uploads/listing_images/'),
                'uploads_contents' => file_exists('../../uploads/listing_images/') ? scandir('../../uploads/listing_images/') : 'Directory not found'
            ]
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Listing not found']);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}
?>
