import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:hanapp/utils/api_config.dart';

class JobPaymentService {
  static String get _baseUrl => ApiConfig.baseUrl;

  /// Creates a job payment invoice with proper fee handling
  Future<Map<String, dynamic>> createJobPayment({
    required int listerId,
    required int applicationId,
    required double doerFee,
    double transactionFee = 25.0, // Default ₱25 platform fee
    required String paymentMethod,
    String? listerEmail,
    String? listerFullName,
  }) async {
    final url = Uri.parse('$_baseUrl/api/wallet/create_xendit_job_payment.php');
    debugPrint('JobPaymentService: Creating job payment for application $applicationId, doer fee: ₱$doerFee, platform fee: ₱$transactionFee');

    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'lister_id': listerId,
          'application_id': applicationId,
          'doer_fee': doerFee,
          'transaction_fee': transactionFee,
          'total_amount': doerFee + transactionFee,
          'payment_method': paymentMethod,
          'lister_email': listerEmail,
          'lister_full_name': listerFullName,
        }),
      );

      final responseData = json.decode(response.body);
      debugPrint('JobPaymentService: Create Job Payment Response: $responseData');

      if (response.statusCode == 200 && responseData['success']) {
        return {
          'success': true,
          'transaction_id': responseData['transaction_id'],
          'invoice_id': responseData['invoice_id'],
          'invoice_url': responseData['invoice_url'],
          'external_id': responseData['external_id'],
          'amount': responseData['amount'],
          'payment_method': responseData['payment_method'],
          'expires_at': responseData['expires_at'],
          'message': responseData['message']
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'Failed to create job payment.'
        };
      }
    } catch (e) {
      debugPrint('JobPaymentService: Error creating job payment: $e');
      return {
        'success': false,
        'message': 'Network error: $e'
      };
    }
  }

  /// Gets job payment transaction history for a user
  Future<Map<String, dynamic>> getJobPaymentHistory({
    required int userId,
    String userType = 'lister', // 'lister' or 'doer'
  }) async {
    final url = Uri.parse('$_baseUrl/wallet/get_job_payment_history.php?user_id=$userId&user_type=$userType');
    debugPrint('JobPaymentService: Fetching job payment history for user $userId as $userType');

    try {
      final response = await http.get(url);
      final responseData = json.decode(response.body);

      debugPrint('JobPaymentService: Job Payment History Response: $responseData');

      if (response.statusCode == 200 && responseData['success']) {
        return {
          'success': true,
          'transactions': responseData['transactions'] ?? [],
          'total_paid': responseData['total_paid'] ?? 0.0,
          'total_received': responseData['total_received'] ?? 0.0,
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'Failed to fetch job payment history.'
        };
      }
    } catch (e) {
      debugPrint('JobPaymentService: Error fetching job payment history: $e');
      return {
        'success': false,
        'message': 'Network error: $e'
      };
    }
  }

  /// Gets platform revenue statistics (admin only)
  Future<Map<String, dynamic>> getPlatformRevenue({
    String? startDate,
    String? endDate,
  }) async {
    final url = Uri.parse('$_baseUrl/admin/get_platform_revenue.php');
    debugPrint('JobPaymentService: Fetching platform revenue statistics');

    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'start_date': startDate,
          'end_date': endDate,
        }),
      );

      final responseData = json.decode(response.body);
      debugPrint('JobPaymentService: Platform Revenue Response: $responseData');

      if (response.statusCode == 200 && responseData['success']) {
        return {
          'success': true,
          'total_revenue': responseData['total_revenue'] ?? 0.0,
          'job_payment_fees': responseData['job_payment_fees'] ?? 0.0,
          'transaction_count': responseData['transaction_count'] ?? 0,
          'revenue_breakdown': responseData['revenue_breakdown'] ?? [],
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'Failed to fetch platform revenue.'
        };
      }
    } catch (e) {
      debugPrint('JobPaymentService: Error fetching platform revenue: $e');
      return {
        'success': false,
        'message': 'Network error: $e'
      };
    }
  }

  /// Validates job payment status
  Future<Map<String, dynamic>> validateJobPayment({
    required int applicationId,
    required int listerId,
  }) async {
    final url = Uri.parse('$_baseUrl/wallet/validate_job_payment.php');
    debugPrint('JobPaymentService: Validating job payment for application $applicationId');

    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'application_id': applicationId,
          'lister_id': listerId,
        }),
      );

      final responseData = json.decode(response.body);
      debugPrint('JobPaymentService: Validate Job Payment Response: $responseData');

      if (response.statusCode == 200 && responseData['success']) {
        return {
          'success': true,
          'is_paid': responseData['is_paid'] ?? false,
          'payment_details': responseData['payment_details'],
          'message': responseData['message']
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'Failed to validate job payment.'
        };
      }
    } catch (e) {
      debugPrint('JobPaymentService: Error validating job payment: $e');
      return {
        'success': false,
        'message': 'Network error: $e'
      };
    }
  }

  /// Calculate total amount including platform fee
  static double calculateTotalAmount(double doerFee, {double transactionFee = 25.0}) {
    return doerFee + transactionFee;
  }

  /// Process TAPP Balance payment for job
  Future<Map<String, dynamic>> processHanAppBalancePayment({
    required int listerId,
    required int applicationId,
    required double doerFee,
    double transactionFee = 25.0,
    required double totalAmount,
  }) async {
    final url = Uri.parse('$_baseUrl/api/wallet/step_by_step_with_payment.php');
    debugPrint('JobPaymentService: Processing TAPP Balance payment for application $applicationId, doer fee: ₱$doerFee, platform fee: ₱$transactionFee');

    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'lister_id': listerId,
          'application_id': applicationId,
          'doer_fee': doerFee,
          'transaction_fee': transactionFee,
          'total_amount': totalAmount,
        }),
      );

      final responseData = json.decode(response.body);
      debugPrint('JobPaymentService: TAPP Balance Payment Response: $responseData');

      if (response.statusCode == 200 && responseData['success']) {
        return {
          'success': true,
          'transaction_id': responseData['transaction_id'],
          'new_lister_balance': responseData['new_lister_balance'],
          'message': responseData['message']
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'Failed to process TAPP Balance payment.'
        };
      }
    } catch (e) {
      debugPrint('JobPaymentService: Error processing Tapp Balance payment: $e');
      return {
        'success': false,
        'message': 'Network error: $e'
      };
    }
  }

  /// Calculate platform fee based on doer fee (currently fixed at ₱25)
  static double calculatePlatformFee(double doerFee) {
    return 25.0; // Fixed ₱25 platform fee
  }

  /// Format currency for display
  static String formatCurrency(double amount) {
    return '₱${amount.toStringAsFixed(2)}';
  }

  /// Get payment method display name
  static String getPaymentMethodName(String method) {
    switch (method) {
      case 'gcash':
        return 'GCash E-Wallet';
      case 'paymaya':
        return 'Maya E-Wallet';
      case 'bank_transfer':
        return 'Bank Transfer';
      case 'card':
        return 'Credit/Debit Card';
      default:
        return method;
    }
  }
}
