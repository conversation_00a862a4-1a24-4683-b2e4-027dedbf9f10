import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:hanapp/utils/api_config.dart';

class CashInService {
  /// Create a cash-in invoice via backend
  Future<Map<String, dynamic>> createCashInInvoice({
    required int userId,
    required double amount,
    required String paymentMethod,
    String? cardType, // Optional card type for credit cards
    required String userEmail,
    required String userFullName,
  }) async {
    try {
      print('CashInService: Creating cash-in invoice for user $userId, amount ₱$amount');

      final response = await http.post(
        Uri.parse(ApiConfig.cashInEndpoint),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'user_id': userId,
          'amount': amount,
          'payment_method': paymentMethod,
          'card_type': cardType, // Include card type for credit cards
          'user_email': userEmail,
          'user_full_name': userFullName,
        }),
      );

      print('CashInService: Response status: ${response.statusCode}');
      print('CashInService: Response body: ${response.body}');

      final responseData = json.decode(response.body);

      if (response.statusCode == 200 && responseData['success']) {
        return {
          'success': true,
          'payment_details': responseData['payment_details'],
          'message': responseData['message'],
        };
      } else {
        return {
          'success': false,
          'message': responseData['message'] ?? 'Failed to create cash-in invoice',
          'error_code': responseData['error_code'],
        };
      }
    } catch (e) {
      print('CashInService: Exception occurred: $e');
      return {
        'success': false,
        'message': 'Network error: ${e.toString()}',
        'error_code': 'NETWORK_ERROR',
      };
    }
  }

  /// Check transaction status (optional - for manual status checking)
  Future<Map<String, dynamic>> checkTransactionStatus({
    required String invoiceId,
  }) async {
    try {
      // This would call a backend endpoint to check Xendit invoice status
      // For now, we'll return a placeholder response
      return {
        'success': true,
        'status': 'pending',
        'message': 'Transaction status check not implemented yet',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to check transaction status: ${e.toString()}',
      };
    }
  }

  /// Validate cash-in amount
  static bool isValidAmount(double amount) {
    return amount >= 500 && amount <= 50000;
  }

  /// Get minimum cash-in amount
  static double get minimumAmount => 500.0;

  /// Get maximum cash-in amount
  static double get maximumAmount => 50000.0;

  /// Format amount for display
  static String formatAmount(double amount) {
    return '₱${amount.toStringAsFixed(2)}';
  }

  /// Get payment method display name
  static String getPaymentMethodDisplayName(String method) {
    switch (method.toLowerCase()) {
      case 'gcash':
        return 'GCash';
      case 'paymaya':
        return 'PayMaya';
      case 'bank_transfer':
        return 'Bank Transfer';
      case 'card':
        return 'Credit/Debit Card';
      case 'bpi':
        return 'BPI';
      case 'bdo':
        return 'BDO';
      case 'metrobank':
        return 'Metrobank';
      case 'unionbank':
        return 'UnionBank';
      case 'security_bank':
        return 'Security Bank';
      case 'rcbc':
        return 'RCBC';
      default:
        return method.toUpperCase();
    }
  }
}
