<?php
// hanapp_backend/api/wallet/create_xendit_job_payment.php
// Create Xendit invoice for job payments (GCash, GrabPay, Maya, Card, Bank Transfer)

ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

ob_start();

require_once '../../config/db_connect.php';

header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ob_clean();
    http_response_code(405);
    echo json_encode(["success" => false, "message" => "Method not allowed"]);
    exit();
}

try {
    // Get and validate input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception("Invalid JSON payload");
    }
    
    $listerId = intval($input['lister_id'] ?? 0);
    $applicationId = intval($input['application_id'] ?? 0);
    $doerFee = floatval($input['doer_fee'] ?? 0);
    $transactionFee = floatval($input['transaction_fee'] ?? 25.0);
    $totalAmount = floatval($input['total_amount'] ?? 0);
    $paymentMethod = $input['payment_method'] ?? '';
    $listerEmail = $input['lister_email'] ?? '';
    $listerFullName = $input['lister_full_name'] ?? '';
    
    // Validate required fields
    if ($listerId <= 0) throw new Exception("Invalid lister ID");
    if ($applicationId <= 0) throw new Exception("Invalid application ID");
    if ($doerFee <= 0) throw new Exception("Invalid doer fee");
    if ($totalAmount <= 0) throw new Exception("Invalid total amount");
    if (empty($paymentMethod)) throw new Exception("Payment method is required");
    
    // Validate payment method
    $allowedMethods = ['gcash', 'grabpay', 'maya', 'credit_card', 'debit_card', 'bank_transfer'];
    if (!in_array($paymentMethod, $allowedMethods)) {
        throw new Exception("Invalid payment method: $paymentMethod");
    }
    
    // Validate calculation
    $expectedTotal = $doerFee + $transactionFee;
    if (abs($totalAmount - $expectedTotal) > 0.01) {
        throw new Exception("Amount mismatch: Expected ₱" . number_format($expectedTotal, 2) . ", got ₱" . number_format($totalAmount, 2));
    }
    
    // Get lister and application details
    $stmt = $conn->prepare("
        SELECT 
            u.id, u.full_name, u.email,
            a.id as app_id, a.doer_id, a.status,
            COALESCE(pl.title, al.title) as listing_title,
            d.full_name as doer_name
        FROM users u
        JOIN applicationsv2 a ON u.id = a.lister_id
        LEFT JOIN listingsv2 pl ON a.listing_id = pl.id AND a.listing_type = 'PUBLIC'
        LEFT JOIN asap_listings al ON a.listing_id = al.id AND a.listing_type = 'ASAP'
        JOIN users d ON a.doer_id = d.id
        WHERE u.id = ? AND a.id = ? AND a.status = 'in_progress'
    ");
    
    $stmt->bind_param("ii", $listerId, $applicationId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception("Application not found, not owned by lister, or not in progress");
    }
    
    $data = $result->fetch_assoc();
    $stmt->close();
    
    // Use database info if not provided
    if (empty($listerEmail)) $listerEmail = $data['email'];
    if (empty($listerFullName)) $listerFullName = $data['full_name'];
    
    $listingTitle = $data['listing_title'];
    $doerName = $data['doer_name'];
    $doerId = $data['doer_id'];
    
    // Xendit configuration
    $xenditSecretKey = 'xnd_production_k5NqlGpmZlTPGEvBlYrk7a9ukwr8b2DzfQtEh3YThOcZazymwOlXwFT5ZEHIZm2';
    $xenditBaseUrl = 'https://api.xendit.co';
    
    // Generate unique external ID
    $timestamp = time();
    $externalId = "hanapp_jobpay_{$listerId}_{$applicationId}_{$timestamp}";
    
    // Map payment methods to Xendit channels
    $paymentChannels = [];
    switch ($paymentMethod) {
        case 'gcash':
            $paymentChannels = ['GCASH'];
            $methodDisplay = 'GCash';
            break;
        case 'grabpay':
            $paymentChannels = ['GRABPAY'];
            $methodDisplay = 'GrabPay';
            break;
        case 'maya':
            $paymentChannels = ['PAYMAYA'];
            $methodDisplay = 'Maya';
            break;
        case 'credit_card':
        case 'debit_card':
            $paymentChannels = ['CREDIT_CARD'];
            $methodDisplay = 'Credit/Debit Card';
            break;
        case 'bank_transfer':
            $paymentChannels = ['BPI', 'BDO', 'RCBC', 'CHINABANK', 'INSTAPAY', 'PESONET'];
            $methodDisplay = 'Bank Transfer';
            break;
    }
    
    // Create Xendit invoice
    $invoiceData = [
        'external_id' => $externalId,
        'amount' => $totalAmount,
        'description' => "Job Payment: {$listingTitle} (₱" . number_format($doerFee, 2) . " + ₱" . number_format($transactionFee, 2) . " fee) via {$methodDisplay}",
        'invoice_duration' => 86400, // 24 hours
        'currency' => 'PHP',
        'customer' => [
            'given_names' => $listerFullName,
            'email' => $listerEmail
        ],
        'payment_methods' => $paymentChannels,
        'success_redirect_url' => 'https://autosell.io/payment-success',
        'failure_redirect_url' => 'https://autosell.io/payment-failed',
        'metadata' => [
            'lister_id' => $listerId,
            'application_id' => $applicationId,
            'doer_id' => $doerId,
            'doer_fee' => $doerFee,
            'transaction_fee' => $transactionFee,
            'payment_type' => 'job_payment',
            'listing_title' => $listingTitle,
            'doer_name' => $doerName
        ]
    ];
    
    // Make API call to Xendit
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $xenditBaseUrl . '/v2/invoices');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($invoiceData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Basic ' . base64_encode($xenditSecretKey . ':')
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode !== 200) {
        throw new Exception("Xendit API error: " . $response);
    }
    
    $invoiceResponse = json_decode($response, true);
    
    if (!$invoiceResponse || !isset($invoiceResponse['id'])) {
        throw new Exception("Invalid Xendit response");
    }
    
    // Store transaction in database
    $stmt = $conn->prepare("
        INSERT INTO job_payment_transactions (
            user_id, application_id, doer_id, doer_fee, transaction_fee, total_amount,
            status, payment_method, description, xendit_invoice_id, xendit_external_id
        ) VALUES (?, ?, ?, ?, ?, ?, 'pending', ?, ?, ?, ?)
    ");
    
    $description = "Job payment for '{$listingTitle}' to {$doerName} via {$methodDisplay}";
    
    $stmt->bind_param(
        "iiidddssss",
        $listerId, $applicationId, $doerId, $doerFee, $transactionFee, $totalAmount,
        $paymentMethod, $description, $invoiceResponse['id'], $externalId
    );
    
    if (!$stmt->execute()) {
        throw new Exception("Failed to store transaction: " . $stmt->error);
    }
    
    $transactionId = $conn->insert_id;
    $stmt->close();
    
    // Return success response
    ob_clean();
    echo json_encode([
        "success" => true,
        "message" => "Payment invoice created successfully",
        "transaction_id" => $transactionId,
        "invoice_id" => $invoiceResponse['id'],
        "invoice_url" => $invoiceResponse['invoice_url'],
        "external_id" => $externalId,
        "amount" => $totalAmount,
        "payment_method" => $methodDisplay,
        "expires_at" => $invoiceResponse['expiry_date']
    ]);
    
} catch (Exception $e) {
    ob_clean();
    http_response_code(400);
    echo json_encode([
        "success" => false,
        "message" => $e->getMessage(),
        "error_type" => "job_payment_creation_error"
    ]);
}

if (isset($conn)) {
    $conn->close();
}
?>
