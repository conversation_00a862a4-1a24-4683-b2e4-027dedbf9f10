import 'package:flutter/material.dart';
import 'package:hanapp/models/conversation.dart';
import 'package:hanapp/models/user.dart';
import 'package:hanapp/utils/auth_service.dart';
import 'package:hanapp/utils/listing_service.dart';
import 'package:hanapp/services/chat_service.dart'; // ADD: Import ChatService

class ConversationsViewModel extends ChangeNotifier {
  final ListingService _listingService = ListingService();
  final ChatService _chatService = ChatService(); // ADD: Create ChatService instance
  User? _currentUser;
  List<Conversation> _conversations = [];
  bool _isLoading = false;
  String? _errorMessage;

  User? get currentUser => _currentUser;
  List<Conversation> get conversations => _conversations;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  Future<void> fetchConversations() async {
    if (_currentUser == null) {
      _errorMessage = 'User not logged in.';
      notifyListeners();
      return;
    }

    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    // FIXED: Use _chatService instance and correct response handling
    final response = await _chatService.getConversationsForUser(
      userId: _currentUser!.id!,
      currentRole: _currentUser!.role!, // Pass current role
    );

    if (response['success']) {
      // FIXED: Handle the response correctly - it returns ConversationPreview objects
      _conversations = (response['conversations'] as List)
          .map<Conversation>((conversationData) {
            // Convert ConversationPreview to Conversation
            if (conversationData is Map<String, dynamic>) {
              return Conversation.fromJson(conversationData, _currentUser!.id!);
            } else {
              // If it's already a ConversationPreview object, convert it
              return Conversation(
                conversationId: conversationData.conversationId,
                listingId: conversationData.listingId,
                listingType: conversationData.listingType,
                listerId: conversationData.listerId,
                doerId: conversationData.doerId,
                applicationId: conversationData.applicationId,
                listingTitle: conversationData.listingTitle,
              );
            }
          })
          .toList();
    } else {
      _errorMessage = response['message'] ?? 'Failed to load conversations.';
    }
    _isLoading = false;
    notifyListeners();
  }
  
  // Keep the filtering method for potential future use
  List<Conversation> _filterConversationsByRole(List<Conversation> conversations, String currentRole) {
    return conversations.where((conversation) {
      if (currentRole == 'lister') {
        return conversation.listerId == _currentUser!.id;
      } else {
        return conversation.doerId == _currentUser!.id;
      }
    }).toList();
  }
}